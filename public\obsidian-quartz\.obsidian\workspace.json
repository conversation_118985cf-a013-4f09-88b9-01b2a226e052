{"main": {"id": "34ed48dade05a336", "type": "split", "children": [{"id": "a8fcd3b9f6ad8b81", "type": "tabs", "children": [{"id": "9e7e3935dd209c64", "type": "leaf", "state": {"type": "markdown", "state": {"file": "content/Rainbow Gatherings and Radical Christianity.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Rainbow Gatherings and Radical Christianity"}}, {"id": "ade3e07597fe4be7", "type": "leaf", "state": {"type": "markdown", "state": {"file": "content/Untitled.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Untitled"}}, {"id": "d50f175a1e4b954e", "type": "leaf", "state": {"type": "markdown", "state": {"file": "content/Untitled.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Untitled"}}, {"id": "3d070dbafa777893", "type": "leaf", "state": {"type": "markdown", "state": {"file": "Site changes.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Site changes"}}, {"id": "05cd0217113f8ef4", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}], "currentTab": 4}], "direction": "vertical"}, "left": {"id": "4036a82dddf209cb", "type": "split", "children": [{"id": "0b2607ccdc5686e6", "type": "tabs", "children": [{"id": "933f46e87182aefd", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "225f2d1c63aa7d23", "type": "leaf", "state": {"type": "search", "state": {"query": "wonderful", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "62b3926c58756e42", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 338.5}, "right": {"id": "10673cb66139e182", "type": "split", "children": [{"id": "72803710758f1f5e", "type": "tabs", "children": [{"id": "2ec70085debaa449", "type": "leaf", "state": {"type": "backlink", "state": {"file": "content/images.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for images"}}, {"id": "9a3f53ff23da177f", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "content/images.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from images"}}, {"id": "e13f9948df9976a8", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "57a7f573390b2537", "type": "leaf", "state": {"type": "outline", "state": {"file": "content/images.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of images"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"bases:Create new base": false, "switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false, "obsidian-excalidraw-plugin:New drawing": false}}, "active": "05cd0217113f8ef4", "lastOpenFiles": ["content/kids-content/teacher-student-instructions.php", "content/kids-content/teacher-student-instructions.md", "content/inspiration/i-wish-you-enough.md", "content/safeguarding/help-during-panic-attack.php", "content/safeguarding/help-during-panic-attack.md", "content/inspiration/rats-happiness.md", "content/inspiration/forty-rules-love.md", "content/climate/collective-migrations.php", "content/climate/collective-migrations.md", "readme/choose_your_own_adventure_structure.md", "readme/choose-adventure.md", "readme/content_analysis.md", "content/humor/boomers-generational-wealth.md", "Using Cannabis to treat a trauma-based condition.md", "readme/QUICK_START.txt", "readme/CATEGORY_CREATION_README.md", "readme/CHEATSHEET.md", "readme/complete-site-guide.md", "readme/DOCUMENTATION_INDEX.md", "readme/FILE_STRUCTURE_GUIDE.md", "readme/IMAGE_REFERENCE_GUIDE.md", "readme/GETTING_STARTED_AFTER_BREAK.md", "readme", "complete-site-guide.md", "content/ptsd-myth/Using Cannabis to treat a trauma-based condition.md", "content/kids-content/yaffa-fatima.md", "content/kids-content/frogs-candle-people-dinosaurs.md", "content/kids-content/the-watcher.md", "content/kids-content/Kid Affirmations.md", "content/kids-content/books-queer-authors.md", "content/kids-content", "generate-category-indexes.php", "content/climate/index.php", "content/writings/you-are-the-one.php", "content/writings/what-you-read.php", "content/writings/the-wrong-turn.php", "img/KickitOver1.jpg", "img/store/breadknife.jpg", "img/store/colorboard.jpg", "img/store/games.png", "img/store/stuffy-invasion.jpg", "img/store/20250909_181349.jpg", "img/edutainment/culoclean.jpg", "img/self/bucket-toilet.jpg", "img/self/dbt workbook.jpg", "img/humor/sir-triplea.jpg"]}