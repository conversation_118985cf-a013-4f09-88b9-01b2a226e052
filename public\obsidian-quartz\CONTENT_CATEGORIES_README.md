# Content Categories Management Guide

This guide explains how to manually add, modify, and manage content categories on the A. A. Chips website.

## Overview

Content categories are organized sections that group related posts together. Each category appears in:
1. **Browse Categories** section on the main page
2. **Content Categories** sidebar on all pages
3. **Category index pages** that list all posts in that category

## Current Categories

As of the latest update, the following categories are available:

- **Access Technology** - Accessibility, digital inclusion, and assistive technology
- **Alienation** - Family alienation and recovery content
- **Climate** - Environmental and climate action content
- **Humor** - Funny stories and observations
- **Inspiration** - Uplifting content and motivational pieces
- **Journal** - Personal reflections and daily thoughts
- **Judaism** - Jewish identity and spiritual reflections
- **Kids Content** - Educational and entertaining content for children
- **Apple Chip Kitchen** - Food, recipes, and kitchen philosophy
- **Music** - Musical content, playlists, and audio experiences
- **PTSD Mythology** - Mental health advocacy and PTSD awareness
- **Safeguarding** - Safety, protection, and wellbeing resources
- **Street Advocacy** - Homelessness advocacy and social justice
- **Writings** - Essays, articles, and written works

## Adding a New Category

### Step 1: Create the Category Directory

1. Create a new directory under `content/` with your category name:
   ```
   content/your-category-name/
   ```
   Use lowercase with hyphens for spaces (e.g., `access-tech`, `kids-content`)

### Step 2: Add Posts to the Category

Place your `.md` and corresponding `.php` files in the category directory.

### Step 3: Create Category Index Page

Create an `index.php` file in your category directory. Use this template:

```php
<?php
// Category index for [Category Name]
// Load path helper and configuration
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Your Category Name';
$meta_description = 'Browse all posts in the Your Category Name category';
$meta_keywords = 'your-category-name, posts, A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$related_posts = [];

// Scan for posts in this category
$category_posts = [];
$categoryDir = __DIR__;
$mdFiles = glob($categoryDir . '/*.md');

foreach ($mdFiles as $mdFile) {
    $filename = basename($mdFile, '.md');
    
    // Skip index.md if it exists
    if ($filename === 'index') {
        continue;
    }
    
    // Check if corresponding PHP file exists
    $phpFile = $categoryDir . '/' . $filename . '.php';
    if (file_exists($phpFile)) {
        // Parse markdown file for metadata
        $content = file_get_contents($mdFile);
        $title = $filename;
        $excerpt = '';
        
        // Extract title from first line if it's a heading
        if (preg_match('/^#\s+(.+)$/m', $content, $matches)) {
            $title = trim($matches[1]);
        } else {
            // Convert filename to readable title
            $title = ucwords(str_replace(['-', '_'], ' ', $filename));
        }
        
        // Extract excerpt from content (first paragraph)
        $lines = explode("\n", $content);
        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line) && !preg_match('/^#/', $line) && !preg_match('/^---/', $line)) {
                $excerpt = substr($line, 0, 150);
                if (strlen($line) > 150) {
                    $excerpt .= '...';
                }
                break;
            }
        }
        
        $category_posts[] = [
            'title' => $title,
            'url' => $filename . '.php',
            'excerpt' => $excerpt ?: 'Description for your category.',
            'date' => date('Y-m-d', filemtime($mdFile)),
            'author' => 'A. A. Chips',
            'tags' => ['your-tag']
        ];
    }
}

// Sort posts by date (newest first)
usort($category_posts, function($a, $b) {
    return strcmp($b['date'], $a['date']);
});

// Generate content
ob_start();
?>
<div class="category-index">
    <header class="category-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
        <p class="category-description">Description of your category</p>
    </header>

    <div class="post-grid">
        <?php foreach ($category_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars($post['excerpt']); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author']): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo htmlspecialchars($post['date']); ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>
```

### Step 4: Register the Category

Add your category to the configuration in `config.php`:

```php
'categories' => [
    // ... existing categories ...
    'Your Category Name' => 'content/your-category-name/index.php',
],
```

### Step 5: Update Static Files

Update `index.html` in two places:

#### A. Browse Categories Section
Add to the categories grid:
```html
<a href="content/your-category-name/index.php" class="category-card">
    <h3>Your Category Name</h3>
    <p>Brief description of the category.</p>
</a>
```

#### B. Sidebar Categories
Add to the categories list:
```html
<li>
    <a href="content/your-category-name/index.php" class="internal-link">Your Category Name</a>
</li>
```

### Step 6: Update Page Template

The page template (`page template.htm`) should automatically pick up new categories from the config, but verify that your category appears in the Browse Categories section.

## Modifying Existing Categories

### Changing Category Name
1. Update the name in `config.php`
2. Update the name in `index.html` (both places)
3. Update the `$page_title` in the category's `index.php`

### Changing Category Description
1. Update the description in `index.html` (Browse Categories section)
2. Update the category description in the category's `index.php`
3. Update the description in `page template.htm` if using the dynamic categories array

### Moving Posts Between Categories
1. Move the `.md` and `.php` files to the new category directory
2. The category index pages will automatically update to reflect the changes

## Removing a Category

1. Remove the category from `config.php`
2. Remove the category from `index.html` (both places)
3. Remove or move the category directory and its contents
4. Update any hardcoded references in `page template.htm`

## Troubleshooting

### Category Not Appearing
- Check that the category is added to `config.php`
- Verify the path in the config matches the actual directory structure
- Ensure `index.php` exists in the category directory

### Posts Not Showing in Category Index
- Verify both `.md` and `.php` files exist for each post
- Check that the category `index.php` is scanning the correct directory
- Ensure file permissions allow reading the files

### Styling Issues
- Category cards use the `.category-card` CSS class
- Post grids use the `.post-grid` and `.post-card` CSS classes
- Check `css/style.css` for styling definitions

## Best Practices

1. **Consistent Naming**: Use lowercase with hyphens for directory names
2. **Clear Descriptions**: Write concise, descriptive category descriptions
3. **Logical Organization**: Group related content together
4. **Regular Maintenance**: Periodically review and reorganize categories as content grows
5. **Test Changes**: Always test category pages after making changes

## Files to Update When Adding Categories

1. `config.php` - Add to categories array
2. `content/[category-name]/index.php` - Create category index page
3. `index.html` - Add to Browse Categories and sidebar
4. `page template.htm` - Should auto-update if using dynamic categories

This system provides a flexible way to organize and display content while maintaining consistency across the site.
