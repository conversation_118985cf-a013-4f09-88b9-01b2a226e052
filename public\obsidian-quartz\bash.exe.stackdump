Stack trace:
Frame         Function      Args
0007FFFF9D30  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8C30) msys-2.0.dll+0x2118E
0007FFFF9D30  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9D30  0002100469F2 (00021028DF99, 0007FFFF9BE8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9D30  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9D30  00021006A545 (0007FFFF9D40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9D40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAC68A0000 ntdll.dll
7FFAC5D90000 KERNEL32.DLL
7FFAC3AC0000 KERNELBASE.dll
7FFAC62D0000 USER32.dll
7FFAC3F70000 win32u.dll
7FFAC5180000 GDI32.dll
7FFAC4320000 gdi32full.dll
7FFAC3EC0000 msvcp_win.dll
000210040000 msys-2.0.dll
7FFAC3FA0000 ucrtbase.dll
7FFAC50C0000 advapi32.dll
7FFAC5FF0000 msvcrt.dll
7FFAC4670000 sechost.dll
7FFAC4830000 RPCRT4.dll
7FFAC3050000 CRYPTBASE.DLL
7FFAC4450000 bcryptPrimitives.dll
7FFAC5FB0000 IMM32.DLL
