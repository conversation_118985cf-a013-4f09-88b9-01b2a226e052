<?php
/**
 * Comments Display Component
 * For A. A. Chips' Obsidian-Quartz Comments System
 */

// Session should already be started by page template
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/simple-auth.php';

function renderCommentsSection($postSlug) {
    try {
        $db = CommentDatabase::getInstance();
        $config = $db->getConfig();
        $currentUser = SimpleAuth::getCurrentUser();
        $commentCount = $db->getCommentCount($postSlug);

        // Get the base path for proper URL construction
        global $paths;
        $basePath = $paths['base_path'] ?? '';

        // If base path is empty, try to determine it from the current request
        if (empty($basePath)) {
            $requestUri = $_SERVER['REQUEST_URI'] ?? '';
            $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';

            // Count directory levels to determine relative path
            $pathParts = explode('/', trim(dirname($scriptName), '/'));
            $depth = count(array_filter($pathParts));

            if ($depth > 1) {
                $basePath = str_repeat('../', $depth - 1);
            } else {
                $basePath = './';
            }
        }

        // Debug output
        error_log("Rendering comments for post: $postSlug");

        ob_start();
        ?>
        <section class="comments-section" id="comments">
            <div class="comments-header">
                <h3>Comments (<span id="comment-count"><?php echo $commentCount; ?></span>)</h3>
            </div>

            <div class="comment-form-container">
                <?php if ($currentUser): ?>
                    <div class="user-info">
                        <span class="user-name"><?php echo htmlspecialchars($currentUser['name']); ?></span>
                        <span class="user-email">(<?php echo htmlspecialchars($currentUser['email']); ?>)</span>
                        <button type="button" id="change-user" class="btn btn-link">Change</button>
                    </div>
                <?php endif; ?>

                <form id="comment-form" class="comment-form">
                    <input type="hidden" name="post_slug" value="<?php echo htmlspecialchars($postSlug); ?>">
                    <input type="hidden" name="parent_id" value="">
                    <input type="hidden" name="<?php echo $config['spam']['honeypot_field']; ?>" value="" style="display: none;">

                    <?php if (!$currentUser): ?>
                        <div class="user-fields">
                            <div class="form-group">
                                <label for="comment-name">Name *</label>
                                <input type="text"
                                       id="comment-name"
                                       name="name"
                                       placeholder="Your name"
                                       maxlength="100"
                                       required>
                            </div>

                            <div class="form-group">
                                <label for="comment-email">Email *</label>
                                <input type="email"
                                       id="comment-email"
                                       name="email"
                                       placeholder="<EMAIL>"
                                       maxlength="255"
                                       required>
                                <small class="form-help">Your email will not be displayed publicly</small>
                            </div>
                        </div>
                    <?php else: ?>
                        <input type="hidden" name="name" value="<?php echo htmlspecialchars($currentUser['name']); ?>">
                        <input type="hidden" name="email" value="<?php echo htmlspecialchars($currentUser['email']); ?>">
                    <?php endif; ?>

                    <div class="form-group">
                        <label for="comment-content">Comment *</label>
                        <textarea id="comment-content"
                                  name="content"
                                  placeholder="Share your thoughts..."
                                  rows="4"
                                  maxlength="<?php echo $config['comments']['max_comment_length']; ?>"
                                  required></textarea>
                        <div class="character-count">
                            <span id="char-count">0</span> / <?php echo $config['comments']['max_comment_length']; ?>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="cancel-reply" class="btn btn-secondary" style="display: none;">Cancel Reply</button>
                        <button type="submit" class="btn btn-primary">Post Comment</button>
                    </div>
                </form>
            </div>

            <div id="comments-container" class="comments-container">
                <div id="comments-loading" class="loading" style="display: none;">
                    Loading comments...
                </div>
                <div id="comments-list" class="comments-list">
                    <!-- Comments will be loaded here via JavaScript -->
                </div>
                <div id="load-more-container" style="display: none;">
                    <button id="load-more-comments" class="btn btn-secondary">Load More Comments</button>
                </div>
            </div>
        </section>

        <script>
            // Configure comments system paths
            window.commentsConfig = {
                postSlug: '<?php echo htmlspecialchars($postSlug); ?>',
                basePath: '<?php echo htmlspecialchars($basePath); ?>',
                commentsPath: '<?php echo htmlspecialchars($basePath . 'comments/'); ?>'
            };

            // Initialize comments system
            document.addEventListener('DOMContentLoaded', function() {
                const commentsSystem = new CommentsSystem(window.commentsConfig.postSlug);
                commentsSystem.init();
            });
        </script>
        <?php
        return ob_get_clean();
    } catch (Exception $e) {
        // Debug output
        error_log("Error rendering comments for post: $postSlug - " . $e->getMessage());
        
        // Return an empty string or a default error message
        return '';
    }
}

function getPostSlugFromUrl() {
    $path = $_SERVER['REQUEST_URI'];
    $path = parse_url($path, PHP_URL_PATH);
    $path = trim($path, '/');
    
    // Remove .php extension if present
    if (substr($path, -4) === '.php') {
        $path = substr($path, 0, -4);
    }
    
    // Remove content/ prefix if present
    if (strpos($path, 'content/') === 0) {
        $path = substr($path, 8);
    }
    
    return $path ?: 'index';
}

// Auto-render comments if this file is included
// Auto-rendering removed - comments are now called explicitly from template
?>


