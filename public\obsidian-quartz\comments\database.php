<?php
/**
 * Database Connection and Helper Functions
 * For A. A. Chips' Obsidian-Quartz Comments System
 */

class CommentDatabase {
    private static $instance = null;
    private $pdo;
    private $config;

    private function __construct() {
        // Define access constant for secure config before loading
        if (!defined('OBQ_CONFIG_ACCESS')) {
            define('OBQ_CONFIG_ACCESS', true);
        }
        $this->config = require __DIR__ . '/config.php';
        $this->connect();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function connect() {
        $dbConfig = $this->config['database'];

        // Debug logging
        error_log("CommentDatabase: Attempting connection with host: {$dbConfig['host']}, user: {$dbConfig['username']}, db: {$dbConfig['dbname']}");

        try {
            $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset={$dbConfig['charset']}";
            $this->pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);

            // Add this line for debugging
            error_log("CommentDatabase: Connection successful to {$dbConfig['host']}");
        } catch (PDOException $e) {
            error_log("CommentDatabase: Connection failed to {$dbConfig['host']} with user {$dbConfig['username']}: " . $e->getMessage());
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }

    public function getPDO() {
        return $this->pdo;
    }

    public function getConfig($key = null) {
        if ($key === null) {
            return $this->config;
        }

        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return null;
            }
            $value = $value[$k];
        }

        return $value;
    }

    private function getTableName($table) {
        $prefix = $this->getConfig('database.table_prefix') ?? '';
        return $prefix . $table;
    }

    public function getTableNamePublic($table) {
        return $this->getTableName($table);
    }

    // User management (simplified for name/email auth)
    public function createOrUpdateUser($email, $name) {
        $usersTable = $this->getTableName('users');
        $sql = "INSERT INTO {$usersTable} (email, name)
                VALUES (:email, :name)
                ON DUPLICATE KEY UPDATE
                updated_at = CURRENT_TIMESTAMP";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            'email' => $email,
            'name' => $name
        ]);

        return $this->getUserByNameAndEmail($name, $email);
    }

    public function getUserByNameAndEmail($name, $email) {
        $usersTable = $this->getTableName('users');
        $sql = "SELECT * FROM {$usersTable} WHERE name = :name AND email = :email AND is_banned = FALSE";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            'name' => $name,
            'email' => $email
        ]);
        return $stmt->fetch();
    }

    public function getUserById($userId) {
        $usersTable = $this->getTableName('users');
        $sql = "SELECT * FROM {$usersTable} WHERE id = :id AND is_banned = FALSE";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['id' => $userId]);
        return $stmt->fetch();
    }

    // Comment management
    public function createComment($postSlug, $userId, $content, $parentId = null, $ipAddress = null, $userAgent = null) {
        $spamScore = $this->calculateSpamScore($content);
        $isSpam = $spamScore >= $this->getConfig('spam.spam_threshold');
        $isApproved = !$this->getConfig('comments.require_approval') && !$isSpam;

        $commentsTable = $this->getTableName('comments');
        $sql = "INSERT INTO {$commentsTable} (post_slug, user_id, parent_id, content, is_approved, is_spam, spam_score, ip_address, user_agent)
                VALUES (:post_slug, :user_id, :parent_id, :content, :is_approved, :is_spam, :spam_score, :ip_address, :user_agent)";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            'post_slug' => $postSlug,
            'user_id' => $userId,
            'parent_id' => $parentId,
            'content' => $content,
            'is_approved' => $isApproved,
            'is_spam' => $isSpam,
            'spam_score' => $spamScore,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent
        ]);

        return $this->pdo->lastInsertId();
    }

    public function getComments($postSlug, $page = 1, $limit = null) {
        if ($limit === null) {
            $limit = $this->getConfig('comments.comments_per_page');
        }

        $offset = ($page - 1) * $limit;

        $commentsTable = $this->getTableName('comments');
        $usersTable = $this->getTableName('users');
        $votesTable = $this->getTableName('comment_votes');

        $sql = "SELECT c.*, u.name,
                       (SELECT COUNT(*) FROM {$votesTable} cv WHERE cv.comment_id = c.id AND cv.vote_type = 'like') as likes,
                       (SELECT COUNT(*) FROM {$votesTable} cv WHERE cv.comment_id = c.id AND cv.vote_type = 'dislike') as dislikes
                FROM {$commentsTable} c
                JOIN {$usersTable} u ON c.user_id = u.id
                WHERE c.post_slug = :post_slug
                AND c.is_approved = TRUE
                AND c.is_spam = FALSE
                AND c.parent_id IS NULL
                ORDER BY c.created_at DESC
                LIMIT :limit OFFSET :offset";

        $stmt = $this->pdo->prepare($sql);
        $stmt->bindValue('post_slug', $postSlug);
        $stmt->bindValue('limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue('offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        $comments = $stmt->fetchAll();

        // Get replies for each comment
        foreach ($comments as &$comment) {
            $comment['replies'] = $this->getReplies($comment['id']);
        }

        return $comments;
    }

    public function getReplies($parentId, $depth = 0) {
        $maxDepth = $this->getConfig('comments.max_thread_depth');
        if ($depth >= $maxDepth) {
            return [];
        }

        $commentsTable = $this->getTableName('comments');
        $usersTable = $this->getTableName('users');
        $votesTable = $this->getTableName('comment_votes');

        $sql = "SELECT c.*, u.name,
                       (SELECT COUNT(*) FROM {$votesTable} cv WHERE cv.comment_id = c.id AND cv.vote_type = 'like') as likes,
                       (SELECT COUNT(*) FROM {$votesTable} cv WHERE cv.comment_id = c.id AND cv.vote_type = 'dislike') as dislikes
                FROM {$commentsTable} c
                JOIN {$usersTable} u ON c.user_id = u.id
                WHERE c.parent_id = :parent_id
                AND c.is_approved = TRUE
                AND c.is_spam = FALSE
                ORDER BY c.created_at ASC";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['parent_id' => $parentId]);

        $replies = $stmt->fetchAll();

        // Recursively get replies to replies
        foreach ($replies as &$reply) {
            $reply['replies'] = $this->getReplies($reply['id'], $depth + 1);
        }

        return $replies;
    }

    public function getCommentCount($postSlug) {
        $commentsTable = $this->getTableName('comments');
        $sql = "SELECT COUNT(*) FROM {$commentsTable}
                WHERE post_slug = :post_slug
                AND is_approved = TRUE
                AND is_spam = FALSE";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['post_slug' => $postSlug]);
        return $stmt->fetchColumn();
    }

    // Spam detection
    private function calculateSpamScore($content) {
        if (!$this->getConfig('spam.enable_spam_detection')) {
            return 0.0;
        }

        $spamPatternsTable = $this->getTableName('spam_patterns');
        $sql = "SELECT pattern, pattern_type, weight FROM {$spamPatternsTable} WHERE is_active = TRUE";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute();
        $patterns = $stmt->fetchAll();
        
        $score = 0.0;
        $content_lower = strtolower($content);
        
        foreach ($patterns as $pattern) {
            $match = false;
            
            switch ($pattern['pattern_type']) {
                case 'keyword':
                    $match = strpos($content_lower, strtolower($pattern['pattern'])) !== false;
                    break;
                case 'regex':
                    $match = preg_match('/' . $pattern['pattern'] . '/i', $content);
                    break;
                case 'url':
                    $match = preg_match('/' . $pattern['pattern'] . '/i', $content);
                    break;
            }
            
            if ($match) {
                $score += $pattern['weight'];
            }
        }
        
        return min($score, 1.0); // Cap at 1.0
    }

    // Rate limiting
    public function checkRateLimit($ipAddress, $userId, $actionType) {
        if (!$this->getConfig('spam.enable_rate_limiting')) {
            return true;
        }

        $limitKey = "rate_limits.{$actionType}_per_hour";
        $limit = $this->getConfig($limitKey);

        if (!$limit) {
            return true;
        }

        $rateLimitsTable = $this->getTableName('rate_limits');
        $sql = "SELECT COUNT(*) FROM {$rateLimitsTable}
                WHERE (ip_address = :ip_address OR user_id = :user_id)
                AND action_type = :action_type
                AND window_start > DATE_SUB(NOW(), INTERVAL 1 HOUR)";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            'ip_address' => $ipAddress,
            'user_id' => $userId,
            'action_type' => $actionType
        ]);

        $count = $stmt->fetchColumn();
        return $count < $limit;
    }

    public function recordRateLimit($ipAddress, $userId, $actionType) {
        $rateLimitsTable = $this->getTableName('rate_limits');
        $sql = "INSERT INTO {$rateLimitsTable} (ip_address, user_id, action_type)
                VALUES (:ip_address, :user_id, :action_type)";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            'ip_address' => $ipAddress,
            'user_id' => $userId,
            'action_type' => $actionType
        ]);
    }

    // Voting
    public function addVote($commentId, $userId, $voteType) {
        $votesTable = $this->getTableName('comment_votes');
        $sql = "INSERT INTO {$votesTable} (comment_id, user_id, vote_type)
                VALUES (:comment_id, :user_id, :vote_type)
                ON DUPLICATE KEY UPDATE vote_type = VALUES(vote_type)";

        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([
            'comment_id' => $commentId,
            'user_id' => $userId,
            'vote_type' => $voteType
        ]);
    }

    public function removeVote($commentId, $userId) {
        $votesTable = $this->getTableName('comment_votes');
        $sql = "DELETE FROM {$votesTable} WHERE comment_id = :comment_id AND user_id = :user_id";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([
            'comment_id' => $commentId,
            'user_id' => $userId
        ]);
    }

    public function getUserVote($commentId, $userId) {
        $votesTable = $this->getTableName('comment_votes');
        $sql = "SELECT vote_type FROM {$votesTable} WHERE comment_id = :comment_id AND user_id = :user_id";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            'comment_id' => $commentId,
            'user_id' => $userId
        ]);
        return $stmt->fetchColumn();
    }
}

