<?php
// Auto-generated blog post
// Source: content\30-things-about-myself.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '30 Things I Know About Myself';
$meta_description = 'This was an exercise for our stand up comedy support group. I hope reading through it doesn\'t cause you to eat rocks.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '30 Things I Know About Myself',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'This was an exercise for our stand up comedy support group. I hope reading through it doesn\'t cause you to eat rocks.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\30-things-about-myself.md',
);

// Post content
$post_content = '<p>This list began as an exercise for a stand-up comedy support group—a way to mine my quirks, contradictions, and life experiences for material. Some entries are absurd, others uncomfortably honest, and a few are just here to make you raise an eyebrow. While I didn’t quite hit 100, here’s a snapshot of me, unfiltered (but lightly polished for public consumption).</p>
<h3>The List:</h3>
<p>1. I never know how I am. Ask me, and I’ll say, “I don’t keep track.”
    
2. Eating is one of my great joys—sometimes to an embarrassing degree.
    
3. I drive like a grandma: five under the speed limit, with frequent multi-point turns.
    
4. Two years sober, and counting.
    
5. I need _a lot_ of sleep to function.
    
6. Talking isn’t my favorite activity, but I do it to survive.
    
7. I’m a 3.5/5 star cook, but my kitchen organization skills are flawless.
    
8. My snacks are legendary. So are my random facts.
    
9. I invented “Chiptocurrency,” the edible money of the future. (Patent pending.)
    
10. Mayonnaise is a culinary marvel. Fight me.
    
11. I sleep in a hammock whenever possible. Mattresses? Overrated.
    
12. Every time life goes sideways, I brainstorm an app to prevent it from happening again. (No investors yet.)
    
13. My car was a gift from the universe—long story involving Asheville and storage units.
    
14. I miss the tactile satisfaction of buttons and knobs. Touchscreens are the worst.
    
15. Nocturnal peeing is my nemesis. So are the stress dreams about public restrooms.
    
16. Restaurants stress me out. Give me a home-cooked meal any day.
    
17. I have a degree in Behavioral Psychology. Also, strong opinions about therapy.
    
18. I once walked across America. Now I’d rather not walk to the mailbox.
    
19. Rice, beans, PB&J: the holy trinity of survival foods.
    
20. If I were a cat, I’d be down to four lives. Maybe three.
    
21. Conflict resolution? Bottle it up, then turn it into creative fuel.
    
22. My cat has better coping mechanisms than I do.
    
23. I’m always open to unexpected windfalls. Universe, my Venmo is ready.
    
24. My witchcraft: building weirdly compelling websites for niche ideas.
    
25. White sage and palo santo give me a headache. Also, cultural appropriation isn’t a vibe.
    
26. My kryptonite: free Chinese takeout.
    
27. The bugs in my brain (metaphorical or otherwise) are valid and loved.
    
28. I used to be homeless—a chapter that taught me resilience and the value of community. Now I advocate for housing and mental health reform.
    
29. Recently went back to school to learn coding. Turns out, I love building things.
    
30. I speak Jewish Spanish (Ladino) and would love to teach it someday.
    
31. My family tree has some… complicated branches.</p>
<h3>Why This List?</h3>
<p>Comedy thrives on specificity and vulnerability. This exercise forced me to dig into the odd, mundane, and occasionally dark corners of my life—then laugh at them. Not every item is a joke, but each one is true. And if you’re writing your own list, remember: the best material often lies in the details you’re tempted to leave out.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>