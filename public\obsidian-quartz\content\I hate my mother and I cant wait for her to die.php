<?php
// Auto-generated blog post
// Source: content\I hate my mother and I cant wait for her to die.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '**Dear Abby: I Don\'t Hate My Mother, But I Hate What She Is Doing to My Child**';
$meta_description = 'Dear Abby: I Don\'t Hate My Mother, But I Hate What She Is Doing to My Child DEAR ABBY: I’m writing this after a long, sleepless night, tormented by...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '**Dear Abby: I Don\'t Hate My Mother, But I Hate What She Is Doing to My Child**',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'Dear Abby: I Don\'t Hate My Mother, But I Hate What She Is Doing to My Child DEAR ABBY: I’m writing this after a long, sleepless night, tormented by...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\I hate my mother and I cant wait for her to die.md',
);

// Post content
$post_content = '<h3><strong>Dear Abby: I Don\'t Hate My Mother, But I Hate What She Is Doing to My Child</strong></h3>
<p><strong>DEAR ABBY:</strong> I’m writing this after a long, sleepless night, tormented by feelings I struggle to accept. I am a kind, loving person, but last night, filled with a rage I can scarcely describe, I found myself thinking, “I hate my mother, and I can’t wait for her to be dead.”</p>
<p>Let me be clear: I have no intention of harming anyone. This hatred terrifies me because it is so at odds with my nature. I have forgiven her for her failures as a parent—she is a flawed person who never truly wanted children. I made peace with that years ago and created a good life for myself.</p>
<p>The breaking point is her role as a grandparent. She pushed her way into my child’s life and is now bestowing the same negativity, judgment, and harmful patterns onto them. She is repeating the cycle I worked so hard to break. The recent news about a violent political figure has triggered this eruption. The public conversation about harm, consequences, and relief has mirrored my private turmoil exactly.</p>
<p>How do I honor the commandment to “honor thy father and mother” when doing so feels like enabling harm? I am lost in a storm of guilt, rage, and grief.</p>
<p>— <strong>ROARING IN THE DARK</strong></p>
<p><strong>DEAR ROARING:</strong> Thank you for your breathtaking honesty. Your letter speaks to a silent struggle many are facing, particularly in an era of deep political and generational divides. What you are experiencing is not a failure of character; it is the sound of a protective parent’s love crashing against a generational curse.</p>
<p>First, let’s reframe that terrifying word: <strong>hatred</strong>. What you describe sounds less like malice and more like <strong>righteous, protective fury</strong>. It is the part of you that loves your child so fiercely it is willing to embrace a “forbidden” emotion to keep them safe. This anger is not the opposite of your kindness; it is the engine of it, powering your resolve to break a cycle that has caused you profound pain.</p>
<p>Regarding “honor,” your interpretation is both wise and valid. To honor does not mean to obey, tolerate, or subject your child to harm. It can mean to honestly acknowledge the role they played in making you who you are. And who are you? You are the <strong>cycle-breaker</strong>. You are the one who endured their shortcomings and emerged with a stronger moral compass. You honor her by being the parent—and the person—she was incapable of being. That is the highest form of honor.</p>
<p>The news event you mentioned acted as a trigger because it echoed your powerlessness. When public figures cause harm with impunity, it resonates deeply with those who feel helpless against a family member’s toxic behavior, often silenced by societal rules like “but she’s your mom!”</p>
<p>Your path forward is not about stifling this rage, but about <strong>channeling it</strong>:</p>
<p>1. <strong>Give the Rage a Safe outlet:</strong> Write it all down. Every vicious, unedited thought. Scream it into a pillow. Get it out of your head and contain it on a page you can control. This prevents it from burning you up inside.
    
2. <strong>Channel the Energy into Love:</strong> Direct this fierce energy toward your child. Plan a fun activity. Tell them what you love about them. Actively pour the love you didn’t receive into them. This is how you weaponize your anger against the curse.
    
3. <strong>Practice Self-Compassion:</strong> You are exhausted and in pain. Tell yourself, “It makes sense that I feel this way. I am protecting my child. I will not always feel this intensely, but for now, I listen.”</p>
<p>You are not a villain. You are a protector. The title of Jeanette McCurdy’s bestselling book, _I’m Glad My Mom Died_, resonated because it gave voice to a relief that many are shamed into hiding. Your feelings are valid.</p>
<p>Your honor is not found in silence. Sometimes, honor is a roar in the darkness—a roar that says, “The harm stops here.”</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>