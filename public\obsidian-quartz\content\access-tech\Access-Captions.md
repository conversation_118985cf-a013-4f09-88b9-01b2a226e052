#april #dd #disability #accessibility #hhs #hse #homework #library #manuals #notes #professional #project #publication #resources #revisit #rights #school #labt #vocation #webd  #writing 

---
Author:: April Cyr
Date:: 11/3/2022
Key:: Public

---


## Content Accessibility - Captioning and Transcripts
Captions and Transcripts are essential for video and audio content accessibility. Deaf and hard of hearing people benefit from Captions and Transcripts, but when was the last time you were in a public space without headphones, and wanted to play video or audio? For low bandwidth internet users, watching videos may only be possible at public wifi spots where there is access to an electrical outlet. In spaces that enforce quiet, you cannot have the sound on. In spaces where other parties are talking and conversating, you cannot hear the sound. This is an important aspect about Accessibility. It is not just about accommodating users with disabilities. Disabled users will benefit the most from these features, but these are ways to make content better and more universally usable. 

### Multilingual Application
Another considerable feature of Captioning and Transcripts is multilingual application. There are many benefits of deploying Captioning and Transcripts in multiple languages, and while it's not in the context of this guide, Dubbing Audio as well. Courses and trainings translated into other commonly spoke languages can be essential for providers and interpreters to learn content in the language that they serve their clientele. It's also a really great gesture for ESL (English as a Second Language) students. For technical and advanced material and concepts, language should never be a barrier to learning.

### Installing Captions and Transcripts
There are many softwares which will automatically generate subtitles on videos and audio to a fairly impressive accuracy. This is a native feature on Youtube. Unfortunately, Artificial Intelligence is often riddled with errors, no matter how good it is. Names and technical terms will get butchered. It is very important for a live human being, to go through the content and correct any errors. These errors can be very disruptive, and disrespectful to the integrity of the content and its creators. The finished product in it's entirety can be exported as a plain text(.txt) file. Video players should clearly display an option or settings bar to disable/enable subtitles. 

### Rambling Presentations
Not every lecture or presentation is easy to Caption. Some presenters use more words than others. For live presentations, having captioning is often not an option. Not everyone is reading off of a script. Some speakers will go on tangents and tell long stories that are not fully relevant to the content. This is okay. But if the presentation is recorded and posted to a public space such as a Youtube account, have someone review the video to add in accurate Captioning. This is generally a paid professional role, and adding captioning and a text file transcript is an asset which raises the value and reachability of materials.

If you have any questions or work requests related to Captioning and Transcripts, send a detailed <NAME_EMAIL>

Authorized for use for Public Awareness by A-B Tech.
April's Apple Chips Creative Commons Licensing, Commercial Rights Reserved.

