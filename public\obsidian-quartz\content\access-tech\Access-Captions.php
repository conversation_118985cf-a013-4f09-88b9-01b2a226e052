<?php
// Auto-generated blog post
// Source: content\access-tech\Access-Captions.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'april #dd #disability #accessibility #hhs #hse #homework #library #manuals #notes #professional #project #publication #resources #revisit #rights #school #labt #vocation #webd  #writing';
$meta_description = 'april dd disability accessibility hhs hse homework library manuals notes professional project publication resources revisit rights school labt vocatio...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'april #dd #disability #accessibility #hhs #hse #homework #library #manuals #notes #professional #project #publication #resources #revisit #rights #school #labt #vocation #webd  #writing',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'april dd disability accessibility hhs hse homework library manuals notes professional project publication resources revisit rights school labt vocatio...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\Access-Captions.md',
);

// Post content
$post_content = '<p>#april #dd #disability #accessibility #hhs #hse #homework #library #manuals #notes #professional #project #publication #resources #revisit #rights #school #labt #vocation #webd  #writing</p>
<p>---
Author:: April Cyr
Date:: 11/3/2022
Key:: Public</p>
<p>---</p>
<h2>Content Accessibility - Captioning and Transcripts</h2>
Captions and Transcripts are essential for video and audio content accessibility. Deaf and hard of hearing people benefit from Captions and Transcripts, but when was the last time you were in a public space without headphones, and wanted to play video or audio? For low bandwidth internet users, watching videos may only be possible at public wifi spots where there is access to an electrical outlet. In spaces that enforce quiet, you cannot have the sound on. In spaces where other parties are talking and conversating, you cannot hear the sound. This is an important aspect about Accessibility. It is not just about accommodating users with disabilities. Disabled users will benefit the most from these features, but these are ways to make content better and more universally usable.
<h3>Multilingual Application</h3>
Another considerable feature of Captioning and Transcripts is multilingual application. There are many benefits of deploying Captioning and Transcripts in multiple languages, and while it\'s not in the context of this guide, Dubbing Audio as well. Courses and trainings translated into other commonly spoke languages can be essential for providers and interpreters to learn content in the language that they serve their clientele. It\'s also a really great gesture for ESL (English as a Second Language) students. For technical and advanced material and concepts, language should never be a barrier to learning.
<h3>Installing Captions and Transcripts</h3>
There are many softwares which will automatically generate subtitles on videos and audio to a fairly impressive accuracy. This is a native feature on Youtube. Unfortunately, Artificial Intelligence is often riddled with errors, no matter how good it is. Names and technical terms will get butchered. It is very important for a live human being, to go through the content and correct any errors. These errors can be very disruptive, and disrespectful to the integrity of the content and its creators. The finished product in it\'s entirety can be exported as a plain text(.txt) file. Video players should clearly display an option or settings bar to disable/enable subtitles.
<h3>Rambling Presentations</h3>
Not every lecture or presentation is easy to Caption. Some presenters use more words than others. For live presentations, having captioning is often not an option. Not everyone is reading off of a script. Some speakers will go on tangents and tell long stories that are not fully relevant to the content. This is okay. But if the presentation is recorded and posted to a public space such as a Youtube account, have someone review the video to add in accurate Captioning. This is generally a paid professional role, and adding captioning and a text file transcript is an asset which raises the value and reachability of materials.
<p>If you have any questions or work requests related to Captioning and Transcripts, send a detailed <NAME_EMAIL></p>
<p>Authorized for use for Public Awareness by A-B Tech.
April\'s Apple Chips Creative Commons Licensing, Commercial Rights Reserved.</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>