[How to improve keyboard navigation of your web page | by <PERSON><PERSON><PERSON> | Chialab Open Source | Medium](https://medium.com/chialab-open-source/how-to-improve-keyboard-navigation-of-your-web-page-f11b324adbab)


![<PERSON><PERSON><PERSON>](https://miro.medium.com/fit/c/96/96/1*qzdSYS9v1IhjlR_ntSmkeQ.jpeg)



](https://medium.com/@nicolcarpignoli?source=post_page-----f11b324adbab--------------------------------)

[<PERSON><PERSON><PERSON>](https://medium.com/@nicolcarpignoli?source=post_page-----f11b324adbab--------------------------------)

Follow

Dec 7, 2018

·

5 min read

·

Listen

[

Save

](https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fbookmark%2Fp%2Ff11b324adbab&operation=register&redirect=https%3A%2F%2Fmedium.com%2Fchialab-open-source%2Fhow-to-improve-keyboard-navigation-of-your-web-page-f11b324adbab&source=--------------------------bookmark_header-----------)

# How to improve keyboard navigation of your web page

## Loock is an accessibility helper for keyboard navigation. It helps you make web pages easily navigable and more accessible, creating context areas in which keyboard navigation is “locked”.

> Also available in [Italian](https://medium.com/chialab-open-source/come-migliorare-la-navigazione-da-tastiera-e-rendere-pi%C3%B9-accessibile-una-web-app-5eb412015669).

![](https://miro.medium.com/max/1050/1*EbUlvAFSm0fZK8X3kE8vag.jpeg)

Keyboard navigation was historically painful.

# No one really use this… right?

During the development of a web application, accessibility and in particular keyboard navigation are maybe the most underestimated aspects. Apart from major companies and tech teams who are particularly sensible about this topic, it is not difficult to run into an unsatisfying implementation on most websites.

Keyboard navigation is not only important for accessibility but also for users who prefer navigate a web page without using the mouse or other computer peripherals.

# Know your users

Users who are looking for a good keyboard navigation system can be divided in two major sets:

-   The ones who need it
-   The ones who like it.

First type of users are the ones with disabilities, who need an accessible web site in order to navigate and get the informations they need. The keyboard navigation system, along with the read speaker support, becomes fundamental.

The second type of users represent those who can use the mouse — and are not bounded to the keyboard — but they want to.

These two kind of users have very different problems and needs, and the navigation system has to respond to all their demands. In every case, what we need is a system that “locks” the user navigation within “contexts” — semantic areas of a web page. Let’s see how to do this.

# Contexts

While navigating a web page using the keyboard, usually the thing that stands out most is the “disorientation” that we felt when we start to hit “TAB”. It is very common to felt lost on the web page.

Some major web sites have a system where on the first TAB the page shows a special button to easily switch to specific “contexts” of the page. This is very useful especially for very complex pages with a high number of different areas (i.e. a “content” area and the rest of the page).

![](https://miro.medium.com/max/1050/1*ZilfYBETXlZ9GMVA1Quc9A.png)

Github.com shows a button to easily switch to the content area.

Apart from this, we need to do more in order to give the user a good keyboard navigation system. We need to “contextualize” the page **every time the user has focus on a semantically contained area**. Think about side navigation menus, toolbars, notifications bars, dialogs, and so on. Every time the user enters one of these “contexts”, he doesn’t expect to exit the context with another “TAB”, but rather he just wants to list all the elements and then choose one of them.

You can think about a blind user who first wants to navigate a container element and listen from a page reader to all the available options, exploring the elements of the area. Probably he doesn’t want to exit that content while he’s exploring, and do all those actions again.

Another good example are dialogs, maybe the most sensible elements to keyboard navigation. If there is no “lock” system, at some point, the tabbing will lead the user out of the dialog scope, back to the browser tabs or worst, “under” the dialog, back to the page elements. That’s pretty bad.

A good way to handle this is to “lock” the “TAB” navigation inside the dialog, and let the user exit the dialog using dialog buttons but also with the “ESC” command.

To do this, you have two options: make this contextualization system by yourself, adapting it to your specific environment, or use _Loock_, a very lightweight library designed to do this. And it can be used with vanilla Javascript or with any Javascript framework you like. The choice is yours.

# Introducing Loock

[_Loock_](https://github.com/chialab/loock) has been created to make easy for a developer to implement a “contextualized” navigation system, and organize the web page into several semantic areas, so the user can navigate in a more natural and comfortable way.

Without _Loock_, the navigation system of the page follows the visual order of the elements. Try to navigate the toolbar on the following example. The “a11y” is the button that opens it. Get the focus clicking on the window and then use only your keyboard.

Navigation system **without** Loock.

As you can see, it’s not difficult to feel lost and find out that the focus, at some point, is out of the embed window. Now try to do the same actions on the example below. This example is using the _Loock_ system.

Navigation system **with** _Loock_.

The user can exit a context with “ESC” key. If the user goes back from the only opened context, the system will fallback to the previously defined default context. Let’s see how simple is to set a _Loock_ navigation system.

# Under the hood

_Loock_ has a very simple set of APIs. You only need to know few things first:

-   _Loock_ is based on contexts. A context is a set of elements that the user can navigate with TAB key without losing focus on them until he hits ESC
-   The default context is used as fallback context when the user has exited the only active context. If no default context has been defined, the navigation will flow out of the page as it happens without _Loock_
-   Once a context is exited or entered, _Loock_ triggers an event. This is useful to change UI elements, for example
-   Contexts have to be “entered” before using them. Default context does not need that.

See below to understand better those concepts and the available APIs.

<html>  
    <body>  
        <nav id="navigation" aria-label="Main navigation">  
            <a href="/">Home</a>  
            <a href="/posts">Posts</a>  
            <a href="/login">Login</a>  
        </nav>  
        <section id="main" aria-label="Main content">  
            ...  
        </section>        <script type="module">  
            import Loock from '@chialab/loock';  
            const loock = new Loock();  
              
            **// define the default context**  
            const mainContext = loock.createDefaultContext(document.getElementById('main'));  
             
            **// define one context**  
            const context = loock.createContext(document.getElementById('navigation'));            **// listen the context events**  
            context.on('enter', () => {  
               console.log('entered the navigation context');  
               // do stuff you like  
            });  
            context.on('exit', () => {  
               console.log('exited the navigation context');  
               // do stuff you like  
            });            **// activate the context**  
            context.enter();  
        </script>  
    </body>  
</html>

If you want to dig deeper on _Loock_ system, please take a look at the [open repository on Github](https://github.com/chialab/loock). You can contribute and help us make the web more accessible than ever! :)

> **_Chialab_** _is a design company. By developing strategy, design, software and content, we generate exciting relationships between brands and people._ [_https://www.chialab.i_](https://chialab.it/)_t._


#school #webd #html #css #web115 #textbooks #accessibility #vocational #professionaldev

---
Author:: Nicolò Carpignoli | Chialab Open Source
Date:: 10/3/2022
Key:: Public

---
