<?php
// Auto-generated blog post
// Source: content\access-tech\How to Improve Keyboard Navigation of your Web Page by <PERSON><PERSON>.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'How to improve keyboard navigation of your web page';
$meta_description = 'How to improve keyboard navigation of your web page | by Nicolò Carpignoli | Chialab Open Source | Mediumhttps://medium.com/chialab-open-source/how-t...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'How to improve keyboard navigation of your web page',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'How to improve keyboard navigation of your web page | by Nicolò Carpignoli | Chialab Open Source | Mediumhttps://medium.com/chialab-open-source/how-t...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\How to Improve Keyboard Navigation of your Web Page by Nicolo Carpignoli.md',
);

// Post content
$post_content = '<p><a href="https://medium.com/chialab-open-source/how-to-improve-keyboard-navigation-of-your-web-page-f11b324adbab" class="external-link">How to improve keyboard navigation of your web page | by Nicolò Carpignoli | Chialab Open Source | Medium</a></p>
<p>!<a href="https://miro.medium.com/fit/c/96/96/1*qzdSYS9v1IhjlR_ntSmkeQ.jpeg" class="external-link">Nicolò Carpignoli</a></p>

<p>](https://medium.com/@nicolcarpignoli?source=post_page-----f11b324adbab--------------------------------)</p>
<p><a href="https://medium.com/@nicolcarpignoli?source=post_page-----f11b324adbab--------------------------------" class="external-link">Nicolò Carpignoli</a></p>
<p>Follow</p>
<p>Dec 7, 2018</p>
<p>·</p>
<p>5 min read</p>
<p>·</p>
<p>Listen</p>
<p><a href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fbookmark%2Fp%2Ff11b324adbab&operation=register&redirect=https%3A%2F%2Fmedium.com%2Fchialab-open-source%2Fhow-to-improve-keyboard-navigation-of-your-web-page-f11b324adbab&source=--------------------------bookmark_header-----------" class="external-link"></p>
<p>Save</p>
<p></a></p>
<h1>How to improve keyboard navigation of your web page</h1>
<h2>Loock is an accessibility helper for keyboard navigation. It helps you make web pages easily navigable and more accessible, creating context areas in which keyboard navigation is “locked”.</h2>
<p>> Also available in <a href="https://medium.com/chialab-open-source/come-migliorare-la-navigazione-da-tastiera-e-rendere-pi%C3%B9-accessibile-una-web-app-5eb412015669" class="external-link">Italian</a>.</p>
<p>![](https://miro.medium.com/max/1050/1*EbUlvAFSm0fZK8X3kE8vag.jpeg)</p>
<p>Keyboard navigation was historically painful.</p>
<h1>No one really use this… right?</h1>
<p>During the development of a web application, accessibility and in particular keyboard navigation are maybe the most underestimated aspects. Apart from major companies and tech teams who are particularly sensible about this topic, it is not difficult to run into an unsatisfying implementation on most websites.</p>
<p>Keyboard navigation is not only important for accessibility but also for users who prefer navigate a web page without using the mouse or other computer peripherals.</p>
<h1>Know your users</h1>
<p>Users who are looking for a good keyboard navigation system can be divided in two major sets:</p>
<p>-   The ones who need it
-   The ones who like it.</p>
<p>First type of users are the ones with disabilities, who need an accessible web site in order to navigate and get the informations they need. The keyboard navigation system, along with the read speaker support, becomes fundamental.</p>
<p>The second type of users represent those who can use the mouse — and are not bounded to the keyboard — but they want to.</p>
<p>These two kind of users have very different problems and needs, and the navigation system has to respond to all their demands. In every case, what we need is a system that “locks” the user navigation within “contexts” — semantic areas of a web page. Let’s see how to do this.</p>
<h1>Contexts</h1>
<p>While navigating a web page using the keyboard, usually the thing that stands out most is the “disorientation” that we felt when we start to hit “TAB”. It is very common to felt lost on the web page.</p>
<p>Some major web sites have a system where on the first TAB the page shows a special button to easily switch to specific “contexts” of the page. This is very useful especially for very complex pages with a high number of different areas (i.e. a “content” area and the rest of the page).</p>
<p>![](https://miro.medium.com/max/1050/1*ZilfYBETXlZ9GMVA1Quc9A.png)</p>
<p>Github.com shows a button to easily switch to the content area.</p>
<p>Apart from this, we need to do more in order to give the user a good keyboard navigation system. We need to “contextualize” the page <strong>every time the user has focus on a semantically contained area</strong>. Think about side navigation menus, toolbars, notifications bars, dialogs, and so on. Every time the user enters one of these “contexts”, he doesn’t expect to exit the context with another “TAB”, but rather he just wants to list all the elements and then choose one of them.</p>
<p>You can think about a blind user who first wants to navigate a container element and listen from a page reader to all the available options, exploring the elements of the area. Probably he doesn’t want to exit that content while he’s exploring, and do all those actions again.</p>
<p>Another good example are dialogs, maybe the most sensible elements to keyboard navigation. If there is no “lock” system, at some point, the tabbing will lead the user out of the dialog scope, back to the browser tabs or worst, “under” the dialog, back to the page elements. That’s pretty bad.</p>
<p>A good way to handle this is to “lock” the “TAB” navigation inside the dialog, and let the user exit the dialog using dialog buttons but also with the “ESC” command.</p>
<p>To do this, you have two options: make this contextualization system by yourself, adapting it to your specific environment, or use _Loock_, a very lightweight library designed to do this. And it can be used with vanilla Javascript or with any Javascript framework you like. The choice is yours.</p>
<h1>Introducing Loock</h1>
<p><a href="https://github.com/chialab/loock" class="external-link">_Loock_</a> has been created to make easy for a developer to implement a “contextualized” navigation system, and organize the web page into several semantic areas, so the user can navigate in a more natural and comfortable way.</p>
<p>Without _Loock_, the navigation system of the page follows the visual order of the elements. Try to navigate the toolbar on the following example. The “a11y” is the button that opens it. Get the focus clicking on the window and then use only your keyboard.</p>
<p>Navigation system <strong>without</strong> Loock.</p>
<p>As you can see, it’s not difficult to feel lost and find out that the focus, at some point, is out of the embed window. Now try to do the same actions on the example below. This example is using the _Loock_ system.</p>
<p>Navigation system <strong>with</strong> _Loock_.</p>
<p>The user can exit a context with “ESC” key. If the user goes back from the only opened context, the system will fallback to the previously defined default context. Let’s see how simple is to set a _Loock_ navigation system.</p>
<h1>Under the hood</h1>
<p>_Loock_ has a very simple set of APIs. You only need to know few things first:</p>
<p>-   _Loock_ is based on contexts. A context is a set of elements that the user can navigate with TAB key without losing focus on them until he hits ESC
-   The default context is used as fallback context when the user has exited the only active context. If no default context has been defined, the navigation will flow out of the page as it happens without _Loock_
-   Once a context is exited or entered, _Loock_ triggers an event. This is useful to change UI elements, for example
-   Contexts have to be “entered” before using them. Default context does not need that.</p>
<p>See below to understand better those concepts and the available APIs.</p>
<html>  
    <body>  
        <nav id="navigation" aria-label="Main navigation">  
            <a href="/">Home</a>  
            <a href="/posts">Posts</a>  
            <a href="/login">Login</a>  
        </nav>  
        <section id="main" aria-label="Main content">  
            ...  
        </section>        <script type="module">  
            import Loock from \'@chialab/loock\';  
            const loock = new Loock();  
              
            <strong>// define the default context</strong>  
            const mainContext = loock.createDefaultContext(document.getElementById(\'main\'));  
             
            <strong>// define one context</strong>  
            const context = loock.createContext(document.getElementById(\'navigation\'));            <strong>// listen the context events</strong>  
            context.on(\'enter\', () => {  
               console.log(\'entered the navigation context\');  
               // do stuff you like  
            });  
            context.on(\'exit\', () => {  
               console.log(\'exited the navigation context\');  
               // do stuff you like  
            });            <strong>// activate the context</strong>  
            context.enter();  
        </script>  
    </body>  
</html>
<p>If you want to dig deeper on _Loock_ system, please take a look at the <a href="https://github.com/chialab/loock" class="external-link">open repository on Github</a>. You can contribute and help us make the web more accessible than ever! :)</p>
<p>> <strong>_Chialab_</strong> _is a design company. By developing strategy, design, software and content, we generate exciting relationships between brands and people._ <a href="https://chialab.it/" class="external-link">_https://www.chialab.i_</a>_t._</p>
<p>#school #webd #html #css #web115 #textbooks #accessibility #vocational #professionaldev</p>
<p>---
Author:: Nicolò Carpignoli | Chialab Open Source
Date:: 10/3/2022
Key:: Public</p>
<p>---</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>