# Three responses to fake news online

By April Cyr
1/24/2024

## Echo Chambers and Bad Information on the Internet

For the uninitiated in information discretion, the world wide web can be a place of conspiracies, echo chambers and wacky thinking. Even the best of us are vulnerable to this. If you do not know somebody who has been lost to echo chambers on social media, I guarantee you know somebody who has lost a family member to mis- and dis-information on the internet. When I say dangerous, I am not talking hyperbole. Wars can be sparked by bad information. There is an old proverb that.. ‘a lie can travel around the world twice before the truth can put pants on.’ There are support forums (subreddits) on reddit.com dedicated to [combating this trend such as r/Qanoncasualties](https://www.reddit.com/r/QAnonCasualties/wiki/index/).

  

  

The stakes are high in 2024. Here are three strategies that can be implemented that may help neutralize the spread of bad information and promote good hygiene on the internet:

  

### Why does this matter for Designers?

Platforms may be held legally liable if, let’s say the unmoderated flow of bad information leads to a bunch of people committing federal crimes. In 2024, deliberately bad information is a threat to everybody and as designers it’s important to be able to spot and understand what is happening. If you design websites with comments, forums, user post-a-bility.. It may be on you to ensure there are adequate mechanisms for moderation. Be the socially responsible design you wish to see in the world..

  

#### Additional Sources:

- [A Dangerous New Home for Online Extremism | WIRED](https://www.wired.com/story/a-dangerous-new-home-for-online-extremism/)
    
- [What is information warfare? With real examples | NordVPN](https://nordvpn.com/blog/information-warfare/)
    
- [They lost their families to conspiracy theories. Now they're finding others just like them on TikTok](https://www.businessinsider.com/qanon-tiktok-family-conspiracy-theories-community-2022-6)
    

## Digital Media Literacy Curricula in Finland

A success story in combatting mis- and dis-information is Finland, who has incorporated into their grade school curriculum Digital Media Literacy, from a young age. Kids in school are taught how to spot bad, fake news information. And as a result, Finland is a healthier and more enlightened society. This is a positive trend in education to make the web better and safer. American public education (and Higher Education) need to implement these lessons in curricula.

One app which has been designed to help build Digital Media Literacy in a fun (maybe also disturbing way) is called [Troll Factory](https://trollfactory.yle.fi/). You can play the app directly on the site. In the app you act as the role of an anonymous employee whose boss has ordered you to spread anti-immigration content online for (_revenue..? Clicks..? infamy..??_)

#### Learn more at the links below:

- [Finland’s ‘visionary’ fight against disinformation teaches citizens to question what they see online | Canada's National Observer](https://www.nationalobserver.com/2023/05/16/news/finland-visionary-fight-disinformation-teaches-citizens-question-online)
    
- [Is Disinformation the Same as Misinformation? | Union of Concerned Scientists](https://www.ucsusa.org/resources/disinformation-same-misinformation)
    
- [National AudioVisual Institute in Finland - Find free curriculum resources here](https://kavi.fi/en/media-education/)
    

  

Here are two web-based trends you can implement as an individual user or designer that will improve the quality of your research and data: Markdown and RSS Feeds.

## Markdown: What’s the Hype About?

A writing syntax for the web, focused on simplicity, and ease of learning. A method to catalog every article, every interesting post, every piece of text-based web content, and keep it in one location for easy access. Organizing all that data on bite sized text files that store easily. And if you are reading this, you likely already know 99% of how to write in it.

  

In the best of cases with Markdown repositories, you have a library of great web content. In the worst of cases with Markdown repositories, you have networks of mis and disinformation documented.

  

Sometimes it is easier to spot bunk information when we revisit it, instead of passively consuming and letting it disappear into the digital ether. Markdown is a powerful documentation tool that can be used in many different applications to improve the quality of data across the web. There are many websites operating today that run in Markdown, such as [Wikipedia](http://www.wikipedia.org) and [Reddit](http://www.reddit.com).

  

If I haven’t sold you on trying Markdown yet, imagine a Markup language that is 99% easier to learn than HTML 🙃.

#### To learn more about the basics of Markdown, check out the resources below 👇

- [Getting Started | Markdown Guide](https://www.markdownguide.org/getting-started/)
    
- [What Is Markdown, and How Do You Use It?](https://www.howtogeek.com/448323/what-is-markdown-and-how-do-you-use-it/)
    
- [Using Markdown in WordPress](https://wpengine.com/resources/using-markdown-wordpress/)
    
- [Why You Shouldn’t Use “Markdown” for Documentation — Eric Holscher](https://www.ericholscher.com/blog/2016/mar/15/dont-use-markdown-for-technical-docs/) (here’s a counterargument against my claim that Markdown is really good for documentation..)
## RSS Feeds: Beating the Algorithm at the Terminal

RSS Feeds are almost as old as Internet Blogs. Both first showed up around the late nineties, and have weathered the test of time. Unfortunately, many are not educated to the power and ease of access RSS can bring to the internet. They can be powerful in combating what are considered echo chambers and social media algorithms.

By aggregating all of the content you follow in one place, you can curate and control that feed 100%. It will save lots of time as well, preventing you from having to visit each site individually. Otherwise your feeds will be based on what can manipulate you into clicking and engagement for likes and third party revenue streams. With RSS, content can be kept informative and thought provoking.

Stay organized, save time, and find new content that actually consistently engage your interests.

You can embed an RSS Feed right into your Wordpress site/page.

- [What Is RSS? How to Use RSS in WordPress](https://www.wpbeginner.com/beginners-guide/what-is-rss-how-to-use-rss-in-wordpress/)
- [11 Best WordPress RSS Feed Plugins in 2023 (& How to Find Your WordPress RSS Feed)](https://blog.hubspot.com/website/wordpress-rss-feed)
- [WP RSS Aggregator – News Feeds, Autoblogging, Youtube Video Feeds and More – WordPress plugin](https://wordpress.org/plugins/wp-rss-aggregator/)

## Conclusion

The internet can be a dangerous place due to algorithms, fake news, and disinformation. This is a bad trend with disastrous consequences for the future of not only the internet, but also democracy, humanity, and the fate of the world.

  

Some countries have begun to implement Digital Media Literacy programs in school curriculums, meant ot teach kids and young adults how to spot B%S, and understand the civic duty of maintaining good information technology on the web. This is something for Web Developers and Designers to be acutely aware of, and consider in design, as well as for local and state governments to fund programs for.

  

Two technologies that give us as users and designers better control over information flow are Markdown, and RSS Feeds. By investing a small amount of time to learn these tools, the quality of your projects and internet use will be dramatically better.

##