
  

_Check out:_ [_“Nothing about me, without me” Center for Disability Rights - NYS_](https://cdrnys.org/blog/disability-dialogue/nothing-about-me-without-me/)

## What is Website Accessibility?

From _Introduction to Web Accessibility | Web Accessibility Initiative (WAI) | W3C_ [https://www.w3.org/WAI/fundamentals/accessibility-intro/](https://www.w3.org/WAI/fundamentals/accessibility-intro/)

  

“When websites and web tools are properly designed and coded, people with disabilities can use them. However, currently many sites and tools are developed with accessibility barriers that make them difficult or impossible for some people to use.

  

Making the web accessible benefits individuals, businesses, and society. International web standards define what is needed for accessibility.

  

While there are no particular guidelines within the Americans with Disabilities Act of 1990 (ADA), the Department of Justice references the Web Content Accessibility Guidelines 2.0.”

## What happens if your website isn’t ADA Compliant?

From _What Is ADA Compliance? (And What It Means for Your Site)_ [https://www.webfx.com/blog/marketing/what-is-ada-compliance/](https://www.webfx.com/blog/marketing/what-is-ada-compliance/)

  

“Unfortunately, if your website isn’t ADA Accessible, you are liable. A lawsuit could be filed against your company if people with disabilities cannot access or use your site. Even if your business didn’t intend to discriminate or exclude people with disabilities from visiting or using your website, you could pay thousands in dollars in lawsuits.”

Read more about Epilepsy and Vestibular issues at [_Web accessibility for seizures and physical reactions - Accessibility | MDN (mozilla.org)_](https://developer.mozilla.org/en-US/docs/Web/Accessibility/Seizure_disorders)

Page URL: [Workshop Videos Archive - Goodwill Industries of Northwest North Carolina, Inc. (goodwillnwnc.org)](https://www.goodwillnwnc.org/workshop-videos/)
## Running Accessibility Checker

[www.AccessibilityChecker.org](http://www.accessibilitychecker.org) allows you to enter the URL and automatically do a basic accessibility audit for any given webpage. This is what is called an Authenticator service and generally these still miss the majority of issues. This service only audits one page at a time, and generally is done with every page on the website. Here is an image snippet from a page scan. Let’s break it down..

  

#### Empty Links

An empty link, also known as a "null link" or "void link," is a hyperlink that does not have a valid destination or URL. This means that when a user clicks on an empty link, nothing happens. Empty links can be problematic for users with disabilities, especially those who use assistive technologies such as screen readers. Screen readers may not be able to identify empty links, and users may not be able to tell that the link is not functional. This can lead to confusion and frustration. In terms of web accessibility, empty links should be avoided as they create barriers for users with disabilities.

#### Broken ARIA References

Broken ARIA references occur when ARIA (Accessible Rich Internet Applications) attributes are used incorrectly or not at all. ARIA is a set of attributes that provide additional information about the purpose and functionality of web elements, making them more accessible to assistive technologies such as screen readers. Broken ARIA references can prevent assistive technologies from understanding the purpose of an element, making it difficult or impossible for users with disabilities to interact with the web page effectively.

#### Contrast Errors

While there were some color contrast errors throughout the 2022 site, which have been fixed, the problems that were flagged were not consequential to the functionality and usability of the website. Sometimes elements built into the design get flagged as color contrast errors.

#### Suspicious Link Text

Suspicious link text is a type of web accessibility issue that occurs when the text used for a hyperlink is not clear or descriptive enough to indicate the purpose or destination of the link. This can be confusing and frustrating for users, especially those who are using assistive technologies such as screen readers.

  

For example, using generic terms like "click here" or "learn more" as link text does not provide users with any information about where the link will take them or what they can expect to find on the linked page. This can make it difficult for users to decide whether to click on the link and can lead to them accidentally navigating to the wrong page or encountering unexpected content.

#### Missing Form Label

In any form, whether created in code or through a guided interface, every entry should have appropriate labels. This includes a visible label for the user and an ARIA-compliant label explaining the purpose of the form element. For example, a form item labeled "Name" with a small text box would require an ARIA label specifying the purpose and function of the input. The developer did use some ARIA on the 2022 website, but not well. It’s said that ‘No ARIA is better than Bad ARIA.’ If you don’t know how to do it right, don’t do it at all.

### What is ARIA?

ARIA (Accessibility Rich Internet Applications) is a set of features incorporated into modern browsers. These features enable various accessibility options, such as screen reading and keyboard navigation. ARIA assists in labeling buttons and links on a website. Consider yourself in a rocketship's cockpit, surrounded by numerous buttons, each with a specific purpose. Proper labeling is essential to prevent misuse and potential disastrous consequences. Similarly, buttons on a website require more than just visual cues. They need clear labels and descriptive names. Color alone is not sufficient for conveying meaning.

  

“Misusing ARIA results in a much more inaccessible experience than when developers do not use ARIA. Developers should try to understand and follow the rules of ARIA, to help provide a more accessible experience to people with disabilities.”

  

From _Top 5 Rules of ARIA | Deque_ [https://www.deque.com/blog/top-5-rules-of-aria/](https://www.deque.com/blog/top-5-rules-of-aria/)

  

## Color Contrast

  

From _Color contrast - Accessibility | MDN (mozilla.org)_

[https://developer.mozilla.org/en-US/docs/Web/Accessibility/Understanding_WCAG/Perceivable/Color_contrast](https://developer.mozilla.org/en-US/docs/Web/Accessibility/Understanding_WCAG/Perceivable/Color_contrast#:~:text=1.4.3%20Minimum%20contrast%20%28AA%29%20The%20color%20contrast%20between,have%20a%20contrast%20ratio%20of%20at%20least%204.5%3A1.)

## Heading Hierarchies

Headings are a feature on every word processor and well made websites used to create a hierarchy of structure. If you go on Microsoft Word, you will find the Heading Menu here:

  

### Why they are important for users and the browser

Headings parse long blocks of text into digestible and readable sections. On the Goodwill site, when there is blocks of text on a page, the developer instinctively used subtitles made with bold slightly larger font.

  

From [www.goodwillwnwc.org/services](http://www.goodwillwnwc.org/services) (add alt text and caption what did wrong)

The problem is that this is only a visual cue, and not every user is sighted. Using the appropriate Header also encodes meaning into the page itself. Headers can allow Tables of Contents to be automatically generated on longer texts like below:

  

  

On a Webpage, Headers create structure and are essential for both Screen Readers and Keyboard Navigators. There are widgets and extensions to your browser to automatically list Heading Structure on a page, and allow quick navigation to each section. Such as headingsMap.

  

  

Notice how many of these Heading 2s(H2s) are for different sections of the page, and not just paragraphs of text? I can click anywhere on that menu and immediately jump to that part of the page.

### How to Install Header Hierarchies

As a professional, I've been tasked with organizing lengthy and complex text documents by implementing a well-structured Heading Hierarchy. It's a meticulous and time-consuming process that requires careful attention to detail and adherence to established standards. There are currently no automated solutions available to accomplish this effectively. A human must meticulously review the text, comprehend the information being conveyed, and assign appropriate headings to each section and subsection. While it may appear straightforward, even a minor mistake can necessitate a complete restructuring of the document. To ensure accuracy and maintain browser compatibility, it's important to adhere to the six Heading levels (h1-h6) and avoid skipping between them (for example, from h1 to h4). While creating Heading Hierarchies in Microsoft Word for a text document is one thing, integrating them seamlessly into a website demands a comprehensive understanding of HTML, CSS, and ARIA. This requires a combination of technical proficiency and an eye for detail to ensure that the website is both user-friendly and accessible.

  

  

What happens here (as indicated with red) is that the browser is looking for the connected h2s and h3s, and when it finds none, gives up. In some cases, this can cause the page to crash when you attempt to use a Screen Reader.

### Benefits of Headings

Organizing web pages by headings helps users get a sense of the page’s organization and structure. Visually, headings are presented as larger and more distinct than surrounding text. Making texts larger helps guide the eye around the page. Using headings and making them visually apparent is especially helpful for users with cognitive disabilities.

  

If the underlying code for pages headings is correct, screen reader users can also benefit from headings. Screen reader users can navigate a page according to its headings, listen to a list of all the headings, and skip to a desired heading to begin reading at that point. Screen reader users can use headings to skip repeated blocks of contents like headers, menus, and side bars.

  

In 2017, WebAIm asked how screen reader users preferred to find information on lengthy web pages. Almost 70% of respondents said they preferred to use headings on a page. Clearly, organizing pages using headings is one of the best accessibility strategies available.

  

from _Usability & Web Accessibility (yale.edu)_ [https://usability.yale.edu/web-accessibility/articles/headings](https://usability.yale.edu/web-accessibility/articles/headings)

  

#### Has it been fixed?

Nope. They still misuse the heading hierarchy.

  

## What is Keyboard Navigation?

For those unable, or who prefer not to use a mouse, the keyboard is a primary method of navigating a computer. Other assistive technologies also rely on keyboard navigation, including voice recognition and screen readers. Keyboard navigation is one of the most important Accessibility items. To use Keyboard navigation, you use a combination of TAB, Arrow, Enter, and SpaceBar keys. Tab jumps from one interactive element to another. Enter Activates a link or menu. As does SpaceBar. Arrows can be used to move along the scroll bars of a page, as does SpaceBar if it is not focused on an element. Also used are Home, End, Page Up, and Page Down.

What is Keyboard Navigation?

  

From _Keyboard Navigation - Accessibility by Design (colostate.edu)_ [https://www.chhs.colostate.edu/accessibility/best-practices-how-tos/keyboard-navigation/](https://www.chhs.colostate.edu/accessibility/best-practices-how-tos/keyboard-navigation/)

#### Keyboard Navigation Demonstration

For this hands-on exercise, go to a desktop computer (if you are reading this by text). Connect online and open up a web browser. Any modern web browser will do. Pick a website that you believe is well-designed and professionally made. Some options to offer are:

  

[www.ABTech.edu](http://www.abtech.edu)

[www.HHS.gov](http://www.hhs.gov)

[https://developer.mozilla.org/en-US/](https://developer.mozilla.org/en-US/)

  

Without clicking anything on the page, locate and press the [TAB] Key. Press the Tab several times slowly and deliberately and watch what happens on the page. You may be prompted with an option to ‘Skip to Main Content’ or ‘Skip to Search Feature.’ After you will see a clearly delineated box hover over elements of the webpage from left to right, top to bottom. Try pressing [SHIFT] + [TAB] key to go backwards. Press [ENTER] to select an option, and if that option is a link to a new page, start again on the next page. Use the Arrow Keys as well to scroll slowly. Press the [SPACEBAR] key to scroll down faster. Press [SHIFT] + [SPACEBAR] key to scroll up faster. Notice the features of this experience.

#### Before

While navigating, there would be poorly visible faint dotted lines around the focus area. It was very difficult to engage with visually. This includes very poor color contrast.

#### After

Today, keyboard navigation works much better on this website. However there is still an issue. In order to navigate past the header, someone needs to tab 17 times. If you are going through multiple pages on this site, that can make for a bad user experience.

  

### Skip to: Functions

For Keyboard Navigators and screen Reader Users, if you have to go through the Navigation Bar and Header content every time you move to a new web page on the site, it gets very tiring. It is a helpful and essential feature to have Skip to Main Content, and Skip to Search Feature, at the very top of the page and at the beginning of Keyboard Tabbing. Skip to Main Content also needs an appropriate tab index to work on legacy browsers.

  

#### Why do I care about Keyboard navigation?

Growing up, I was diagnosed with Dysgraphia, which is a disorder affecting handwriting and fine motor skills in the hands. My earliest sports memory is when I was four years old and smashed my finger while attempting to dribble a basketball. It is common for children with Dysgraphia to learn to write with large pencils and grip tools. While I can write adequately as an adult, I occasionally struggle with lapses in grip and motor skills, especially when stressed. Dysgraphia is one of several disabling conditions I have, but it does not define me or my life. It has, however, led me to find using Keyboard Navigation on the computer to be more comfortable and effective.

  

Keyboard Navigation is essential for some users to access websites, while others find it a convenient alternative. I am a Developer with a background in Human Services and Social Work, specializing in Digital Accessibility and Disability Support. Currently, I am a full-time student at Asheville-Buncombe Technical Community College.

#### Street Clients, Toiled Hands, and Keyboard Skills

Imagine you are working odd manual labor jobs each day with Labor Finders. One day you are doing Roof Tiling, another Demolition. Your hours are long and paying about $11 an hour. Estranged from family and housing insecure, you take what you can get. Every day you come into the Goodwill Career Center to check your email for job postings and responses to applications in hope of something better. Your hands are toiling in pain. Computers were never something you were brought up on, and if we are being honest, using the mouse is not helping. Being new to computers, your wrist and hand are tense and stiff using the mouse because you want to get the movements right, like when somebody is learning to play the piano and their fingers lock up. The mouse doesn’t work well either and the fine motor skills needed to click on buttons is.. At best, annoying. In your work ethic you grew up modeling, we don’t complain when we are in pain. We carry on and suffer in silence. This is the problem with accessibility errors on the computer. People who suffer from them don’t know what is happening. They attribute it to computers being difficult and many times they either don’t want to, or are not in a position to complain.

## Client Success: Compliance, or Excellence?

Many clients at Goodwill Career Center lack computer skills due to accessibility barriers. The center can be a better education hub for beginners to have better opportunities. Providing educational materials on the website, like how to have the webpage read aloud, is important for accessibility. A Digital Accessibility Specialist can help optimize the digital experience for clients.

[Students Explain Digital Accessibility: Content Structure - YouTube](https://www.youtube.com/watch?v=u_yh9-iXtGQ) (length - 4:15)