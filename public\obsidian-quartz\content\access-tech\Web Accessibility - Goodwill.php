<?php
// Auto-generated blog post
// Source: content\access-tech\Web Accessibility - Goodwill.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'What is Website Accessibility?';
$meta_description = 'Check out: “Nothing about me, without me” Center for Disability Rights - NYShttps://cdrnys.org/blog/disability-dialogue/nothing-about-me-without-m...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'What is Website Accessibility?',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'Check out: “Nothing about me, without me” Center for Disability Rights - NYShttps://cdrnys.org/blog/disability-dialogue/nothing-about-me-without-m...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\Web Accessibility - Goodwill.md',
);

// Post content
$post_content = '
<p>_Check out:_ <a href="https://cdrnys.org/blog/disability-dialogue/nothing-about-me-without-me/" class="external-link">_“Nothing about me, without me” Center for Disability Rights - NYS_</a></p>
<h2>What is Website Accessibility?</h2>
<p>From _Introduction to Web Accessibility | Web Accessibility Initiative (WAI) | W3C_ <a href="https://www.w3.org/WAI/fundamentals/accessibility-intro/" class="external-link">https://www.w3.org/WAI/fundamentals/accessibility-intro/</a></p>

<p>“When websites and web tools are properly designed and coded, people with disabilities can use them. However, currently many sites and tools are developed with accessibility barriers that make them difficult or impossible for some people to use.</p>

<p>Making the web accessible benefits individuals, businesses, and society. International web standards define what is needed for accessibility.</p>

<p>While there are no particular guidelines within the Americans with Disabilities Act of 1990 (ADA), the Department of Justice references the Web Content Accessibility Guidelines 2.0.”</p>
<h2>What happens if your website isn’t ADA Compliant?</h2>
<p>From _What Is ADA Compliance? (And What It Means for Your Site)_ <a href="https://www.webfx.com/blog/marketing/what-is-ada-compliance/" class="external-link">https://www.webfx.com/blog/marketing/what-is-ada-compliance/</a></p>

<p>“Unfortunately, if your website isn’t ADA Accessible, you are liable. A lawsuit could be filed against your company if people with disabilities cannot access or use your site. Even if your business didn’t intend to discriminate or exclude people with disabilities from visiting or using your website, you could pay thousands in dollars in lawsuits.”</p>
<p>Read more about Epilepsy and Vestibular issues at <a href="https://developer.mozilla.org/en-US/docs/Web/Accessibility/Seizure_disorders" class="external-link">_Web accessibility for seizures and physical reactions - Accessibility | MDN (mozilla.org)_</a></p>
<p>Page URL: <a href="https://www.goodwillnwnc.org/workshop-videos/" class="external-link">Workshop Videos Archive - Goodwill Industries of Northwest North Carolina, Inc. (goodwillnwnc.org)</a>
<h2>Running Accessibility Checker</h2></p>
<p><a href="http://www.accessibilitychecker.org" class="external-link">www.AccessibilityChecker.org</a> allows you to enter the URL and automatically do a basic accessibility audit for any given webpage. This is what is called an Authenticator service and generally these still miss the majority of issues. This service only audits one page at a time, and generally is done with every page on the website. Here is an image snippet from a page scan. Let’s break it down..</p>

<p>#### Empty Links</p>
<p>An empty link, also known as a "null link" or "void link," is a hyperlink that does not have a valid destination or URL. This means that when a user clicks on an empty link, nothing happens. Empty links can be problematic for users with disabilities, especially those who use assistive technologies such as screen readers. Screen readers may not be able to identify empty links, and users may not be able to tell that the link is not functional. This can lead to confusion and frustration. In terms of web accessibility, empty links should be avoided as they create barriers for users with disabilities.</p>
<p>#### Broken ARIA References</p>
<p>Broken ARIA references occur when ARIA (Accessible Rich Internet Applications) attributes are used incorrectly or not at all. ARIA is a set of attributes that provide additional information about the purpose and functionality of web elements, making them more accessible to assistive technologies such as screen readers. Broken ARIA references can prevent assistive technologies from understanding the purpose of an element, making it difficult or impossible for users with disabilities to interact with the web page effectively.</p>
<p>#### Contrast Errors</p>
<p>While there were some color contrast errors throughout the 2022 site, which have been fixed, the problems that were flagged were not consequential to the functionality and usability of the website. Sometimes elements built into the design get flagged as color contrast errors.</p>
<p>#### Suspicious Link Text</p>
<p>Suspicious link text is a type of web accessibility issue that occurs when the text used for a hyperlink is not clear or descriptive enough to indicate the purpose or destination of the link. This can be confusing and frustrating for users, especially those who are using assistive technologies such as screen readers.</p>

<p>For example, using generic terms like "click here" or "learn more" as link text does not provide users with any information about where the link will take them or what they can expect to find on the linked page. This can make it difficult for users to decide whether to click on the link and can lead to them accidentally navigating to the wrong page or encountering unexpected content.</p>
<p>#### Missing Form Label</p>
<p>In any form, whether created in code or through a guided interface, every entry should have appropriate labels. This includes a visible label for the user and an ARIA-compliant label explaining the purpose of the form element. For example, a form item labeled "Name" with a small text box would require an ARIA label specifying the purpose and function of the input. The developer did use some ARIA on the 2022 website, but not well. It’s said that ‘No ARIA is better than Bad ARIA.’ If you don’t know how to do it right, don’t do it at all.</p>
<h3>What is ARIA?</h3>
<p>ARIA (Accessibility Rich Internet Applications) is a set of features incorporated into modern browsers. These features enable various accessibility options, such as screen reading and keyboard navigation. ARIA assists in labeling buttons and links on a website. Consider yourself in a rocketship\'s cockpit, surrounded by numerous buttons, each with a specific purpose. Proper labeling is essential to prevent misuse and potential disastrous consequences. Similarly, buttons on a website require more than just visual cues. They need clear labels and descriptive names. Color alone is not sufficient for conveying meaning.</p>

<p>“Misusing ARIA results in a much more inaccessible experience than when developers do not use ARIA. Developers should try to understand and follow the rules of ARIA, to help provide a more accessible experience to people with disabilities.”</p>

<p>From _Top 5 Rules of ARIA | Deque_ <a href="https://www.deque.com/blog/top-5-rules-of-aria/" class="external-link">https://www.deque.com/blog/top-5-rules-of-aria/</a></p>

<h2>Color Contrast</h2>

<p>From _Color contrast - Accessibility | MDN (mozilla.org)_</p>
<p><a href="https://developer.mozilla.org/en-US/docs/Web/Accessibility/Understanding_WCAG/Perceivable/Color_contrast#:~:text=1.4.3%20Minimum%20contrast%20%28AA%29%20The%20color%20contrast%20between,have%20a%20contrast%20ratio%20of%20at%20least%204.5%3A1." class="external-link">https://developer.mozilla.org/en-US/docs/Web/Accessibility/Understanding_WCAG/Perceivable/Color_contrast</a></p>
<h2>Heading Hierarchies</h2>
<p>Headings are a feature on every word processor and well made websites used to create a hierarchy of structure. If you go on Microsoft Word, you will find the Heading Menu here:</p>

<h3>Why they are important for users and the browser</h3>
<p>Headings parse long blocks of text into digestible and readable sections. On the Goodwill site, when there is blocks of text on a page, the developer instinctively used subtitles made with bold slightly larger font.</p>

<p>From <a href="http://www.goodwillwnwc.org/services" class="external-link">www.goodwillwnwc.org/services</a> (add alt text and caption what did wrong)</p>
<p>The problem is that this is only a visual cue, and not every user is sighted. Using the appropriate Header also encodes meaning into the page itself. Headers can allow Tables of Contents to be automatically generated on longer texts like below:</p>


<p>On a Webpage, Headers create structure and are essential for both Screen Readers and Keyboard Navigators. There are widgets and extensions to your browser to automatically list Heading Structure on a page, and allow quick navigation to each section. Such as headingsMap.</p>


<p>Notice how many of these Heading 2s(H2s) are for different sections of the page, and not just paragraphs of text? I can click anywhere on that menu and immediately jump to that part of the page.</p>
<h3>How to Install Header Hierarchies</h3>
<p>As a professional, I\'ve been tasked with organizing lengthy and complex text documents by implementing a well-structured Heading Hierarchy. It\'s a meticulous and time-consuming process that requires careful attention to detail and adherence to established standards. There are currently no automated solutions available to accomplish this effectively. A human must meticulously review the text, comprehend the information being conveyed, and assign appropriate headings to each section and subsection. While it may appear straightforward, even a minor mistake can necessitate a complete restructuring of the document. To ensure accuracy and maintain browser compatibility, it\'s important to adhere to the six Heading levels (h1-h6) and avoid skipping between them (for example, from h1 to h4). While creating Heading Hierarchies in Microsoft Word for a text document is one thing, integrating them seamlessly into a website demands a comprehensive understanding of HTML, CSS, and ARIA. This requires a combination of technical proficiency and an eye for detail to ensure that the website is both user-friendly and accessible.</p>


<p>What happens here (as indicated with red) is that the browser is looking for the connected h2s and h3s, and when it finds none, gives up. In some cases, this can cause the page to crash when you attempt to use a Screen Reader.</p>
<h3>Benefits of Headings</h3>
<p>Organizing web pages by headings helps users get a sense of the page’s organization and structure. Visually, headings are presented as larger and more distinct than surrounding text. Making texts larger helps guide the eye around the page. Using headings and making them visually apparent is especially helpful for users with cognitive disabilities.</p>

<p>If the underlying code for pages headings is correct, screen reader users can also benefit from headings. Screen reader users can navigate a page according to its headings, listen to a list of all the headings, and skip to a desired heading to begin reading at that point. Screen reader users can use headings to skip repeated blocks of contents like headers, menus, and side bars.</p>

<p>In 2017, WebAIm asked how screen reader users preferred to find information on lengthy web pages. Almost 70% of respondents said they preferred to use headings on a page. Clearly, organizing pages using headings is one of the best accessibility strategies available.</p>

<p>from _Usability & Web Accessibility (yale.edu)_ <a href="https://usability.yale.edu/web-accessibility/articles/headings" class="external-link">https://usability.yale.edu/web-accessibility/articles/headings</a></p>

<p>#### Has it been fixed?</p>
<p>Nope. They still misuse the heading hierarchy.</p>

<h2>What is Keyboard Navigation?</h2>
<p>For those unable, or who prefer not to use a mouse, the keyboard is a primary method of navigating a computer. Other assistive technologies also rely on keyboard navigation, including voice recognition and screen readers. Keyboard navigation is one of the most important Accessibility items. To use Keyboard navigation, you use a combination of TAB, Arrow, Enter, and SpaceBar keys. Tab jumps from one interactive element to another. Enter Activates a link or menu. As does SpaceBar. Arrows can be used to move along the scroll bars of a page, as does SpaceBar if it is not focused on an element. Also used are Home, End, Page Up, and Page Down.</p>
<p>What is Keyboard Navigation?</p>

<p>From _Keyboard Navigation - Accessibility by Design (colostate.edu)_ <a href="https://www.chhs.colostate.edu/accessibility/best-practices-how-tos/keyboard-navigation/" class="external-link">https://www.chhs.colostate.edu/accessibility/best-practices-how-tos/keyboard-navigation/</a></p>
<p>#### Keyboard Navigation Demonstration</p>
<p>For this hands-on exercise, go to a desktop computer (if you are reading this by text). Connect online and open up a web browser. Any modern web browser will do. Pick a website that you believe is well-designed and professionally made. Some options to offer are:</p>

<p><a href="http://www.abtech.edu" class="external-link">www.ABTech.edu</a></p>
<p><a href="http://www.hhs.gov" class="external-link">www.HHS.gov</a></p>
<p><a href="https://developer.mozilla.org/en-US/" class="external-link">https://developer.mozilla.org/en-US/</a></p>

<p>Without clicking anything on the page, locate and press the [TAB] Key. Press the Tab several times slowly and deliberately and watch what happens on the page. You may be prompted with an option to ‘Skip to Main Content’ or ‘Skip to Search Feature.’ After you will see a clearly delineated box hover over elements of the webpage from left to right, top to bottom. Try pressing [SHIFT] + [TAB] key to go backwards. Press [ENTER] to select an option, and if that option is a link to a new page, start again on the next page. Use the Arrow Keys as well to scroll slowly. Press the [SPACEBAR] key to scroll down faster. Press [SHIFT] + [SPACEBAR] key to scroll up faster. Notice the features of this experience.</p>
<p>#### Before</p>
<p>While navigating, there would be poorly visible faint dotted lines around the focus area. It was very difficult to engage with visually. This includes very poor color contrast.</p>
<p>#### After</p>
<p>Today, keyboard navigation works much better on this website. However there is still an issue. In order to navigate past the header, someone needs to tab 17 times. If you are going through multiple pages on this site, that can make for a bad user experience.</p>

<h3>Skip to: Functions</h3>
<p>For Keyboard Navigators and screen Reader Users, if you have to go through the Navigation Bar and Header content every time you move to a new web page on the site, it gets very tiring. It is a helpful and essential feature to have Skip to Main Content, and Skip to Search Feature, at the very top of the page and at the beginning of Keyboard Tabbing. Skip to Main Content also needs an appropriate tab index to work on legacy browsers.</p>

<p>#### Why do I care about Keyboard navigation?</p>
<p>Growing up, I was diagnosed with Dysgraphia, which is a disorder affecting handwriting and fine motor skills in the hands. My earliest sports memory is when I was four years old and smashed my finger while attempting to dribble a basketball. It is common for children with Dysgraphia to learn to write with large pencils and grip tools. While I can write adequately as an adult, I occasionally struggle with lapses in grip and motor skills, especially when stressed. Dysgraphia is one of several disabling conditions I have, but it does not define me or my life. It has, however, led me to find using Keyboard Navigation on the computer to be more comfortable and effective.</p>

<p>Keyboard Navigation is essential for some users to access websites, while others find it a convenient alternative. I am a Developer with a background in Human Services and Social Work, specializing in Digital Accessibility and Disability Support. Currently, I am a full-time student at Asheville-Buncombe Technical Community College.</p>
<p>#### Street Clients, Toiled Hands, and Keyboard Skills</p>
<p>Imagine you are working odd manual labor jobs each day with Labor Finders. One day you are doing Roof Tiling, another Demolition. Your hours are long and paying about $11 an hour. Estranged from family and housing insecure, you take what you can get. Every day you come into the Goodwill Career Center to check your email for job postings and responses to applications in hope of something better. Your hands are toiling in pain. Computers were never something you were brought up on, and if we are being honest, using the mouse is not helping. Being new to computers, your wrist and hand are tense and stiff using the mouse because you want to get the movements right, like when somebody is learning to play the piano and their fingers lock up. The mouse doesn’t work well either and the fine motor skills needed to click on buttons is.. At best, annoying. In your work ethic you grew up modeling, we don’t complain when we are in pain. We carry on and suffer in silence. This is the problem with accessibility errors on the computer. People who suffer from them don’t know what is happening. They attribute it to computers being difficult and many times they either don’t want to, or are not in a position to complain.</p>
<h2>Client Success: Compliance, or Excellence?</h2>
<p>Many clients at Goodwill Career Center lack computer skills due to accessibility barriers. The center can be a better education hub for beginners to have better opportunities. Providing educational materials on the website, like how to have the webpage read aloud, is important for accessibility. A Digital Accessibility Specialist can help optimize the digital experience for clients.</p>
<p><a href="https://www.youtube.com/watch?v=u_yh9-iXtGQ" class="external-link">Students Explain Digital Accessibility: Content Structure - YouTube</a> (length - 4:15)</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>