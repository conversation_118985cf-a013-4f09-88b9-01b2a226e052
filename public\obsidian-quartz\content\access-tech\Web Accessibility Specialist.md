#april #vocation #writing #tech #webd #accessibility #revisit

---
Author:: April Cyr
Date:: 10/14/2022
Key:: Public

---


If you or somebody you know has a website, that is a site of Public Accommodation. Under the Americans with Disabilities Act of 1990, it is mandatory to make reasonable efforts to accommodate users with various types of disability. These can be visual or auditory impairments, cognitive impairments, or fine motor skill impairments. While there is no accepted and defined legal guidelines under the ADA, rules for this are generally deferred to the Website Content Accessibility Guidelines (WCAG 2.0. 1990 was a long time ago, but the ADA itself was written in a robust way that allowed space for evolution. What does this all mean?

There is a framework of rules and best practices that are legally mandated for your website to be accessible. Not following this mandate can result in being the subject of a federal lawsuit. These have been steadily increasing year by year.

-If you hire a third party to build and maintain your website, there is no guarantee they are trained in this or are prepared to help with this.
-If you use tools like Wordpress or Squarespace, there is no guarantee your site is ADA Compliant. You can make your site compliant through those platforms but it still requires literacy and awareness.
-These standards exist for very good reason, and make websites better and more robust for User Experiences, especially for 26% of the population that has a disability, or relies on non-sighted or non-hearing browser features like keyboard navigation or screen reading. I'd argue that this is to include low bandwidth users as well, but not getting into that today.
-There are widgets, applications, and softwares available that can audit and remediate some of these issues on your website. Even the most advanced Artificial Intelligence can only detect about 30% of non-compliance errors. Software that promises to fix this instantly generally doesn't deliver, and it's pretty expensive.
-The best practice for dealing with this, is having a trained human being read through your website and it's code and make the changes. The most ethical practice is picking a trained human being who is disabled to do this, and pay them to do this. 
-The average starting rate for Accessibility Specialists is 45$ an hour.
-I am training to be a Web Accessibility Specialist and feel confident enough answering your questions (ask me anything!) I'm willing to help out with auditing and remediation! I don't need 45$ an hour but I am also not a charity worker either! I have been chronically unemployed for a long time, and really appreciate getting paid for my work! Questions are free! Tips are appreciated! Here are my payment plugs!

Thanks for reading! Stay safe and accessible! Not only is it the right thing to do, it is a legal mandate! 
