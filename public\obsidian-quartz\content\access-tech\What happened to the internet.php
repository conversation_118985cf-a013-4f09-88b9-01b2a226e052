<?php
// Auto-generated blog post
// Source: content\access-tech\What happened to the internet.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Echo Chambers';
$meta_description = 'Is it just me.. Or has the internet gotten much worse in the past fifteen years? Yes, nostalgia is one heck of a drug. And it can impair our judgment....';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Echo Chambers',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'Is it just me.. Or has the internet gotten much worse in the past fifteen years? Yes, nostalgia is one heck of a drug. And it can impair our judgment....',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\What happened to the internet.md',
);

// Post content
$post_content = '<p>Is it just me..</p>
<p>Or has the internet gotten much worse in the past fifteen years?</p>
<p>Yes, nostalgia is one heck of a drug. And it can impair our judgment. The grass is always greener on the other side. It always used to be better back then..</p>
<p>But I remember back when, sometime after the age of pop-ups and America Online, around the mid to late 2000’s, when we had what I believe to be peak internet.</p>
<p>Most of the features we enjoy were available, such as video sharing, large social media platforms, and content filters. But between then and now, the internet just has become a more dangerous place in preventable ways, and fixable ways. I want to detail a few of these, with some of the existing solutions to these problems. While there are solutions, they often require some technical experience and a learning curve to implement in one’s own life, much less for a large online audience..</p>
<h2>Echo Chambers</h2>
<p>For the uninitiated in information discretion, the world wide web can be a place of conspiracies, echo chambers and wacky thinking. Even the best of us are vulnerable to this. If you do not know somebody who has been lost to echo chambers on social media, I guarantee you know somebody who has lost a family member to mis- and dis-information on the internet. When I say dangerous, I am not talking hyperbole. Wars can be sparked by bad information. There is an old proverb that.. ‘a lie can travel around the world twice before the truth can put pants on.’ There are support forums (subreddits) on reddit.com dedicated to <a href="https://www.reddit.com/r/QAnonCasualties/wiki/index/" class="external-link">combating this trend such as r/Qanoncasualties</a>.</p>
<p>A success story in combatting mis- and dis-information is Finland, who has incorporated into their grade school curriculum Digital Media Literacy, from a young age. Kids in school are taught how to spot bad, fake news information. And as a result, Finland is a healthier and more enlightened society. This is a positive trend in education to make the web better and safer. American public education (and Higher Education) need to implement these lessons in curricula.</p>
<p>Learn more about <a href="https://www.nationalobserver.com/2023/05/16/news/finland-visionary-fight-disinformation-teaches-citizens-question-online" class="external-link">Finland’s ‘visionary’ fight against disinformation teaches citizens to question what they see online | Canada\'s National Observer</a></p>
<p><a href="https://www.ucsusa.org/resources/disinformation-same-misinformation" class="external-link">Is Disinformation the Same as Misinformation? | Union of Concerned Scientists</a></p>
<h2>De-Moderation</h2>
<p>There is no hell like the comments section of the internet. If you value your sanity and wellbeing, avoid reading comments on articles and videos on topics that are already depressing. There are spaces in the internet where there is no moderation. While that always has been the case, many forum and comment spaces used to have human moderators. We have seen in the past decade, the rise of hate content online, translate to increases in violence in the physical world.</p>
<p><a href="https://www.washingtonpost.com/nation/2018/11/30/how-online-hate-speech-is-fueling-real-life-violence/" class="external-link">How online hate turns into real-life violence - The Washington Post</a></p>
<p>The answer to this is supporting moderators online in the spaces that we love and cherish. Also, establishing accepted guidelines on what constitutes hate speech/content, and finding ways to pay full-time online moderators so they can support themselves. Moderation is real work and deserves to be paid as such. And doing so creates economic opportunities for people with disabilities, or who are homebound for various reasons such as parents and care providers.</p>
<p>Online moderation by humans has largely been outsourced to the third world, where workers are paid very little, and are shown to often have PTSD symptoms from the exposure of dangerous content they see on a day-to-day basis. This model is not sustainable.</p>
<p><a href="https://jolt.law.harvard.edu/digest/the-human-cost-of-online-content-moderation" class="external-link">The Human Cost of Online Content Moderation - Harvard Journal of Law & Technology</a></p>

<h2>Commercial Privatization</h2>
<p>In the early days of the internet, websites and online spaces functioned as shared platforms where people could socialize, converse, and engage in leisure activities collectively. However, the vision of a globally distributed entity owned by everyone has gradually shifted, and control has been consolidating in the hands of several dominant corporations.</p>

<p>The most prominent of these companies are often referred to by the acronym GAFA, which stands for Google, Amazon, Facebook, and Apple. These tech giants frequently acquire and merge with smaller companies, resulting in a growing trend where numerous applications and websites are built utilizing their respective platforms and services. For instance, React (owned by Facebook), Amazon Web Services, and Google\'s Cloud Computing Services are commonly used in the development of modern digital products.</p>

<p>Additionally, Microsoft, which has surpassed Apple in market valuation since the coining of the GAFA acronym, holds a substantial monopoly in the realm of enterprise-level suite servers. This includes businesses, non-profit organizations, and institutions that rely on Microsoft\'s software and services for their operations.</p>



<p>There is an online support group on Reddit I think a lot about called r/QAnonCasualties. This is a space where people who have lost a loved one to conspiratorial echo chambers online can find communion with others. Sitting there reading somber stories stirs up a lot of mixed feelings. It’s really difficult to mourn the death of somebody who is still there, but functionally gone from our lives.</p>
<p>At the same time, online communities can be life-saving and liberatory for some. For many, our family and friends, despite best intentions, may not be the best for us. For those who have grown up around abuse, addiction, or the alienation of the modern world, finding affirmation, connection, and purpose through the World Wide Web can be the best thing to happen.</p>
<p>Whichever side you may find yourself on, it’s important that we are the ones who make well-informed decisions about our life around the way we relate to and use the internet. Losing that control to anonymous algorithms, poor note management, and ‘loss of the commons’ type erosion of online spaces we love and cherish can have devastating consequences.</p>
<p>I’d like to share three different Web Trends that I believe make the internet a better place: <strong>RSS Feeds</strong>, <strong>Markdown Note Libraries</strong>, and <strong>Micro-Patron Income Streams</strong>.</p>

<h2>Social-Mediaification</h2>

<h3>Solution: RSS Feeds</h3>

<h2>Where did the Content Moderators go?</h2>

<h3>Solution: Moderator Income Streams</h3>


<h2>Markdown Note Management & Web Design</h2>
<p>Let me introduce you to Markdown-based Text Editing. This is a gift to anybody who can benefit from this technology life hack. Give it a chance, it might change your life..</p>

<h2>  </h2>
<h2>  </h2>
<h2>  </h2>
<h2>The Echoes of Loss: Finding Refuge and Empowerment in the Digital Labyrinth</h2>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>