<?php
// Auto-generated blog post
// Source: content\access-tech\What is a Glossary - Accessibility Primers.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'A glossary is a list of terms and their definitions that are related to a particular subject. Glossaries are often included in textbooks to help stude...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'A glossary is a list of terms and their definitions that are related to a particular subject. Glossaries are often included in textbooks to help stude...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\What is a Glossary - Accessibility Primers.md',
);

// Post content
$post_content = '<p>A glossary is a list of terms and their definitions that are related to a particular subject. Glossaries are often included in textbooks to help students understand the vocabulary that is used in the text. Glossaries can make learning more accessible by providing students with a quick and easy way to look up unfamiliar words. This can help students to better understand the text and to participate more fully in class discussions.</p>
<p>Glossaries can also be helpful for students who are learning English as a second language. By providing definitions of key terms in English, glossaries can help these students to better understand the text and to improve their English skills.</p>
<p>In addition, glossaries can be helpful for students who have learning disabilities. By providing definitions of key terms in a clear and concise way, glossaries can help these students to better understand the text and to succeed in school.</p>
<p>Overall, glossaries are a valuable tool that can help students to learn more effectively. They can provide students with a quick and easy way to look up unfamiliar words, which can help them to better understand the text and to participate more fully in class discussions. Glossaries can also be helpful for students who are learning English as a second language or who have learning disabilities.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>