<?php
// Auto-generated blog post
// Source: content\access-tech\ada-college-uni.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'ADA Compliance for Colleges & Universities - Laws Protecting People with Disabilities';
$meta_description = 'ADA Compliance for Colleges & Universities: Laws Protecting People with Disabilities - Rev';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'ADA Compliance for Colleges & Universities - Laws Protecting People with Disabilities',
  'author' => 'Rev',
  'date' => '2020-02-28',
  'excerpt' => 'ADA Compliance for Colleges & Universities: Laws Protecting People with Disabilities - Rev',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\ada-college-uni.md',
);

// Post content
$post_content = '<p>The Americans with Disabilities Act (ADA) was signed into law in 1990, and the Americans with Disabilities Act Amendments Act (ADAAA) was signed into law in 2008. The ADA is a federal law that prohibits discrimination against individuals with disabilities in the following areas:</p>
<p>- Public accommodations
- Employment
- Transportation
- State and local government services
- Telecommunications</p>
<p>The Rehabilitation Act is a federal law that prohibits discrimination against individuals with disabilities in programs receiving federal financial assistance. It was enacted in 1973, and amended in 1998, to include equal access to electronic and information technology.</p>
<p>The Rehabilitation Act applies to universities and colleges receiving federal funding. It requires that institutions make accommodations for equal access to all programs, services, and activities receiving federal financial assistance.</p>
<p>The ADA and the Rehabilitation Act require that institutions make accommodations for students with disabilities. Accommodations must be made for students using public-facing websites and any online courses offered. These must be made accessible to students with vision, hearing, and speech disabilities.</p>
<p>The ADA and the Rehabilitation Act also require that institutions make accommodations for employees with disabilities. Employees also have the right to an accessible, safe workplace.</p>
<p>Individual state laws may either mirror or add to the ADA and Rehabilitation Act accessibility requirements. Many states have "little 508" laws designed to ensure enforcement of federal standards. Other state regulations directly address the international standard for web accessibility, known as WCAG 2.0. This standard specifically requires closed captioning and audio description for video content.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>