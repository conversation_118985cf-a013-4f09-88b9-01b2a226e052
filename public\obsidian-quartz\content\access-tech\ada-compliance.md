---
title: Do I Need to Make My Website ADA Compliant?
author: Functional Lawyer
key: Public
tags:
  - accessibility
  - library
  - lawyer
  - index
  - tech
  - resources
---

## Does my Website Have to be ADA Compliant?
Yes. A website is a place of Public Accommodation. Both Private Businesses and Government have to provide website accessibility to their visitors. 

Your website has to be accessible for users that use assistive technology. Issues can arise from [lack of] color contrast between text and background. Inaccessible website is interpreted as active discrimination against people with disabilities. you are at risk of being sued. Lawsuits over ADA compliance have risen steadily year after year. A complaint will be written for one industry, which is first step of a lawsuit. Then it is very easy to swap out the name in the complaint. Typically we see one industry at a time being attacked. Lawsuits have risen sometimes 50% year after year. More and more businesses are starting since the pandemic, and have websites. Plaintiff attorneys know there is cash to be made. Be aware, this is a real concern. There are also ways to fix this and deter lawsuits. Business websites are considered places of public accommodation in the law. Ten new lawsuits per day in the country. 

The worldwide standard is the WCAG 2.0. These standards are mandatory under other Federal Laws such as the Rehabilitation Act of 1973. the Air Carrier Access Act of 1986. There is no technical standard under ADA, WCAG is best we have. Follow WCAG 2.0 AA Standards. 

[Does My Website Have to Be ADA Compliant? - YouTube](https://www.youtube.com/watch?v=UXlike1Qc_0)
<iframe width="560" height="315" src="https://www.youtube.com/embed/UXlike1Qc_0" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

<!-- Images without Alt Text
Lack of Color Contrast
No clear or meaningful hierarchy
Links without proper descriptions where they go -->
