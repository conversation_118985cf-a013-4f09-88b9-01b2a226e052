<?php
// Auto-generated blog post
// Source: content\access-tech\ada-healthcare-lawsuits.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '4 Healthcare Companies Sued Over ADA Website Compliance (and Why it Matters!)';
$meta_description = '4 Healthcare Companies Sued Over ADA Website Compliance (and Why it Matters!)';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '4 Healthcare Companies Sued Over ADA Website Compliance (and Why it Matters!)',
  'author' => 'Iolanda Bulgaru',
  'date' => '2021-07-24',
  'excerpt' => '4 Healthcare Companies Sued Over ADA Website Compliance (and Why it Matters!)',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\ada-healthcare-lawsuits.md',
);

// Post content
$post_content = '<h1>4 Healthcare Companies Sued Over ADA Website Compliance (and Why it Matters!)</h1>
<p>Is your healthcare website accessible to all people, including individuals with a disability? Just like your business premise, services or products, your digital properties (i.e., website, software, etc.) should all adhere to ADA compliance regulations. If your website is found to be not ADA compliant, you risk being slapped with a hefty lawsuit by the ADA or the Department of Justice (DoJ).</p>
<h2><strong>ADA Regulations Affect Every Website, Says Recent Clarification</strong></h2>
<p>Since being signed into law in 1990, the Americans with Disabilities Act (ADA) had fallen into stagnation, failing to stay abreast of changing technologies and times. That changed dramatically when newly introduced changes to ADA came to force last year, bringing major updates and clarifications to Section 508.</p>
<p>Section 508 is a provision under ADA Compliance regulations that enforces digital accessibility. Of more importance to healthcare bosses is that Section 508 now acknowledges WCAG 2.0/2.1, the typically-accepted Web Content Accessibility Guidelines set out by the World Wide Web Consortium (W3C). So, whether you’re a small clinic or an established network hospital, public or private, you should be aware that turning a blind eye to ADA website compliance is a sin that is being ruthlessly pursued by the DoJ.</p>
<h2><strong>Rising ADA Lawsuits: A Wake-Up Call to Healthcare Executives</strong></h2>
<p>If you think ADA accessibility regulations don’t apply to your website, think again. Given that healthcare is of paramount importance to Americans with a disability, ADA non-compliance can land you in hot soup, including having to pay steep fines of up to $150,000 (or more depending on the gravity of non-compliance). If that isn’t worrying enough, the number of ADA lawsuits has continued to rise sharply in the past two years or so. In fact, close to 5,000 Title III ADA lawsuits were filed in U.S. federal courts in the first half of 2018 alone, an eye-opening 33% uptick from the same time in 2017.</p>
<p>Interestingly, 21% of these cases were website accessibility lawsuits, most of which targeted big corporations, government agencies, and healthcare businesses. Of course, following the recent clarifications to Title III, it’s clear that ADA lawsuits will be filed at a record pace in 2019, and in the next few years. What a wake-up call to healthcare executives!</p>
<h2><strong>ADA Website Compliance: Why it Matters to Healthcare Sector</strong></h2>
<p>An ADA compliant website offers an online experience that is accessible to ALL users, including those with disabilities. Oftentimes that means your healthcare website should accommodate people with a visual disability, creating web pages and content that are suitable for screen-readers. More importantly, the entire site should be accessible via a keyboard.</p>
<p>According to <a href="chrome-extension://jbbplnpkjmmeebjpijfedlgcdilocofh/img/icons/contrast.svg" class="external-link">_![CONTRAST ERRORS: Very low contrast</a>The Ultimate Guide to Website ADA Compliance_](https://www.digitalauthority.me/resources/ada-compliance-website/) by Digital Authority Partners, compliance with website accessibility regulations is something that healthcare bosses should take to heart. They should think of it beyond simply being on the good side of the ADA. Sure, ADA non-compliance can come with far-reaching social and economic ramifications, but complying is also the right thing to do.</p>
<p>Of course, there are myriad other reasons to ensure that your website is ADA compliant:</p>
<p>+      If you aren’t compliant, you risk losing contracts, funding, and assistance from municipal, local and federal government agencies.
-        It’s good for your reputation and credibility as a healthcare facility. Needless to say, scrambling to fix your noncompliance when discovered can have an adverse effect on how patients and the general public perceive your business.
-        You’ll lose customers with a disability, numbering more than 25 million in the US alone.
-        Staying one step ahead of compliance regulations will help you keep costly ADA lawsuits and possible decisions against your firm at bay.</p>
<p>ADA website accessibility lawsuits are at an all-time high and affect organizations from the entire industry spectrum. From Netflix and Amazon to Adidas and local government agencies, no one is immune to ADA lawsuits – and healthcare organizations are no different. Here are 4 healthcare companies that made headlines for flouting website accessibility regulations and therefore faced ADA lawsuits:</p>
<h2><strong>(1) WellPoint, Inc.</strong></h2>
<p>_Steven Mendelsohn & Sam Chen vs. WellPoint Inc. (2014)_</p>
<p>Now called Anthem, WellPoint Health Networks Inc. was the name of the American health insurance company, a predecessor of Blue Cross of California, until 2014. The insurer was slapped with an ADA lawsuit by two visually impaired persons living in the state of California, Sam Chen and Steven Mendelsohn. The two were members of an affiliate company of WellPoint Health Networks Inc. called Anthem Blue Cross.</p>
<p>The duo informed WellPoint in 2011 about their difficulties accessing their main website. The good thing is that the insurer acted swiftly and entered into negotiations with them. Moreso, the company signed an agreement detailing how WellPoint planned to comply with WCAG 2.0.</p>
<h2><strong>(2) HCA Holdings, Inc.</strong></h2>
<p>!<a href="chrome-extension://jbbplnpkjmmeebjpijfedlgcdilocofh/img/icons/heading_possible.svg" class="external-link">ALERTS: Possible heading</a>_Frazier vs. HCA Holdings, Inc. (2017)_</p>
<p>The chances are good that you’ve been to a healthcare facility operated by HCA Holdings. They have over 100 hospitals under their belt. Frazier, the plaintiff in this lawsuit is a legally blind American who uses a screen-reader to browse online. The plaintiff, through her disability lawyers, claimed that websites of some of the HCA-owned hospitals were riddled with accessibility issues such as lack of alt text for images and failing to support keyboard navigation. The case is still open.</p>
<h2>(3) Tenet Healthcare**</h2>
<p>!<a href="chrome-extension://jbbplnpkjmmeebjpijfedlgcdilocofh/img/icons/heading_possible.svg" class="external-link">ALERTS: Possible heading</a>_The American Blind Community vs. Tenet Healthcare_</p>
<p>The operator of several healthcare facilities, including Hahnemann University Hospital, Hialeah Hospital, and Coral Gables Hospital, Tenet Healthcare was named in a class action lawsuit filed on behalf of all Americans with visual impairment. The complaint argues that the healthcare company’s websites aren’t accessible via screen-readers, and therefore violate the Rehabilitation Act Section 504 and Title III of the ADA.</p>
<h2><strong>(4) CAC Florida Medical Centers</strong></h2>
<p>_Andres Gomez vs. CAC Florida Medical Centers (2017)_</p>
<p>Andres Gomez is a serial ADA plaintiff who is legally blind. He recently filed an ADA lawsuit against CAC Florida Medical Centers in Federal District Court in Florida, claiming that he wasn’t able to access the company’s website via a screen reader. The lawsuit was ultimately dismissed by the judge.</p>
<h2><strong>Parting Remarks</strong></h2>
<p>From the gravity of this lawsuits, it’s apparent that ADA website compliance is something no healthcare executive should take lightly. To figure out whether a company’s website is truly ADA compliant, healthcare companies should carry out thorough, site-wide inspections employing a combination of automated and manual testing.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>