<?php
// Auto-generated blog post
// Source: content\access-tech\before-you-die.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Before You Die';
$meta_description = 'Before You Die - A. A. Chips';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Before You Die',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'Before You Die - A. A. Chips',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\before-you-die.md',
);

// Post content
$post_content = '<p>Spreading this information for those of you that don’t have your affairs in order. Make sure all bank accounts have direct beneficiaries. The beneficiary need only go to the bank with your death certificate and an ID of their own.</p>
<p>- TOD = Transfer On Death deed if you own a home. Completing this document and filing it with your county saves your heirs THOUSANDS. This document allows you to transfer ownership of your home to your designee. All they need to do is take their ID and your death certificate to the county building and the deed is signed over. Doing this will avoid the home having to go through probate.</p>
<p>- Living Will: Allows one to put in writing exactly what you want done in the event you cannot speak for yourself when it comes to healthcare decisions</p>
<p>- Durable Power of Attorney: Allows one to designate a person to make legal decisions if one is no longer competent to do so.</p>
<p>- Power of Attorney for Healthcare: This document allows one to designate someone to make healthcare decisions for their person.</p>
<p>- Last Will and Testament: Designates to whom personal belongings will go too.</p>
<p>- Funeral Planning Declaration: allows one to say exactly one’s wishes as far as disposition of the body and the services.</p>
<p>- If the above documents are done, you can AVOID probate. If all the above is not done, you have to open an estate account at the bank. All money that doesn’t have direct beneficiaries goes into this account. You have to have an attorney to open the estate account. The attorney also has to publicize your passing in the newspaper or post publication at the county courthouse, to allow anyone to make a claim on your property. - It’s a complete PAIN.</p>
<p>- Make a list of all banks and account numbers, all investment institutions with account numbers, lists of credit cards, utility accounts, etc. Leave clear instructions as to how and when these things are paid. Make sure heirs knows where life insurance policies are located.</p>
<p>- Make 100% sure SOMEONE knows your Apple ID, bank ID account logins and passwords!</p>
<p>- Make sure you have titles for all vehicles, campers, etc!</p>
<p>- MOST IMPORTANTLY!!!! - Talk with those closest to you and make all your wishes KNOWN. Talk to those whom you’ve designated, as well as those close to you whom you did not designate. - Do this to explain why your decisions were made and to avoid any lingering questions or hurt feelings.</p>
<p>Hope this helps! Hope this lights a spark to encourage all your friends and family to take care of these things to make it easier for those we all leave behind!</p>
<p>My hope is that the above list at least helps you start an important conversation with your loved ones.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>