<?php
// Auto-generated blog post
// Source: content\access-tech\independence-human-right.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Independence is a human right';
$meta_description = 'Independence is a human right Taking control of your life and having the freedom to make you own choices is a basic human right where work plays an im...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Independence is a human right',
  'author' => 'Abilia',
  'date' => '2025-10-10',
  'excerpt' => 'Independence is a human right Taking control of your life and having the freedom to make you own choices is a basic human right where work plays an im...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\independence-human-right.md',
);

// Post content
$post_content = '<h3>Independence is a human right</h3>
<p>Taking control of your life and having the freedom to make you own choices is a basic human right where work plays an important part. Being able to make a living is a basic need and creates both personal and economic independence as well as a sense of purpose and a higher quality of life.</p>
<p>The challenges that comes with cognitive disabilities like autism, ADHD or learning disabilities, are unique for every person. Some may excel in some areas while having difficulties in other areas.  In common are often difficulties that lead to practical everyday problems and makes it difficult to run a job like everyone else without any form of adjustment of work tasks. </p>
<h3>What are the challenges for employees with cognitive disabilities?</h3>
<p>•    Focusing on the task at hand</p>
<p>•    Planning the work day</p>
<p>•    Difficulties to handle changes</p>
<p>•    Remembering what to do and how to do certain work tasks</p>
<p>•    Understanding the responsibility of oneself versus colleagues</p>
<p>Having these challenges leads to stress, anxiety and some times to a mental break-down. There are several ways to facilitate the working environment by work tempo and tasks. </p>
<p>Our assistive tools provide employees with special needs a way to structure and plan the day and also instructions on how to perform specific work tasks. By adjusting work tasks and providing aids the employee can do a much better job will less stress and that goes for the managers and the colleagues as well.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>