My entire life, I have grown up around computers. This is not everyone's reality. Many people find themselves needing to learn computer skills for school, or work, or keeping in touch with their loved ones. Most modern computers are equipped with a host of tools that make their use easier for people with access issues.

### What does this all mean?

Some people using computers have disabilities. Some people using computers are very young, old, financially disadvantaged, don't speak English as a first language. Some are scared of using computers. Some have used computers for decades. And the truth is, better usability benefits everyone listed above.

I am creating this little nook to have helpful resources and information for anyone who might benefit. It's not exhaustive. But it's something. 

[[What is a computer and how does it handle information differently than humans]]

[[subtitles & transcripts]]
[[glossaries]]
[[heading conventions]]
[[keyboard navigation]]
[[screen readers]]
[[low bandwidth access]]
[[video & audio media]]
[[outlines and written detailed instructions]]
[[americans with disabilities act simplified]]
[[web content accessibility guidelines]]
[[personal cybersecurity]]
[[color contrasts & text legibility]]
[[what is aria?]]
[[hybrid flexible teaching model]]
[[student accommodation list]]


[[Before you die]] - This is a generalized list from an anonymous source of things to do before you die. Something important on this list is to make sure the passwords to all of your computer accounts are written down somewhere safe.
[[Good Clinic Practices Post Covid World]] - some people may get into computers more to access virtual appointments. Whether this is or isn't for viral illness related health reasons, here is a list I put together of good practices for in person clinic locations to implement to keep their clients/patients safe.
[[Accessing Websites]] - Generally on a computer you will either be interacting with software/applications, or websites on the internet. Both by law need to be made and designed with certain standards of being user friendly. Sometimes they fail in this regard. You likely aren't losing your mind, it's just not made well. Here is what you should expect visiting a web page, and what to do if it falls short.
[[Accessibility tools built into modern operating systems]] - These are tools built into almost every modern device that have the potential to make your life around technology much easier.
[[Avoiding bad information and news on internet]]