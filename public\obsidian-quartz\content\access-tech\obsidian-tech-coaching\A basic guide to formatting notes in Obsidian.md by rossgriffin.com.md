

[A basic guide to formatting notes in Obsidian.md (rossgriffin.com)](https://rossgriffin.com/tutorials/obsidian-basics-guide/)

# [A basic guide to formatting notes in Obsidian.md](https://rossgriffin.com/tutorials/obsidian-basics-guide/)

![](https://i0.wp.com/rossgriffin.com/wp-content/uploads/2021/08/Obsidian-Graph.jpg?fit=800%2C409&ssl=1)

[Home](https://rossgriffin.com/) » [Blog](https://rossgriffin.com/blog/) » [Tutorials](https://rossgriffin.com/category/tutorials/) » A basic guide to formatting notes in Obsidian.md

-   [<PERSON>](https://rossgriffin.com/)
-   August 30, 2021

#### Recent Posts

[A Day of Rest, Self-Reflection and Appreciation](https://rossgriffin.com/self-improvement/a-day-of-rest-self-reflection-and-appreciation/)

March 2, 2022

[My Obsidian Personal Knowledge Management System – Walk Through](https://rossgriffin.com/productivity/my-obsidian-personal-knowledge-management-system-walk-through/)

February 5, 2022

[Why I decided to start a degree at age 25](https://rossgriffin.com/essays/why-i-decided-to-start-a-degree-at-age-25/)

February 5, 2022

[Running Calculator](https://rossgriffin.com/self-improvement/running/running-calculator/)

February 2, 2022

[No Switchy](https://rossgriffin.com/misc/no-switchy/)

January 21, 2022

[](https://rossgriffin.com/self-improvement/a-day-of-rest-self-reflection-and-appreciation/)

[](https://rossgriffin.com/productivity/my-obsidian-personal-knowledge-management-system-walk-through/)

[](https://rossgriffin.com/essays/why-i-decided-to-start-a-degree-at-age-25/)

[](https://rossgriffin.com/self-improvement/running/running-calculator/)

[](https://rossgriffin.com/misc/no-switchy/)

**Download a FREE obsidian.md reference guide:**

Success! Now check your email to confirm your subscription.

Obsidian.md is a [markdown](https://www.markdownguide.org/getting-started/) text editor. It is different from other markdown editors because it has jumped on the _linked thought_ band wagon. “Linked Thought” refers to a group of note taking applications that allow you to seamlessly link thoughts and notes together. It is like having your very own wikipedia. The applications go much further than note taking. Applications like [Obsidian.md](https://obsidian.md/) and [Roam Research](https://roamresearch.com/) are spearheading a [knowledge management](https://en.wikipedia.org/wiki/Knowledge_management#:~:text=Knowledge%20management%20(KM)%20is%20the,the%20best%20use%20of%20knowledge.&text=KM%20is%20an%20enabler%20of%20organizational%20learning.) revolution. People have used it to write academic papers and novels. People also use it to support their own work: everyone from software developers to lawyers are seeing the value in the idea of _linked thought_.

If you are unfamiliar with markdown it can be tricky to get started with obsidian. This article is meant to be a quick reference guide on the basics of Obsidian and the Markdown specific to obsidian. It is aimed at beginners and people who are unfamiliar with markdown.

Most Obsidian Tutorials start with how to link pages together, this doesn’t make any sense. While this is one of the big selling points of Obsidian it can be a confusing topic for someone that is just starting out. My approach will be to explain obsidian as a text editing tool, and then we’ll add “linked thought” at a later stage as the icing on the cake. You can use the contents menu to jump to a section you want to read more about.

Here is a walkthrough of [my Obsidian.md personal knowledge management system](https://rossgriffin.com/tru/).

## Basic Text formatting

Like Microsoft Word or Apple pages Obsidian allows you to perform some basic text editing like making text: Bold, Italic, Strike Through and highlighted.

---

**This is some bold text**:

```
**This is some bold text**
```

---

_This is italicized text_

```
*This is italicized text*
```

---

~~This text has a strikethrough~~

```
~~This text has a strike through~~
```

---

This is highlighted

```
==This is highlighted==
```

---

Block quotes are a good way of indicating that you’re quoting someone, or to call attention to specific text:

![](https://i0.wp.com/rossgriffin.com/wp-content/uploads/2021/08/Block-Quote.jpg?resize=800%2C58&ssl=1)

```
> Block Quote
```

---

## Headings and Horizontal Rules

In Obsidian you can add headings:

![](https://i0.wp.com/rossgriffin.com/wp-content/uploads/2021/08/Headings-Example.jpg?resize=671%2C269&ssl=1)

```
# Heading 1
## Heading 2
### Heading 3
#### Heading 4
##### Heading 5
###### Heading 6
```

---

Another helpful text editing tool is the horizontal rule.

---

Use 3 dashes for a horizontal rule: `**---**`  
**One more note on headings and horizontal rules:** you can put 3 dashes underneath text to make it a heading like this:

```
some text
---
```

This will display as a heading 1

## Lists and Checklists

In Obsidian you can create ordered lists, unordered lists and check lists:

### Ordered list

1.  This
2.  is an
3.  Ordered list

**The Markdown:**

```
1. This
2. is an
3. Ordered list
```

### Unordered List

-   This
-   is an
-   unordered list

**The Markdown:**

```
- This
- is an
- unordered list
```

### Checklist

-   This
-   is a
-   checklist

**The Markdown:**

```
- [x] This
- [ ] is a
- [ ] checklist
```

## Code Blocks

Code blocks are useful for two reasons: one, the code is not compiled in your editor. Two, the code will in most cases have proper syntax highlighting.

![](https://i0.wp.com/rossgriffin.com/wp-content/uploads/2021/08/HTML-example.jpg?resize=800%2C175&ssl=1)

To insert a code block use the “` followed by the programming language you want to use. For example:

**The Markdown:**

````
```html
some code here
```
````

## Tables

Obsidian allows you to insert tables into text:

Heading

Description

Header

Title

Paragraph

Text

**The Markdown:**

```
| Syntax      | Description |
| ----------- | ----------- |
| Header      | Title       |
| Paragraph   | Text        |
```

Tables are a bit tricky to work with in markdown so I’d recommend downloading the plugin “Advanced Tables” which makes editing tables a more pleasant experience.

## Footnotes

Footnotes are great if you want to add something to your notes without breaking the flow of your writing. The mark up would look like this:

```
Text with foot note[^1]

[^1]: Footnote
```

**Tip:** I’d highly recommend the plugin: “Footnote Shortcut” if you are going to be using footnotes on a regular basis.

## Linking

### Linking and back Linking

The most basic way to link in obsidian is the wiki style link. This is an in-text link to another page in your obsidian vault. You can achieve this by using square brackets like so: **[[Page Link]]**

You can also link to specific blocks by adding a “^” symbol after your page name like so: **[[Page Link^block to link to]]**. When you do this, Obsidian will bring up a context menu to assist you in choosing the correct block in your document. You can link to other pages in your obsidian vault or you can use this to link to blocks in the current document. This is helpful for creating page contents for large documents.

Explaining back links in text is a bit difficult so I have made a video below to demonstrate how this works.

### Adding Aliases

Aliases are a way to link to your files with different names. For example, you want to link to a page called “Productivity Home” this page contains all your important research on productivity. If you wanted to use this text in a natural sentence it would be tricky. In come Aliases: using an aliase you can refer to this page like so “[[Productivity Home|**Productivity**]] is a topic that too many people have an opinion about, but few have truly mastered.” The bolded text is what will display in your document. You can find a more clear explanation of this in the video below:

### Linking to External Locations

You can link to websites and files on your computer by using external links. They’re different from internal links. External links look like this: **[Text]([https://placetolinkto.com](https://placetolinkto.com/))**

## Embedding content

Embedding content is another thing that makes _linked thought_ software so powerful. In Software like Obsidian.md and Roam Research you can link to other pages or blocks. This is powerful because you can show an entire page within a page, or just a paragraph or two.

This is great because when the content is updated on the original page it is also updated everywhere it’s embedded. People have found many creative uses for this feature.

To embed a single page use this syntax: **![[Page Name]]** Notice how it’s the same as linking to page except you just put the exclamation mark in front?

You can embed just a paragraph by using the same syntax but, you’ll need to include the “^” symbol after the page name like so: **![[Page Name^block to link to]]**

### Embedding images and other file types.

You can embed media in your Obsidian documents. You’ll need to make sure that the media exists in the vault folder first. Many people like to create an attachemnts folder and keep all their media there. Once you’ve put your media in the obsidian folder you can link to it like this: ![[picture.jpg]]

Here is a list of file types you can embed in obsidian:

1.  Markdown files: `md`;
2.  Image files: `png`, `jpg`, `jpeg`, `gif`, `bmp`, `svg`;
3.  Audio files: `mp3`, `webm`, `wav`, `m4a`, `ogg`, `3gp`, `flac`;
4.  Video files: `mp4`, `webm`, `ogv`;
5.  PDF files: `pdf`.

[Video] – Coming Soon

## Queries and Search

Queries allow you to find several notes in your vault that match a specific criteria. This is helpful if you want to create a hub for specific notes. For example, you could tag all notes derived from videos, and then query your vault so only the notes from a specific creator are shown:

````
```query
#video + Tiago Forte
```
````

In my vault this will show me all notes on videos by the creator Tiago Forte

[Video] – Coming Soon

## Adding Meta Data

You can add additional data to your notes such as tags and aliases. Metadata uses a markup called YAML which stands for “Yet another markup language”.

YAML metadata is useful if you want to add tags to your notes or globally refer to notes by an alias. YAML is hidden in notes so you can add a lot of data to the YAML markup without making your notes messy.

YAML in obsidian typically looks like this:

```
---
alias: [how to use obsidian,obsidian guide]
tags: [note]
---
```

The dashes will go a different colour (By default, the dashes are green) if you have placed the YAML in your notes correctly.

By default obsidian supports the following YAML in this order:

1.  alias
2.  tags
3.  cssclass

You are able to add more YAML metadata but it’s not natively supported by obsidian. However, this can still be useful if you’re using plugins like _Dataview_

[Video] – Coming Soon

## Other Obsidian Content

1.  [My obsidian workflow walkthrough (30min video)](https://rossgriffin.com/productivity/my-obsidian-personal-knowledge-management-system-walk-through/)

### _You may also be interested in:_

[](https://rossgriffin.com/the-round-up/how-to-choose-your-priorities-like-warren-buffett/?relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=0&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=0&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=0 "How to choose your priorities like Warren Buffett #23")

#### [How to choose your priorities like Warren Buffett #23](https://rossgriffin.com/the-round-up/how-to-choose-your-priorities-like-warren-buffett/?relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=0&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=0&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=0 "How to choose your priorities like Warren Buffett #23")

Join The NewsletterThe following is an excerpt from The Round Up, a somewhat frequent newsletter that I send out. SubscribeI won't send you spam. Unsubscribe at any time.Built with ConvertKit Hey everyone,Welcome to all the new faces around here! It's really encouraging to see this newsletter growing. One of the…

[](https://rossgriffin.com/the-round-up/the-round-up-24/?relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=1&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=1&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=1 "The Round Up #24")

#### [The Round Up #24](https://rossgriffin.com/the-round-up/the-round-up-24/?relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=1&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=1&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=1 "The Round Up #24")

Join The NewsletterThe following is an excerpt from The Round Up, a somewhat frequent newsletter that I send out. SubscribeI won't send you spam. Unsubscribe at any time.Built with ConvertKit Hey everyone, As usual, welcome to all the new faces around here. Since I posted my obsidian markdown guide there…

[](https://rossgriffin.com/the-round-up/the-round-up-25/?relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=2&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=2&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=2 "The Round Up #25")

#### [The Round Up #25](https://rossgriffin.com/the-round-up/the-round-up-25/?relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=2&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=2&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=2 "The Round Up #25")

Hey Everyone, as usual welcome to all the new faces around here. Since I've published my guide to Obsidian.md markdown I've seen 10% growth in the Round Up Subscribers week-to-week. The amount of visitors my website gets has also increased by 100% since I published that silly guide. If you're…

[PrevPREVIOUSHow to quickly create new habits (or break old ones)](https://rossgriffin.com/the-round-up/how-to-quickly-create-new-habits-or-break-old-ones/)

[NEXTHow to find your passionNext](https://rossgriffin.com/the-round-up/how-to-find-your-passion/)

### Table of Contents

-   [Basic Text formatting](https://rossgriffin.com/tutorials/obsidian-basics-guide/#basic-text-formatting)
    
-   [Headings and Horizontal Rules](https://rossgriffin.com/tutorials/obsidian-basics-guide/#headings-and-horizontal-rules)
    
-   [Lists and Checklists](https://rossgriffin.com/tutorials/obsidian-basics-guide/#lists-and-checklists)
    
-   [Code Blocks](https://rossgriffin.com/tutorials/obsidian-basics-guide/#code-blocks)
    
-   [Tables](https://rossgriffin.com/tutorials/obsidian-basics-guide/#tables)
    
-   [Footnotes](https://rossgriffin.com/tutorials/obsidian-basics-guide/#footnotes)
    
-   [Linking](https://rossgriffin.com/tutorials/obsidian-basics-guide/#linking)
    
-   [Embedding content](https://rossgriffin.com/tutorials/obsidian-basics-guide/#embedding-content)
    
    -   [Embedding images and other file types.](https://rossgriffin.com/tutorials/obsidian-basics-guide/#embedding-images-and-other-file-types)
        
-   [Queries and Search](https://rossgriffin.com/tutorials/obsidian-basics-guide/#queries-and-search)
    
-   [Adding Meta Data](https://rossgriffin.com/tutorials/obsidian-basics-guide/#adding-meta-data)
    
-   [Other Obsidian Content](https://rossgriffin.com/tutorials/obsidian-basics-guide/#other-obsidian-content)