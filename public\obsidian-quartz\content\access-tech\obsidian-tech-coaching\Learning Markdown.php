<?php
// Auto-generated blog post
// Source: content\access-tech\obsidian-tech-coaching\Learning Markdown.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'What is Markdown?[](https://www.markdownguide.org/getting-started/#what-is-markdown)';
$meta_description = 'Learn Markdown. Use Markdown to format every written assignment.  Getting Started | Markdown Guidehttps://www.markdownguide.org/getting-started/what-i...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'What is Markdown?[](https://www.markdownguide.org/getting-started/#what-is-markdown)',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'Learn Markdown. Use Markdown to format every written assignment.  Getting Started | Markdown Guidehttps://www.markdownguide.org/getting-started/what-i...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\obsidian-tech-coaching\\Learning Markdown.md',
);

// Post content
$post_content = '<p>Learn Markdown. Use Markdown to format every written assignment.</p>
<p><a href="https://www.markdownguide.org/getting-started/#what-is-markdown" class="external-link">Getting Started | Markdown Guide</a></p>
<h2>What is Markdown?[](https://www.markdownguide.org/getting-started/#what-is-markdown)</h2>
<p>Markdown is a lightweight markup language that you can use to add formatting elements to plaintext text documents. Created by <a href="https://daringfireball.net/projects/markdown/" class="external-link">John Gruber</a> in 2004, Markdown is now one of the world’s most popular markup languages.</p>
<p>Using Markdown is different than using a <a href="https://en.wikipedia.org/wiki/WYSIWYG" class="external-link">WYSIWYG</a> editor. In an application like Microsoft Word, you click buttons to format words and phrases, and the changes are visible immediately. Markdown isn’t like that. When you create a Markdown-formatted file, you add Markdown syntax to the text to indicate which words and phrases should look different.</p>
<p>For example, to denote a heading, you add a number sign before it (e.g., `# Heading One`). Or to make a phrase bold, you add two asterisks before and after it (e.g., `<strong>this text is bold</strong>`). It may take a while to get used to seeing Markdown syntax in your text, especially if you’re accustomed to WYSIWYG applications. The screenshot below shows a Markdown file displayed in the <a href="https://www.markdownguide.org/tools/vscode/" class="external-link">Visual Studio Code text editor</a>.</p>
<p>!<a href="https://mdg.imgix.net/assets/images/vscode.png" class="external-link">Markdown file in the Visual Studio Code text editor</a></p>
<p>You can add Markdown formatting elements to a plaintext file using a text editor application. Or you can use one of the many Markdown applications for macOS, Windows, Linux, iOS, and Android operating systems. There are also several web-based applications specifically designed for writing in Markdown.</p>
<p>Depending on the application you use, you may not be able to preview the formatted document in real time. But that’s okay. <a href="https://daringfireball.net/projects/markdown/" class="external-link">According to Gruber</a>, Markdown syntax is designed to be readable and unobtrusive, so the text in Markdown files can be read even if it isn’t rendered.</p>
<p>> The overriding design goal for Markdown’s formatting syntax is to make it as readable as possible. The idea is that a Markdown-formatted document should be publishable as-is, as plain text, without looking like it’s been marked up with tags or formatting instructions.</p>
<h2>Why Use Markdown?[](https://www.markdownguide.org/getting-started/#why-use-markdown)</h2>
<p>You might be wondering why people use Markdown instead of a WYSIWYG editor. Why write with Markdown when you can press buttons in an interface to format your text? As it turns out, there are a couple different reasons why people use Markdown instead of WYSIWYG editors.</p>
<p>-   Markdown can be used for everything. People use it to create <a href="https://www.markdownguide.org/getting-started/#websites" class="external-link">websites</a>, <a href="https://www.markdownguide.org/getting-started/#documents" class="external-link">documents</a>, <a href="https://www.markdownguide.org/getting-started/#notes" class="external-link">notes</a>, <a href="https://www.markdownguide.org/getting-started/#books" class="external-link">books</a>, <a href="https://www.markdownguide.org/getting-started/#presentations" class="external-link">presentations</a>, <a href="https://www.markdownguide.org/getting-started/#email" class="external-link">email messages</a>, and <a href="https://www.markdownguide.org/getting-started/#documentation" class="external-link">technical documentation</a>.
    
-   Markdown is portable. Files containing Markdown-formatted text can be opened using virtually any application. If you decide you don’t like the Markdown application you’re currently using, you can import your Markdown files into another Markdown application. That’s in stark contrast to word processing applications like Microsoft Word that lock your content into a proprietary file format.
    
-   Markdown is platform independent. You can create Markdown-formatted text on any device running any operating system.
    
-   Markdown is future proof. Even if the application you’re using stops working at some point in the future, you’ll still be able to read your Markdown-formatted text using a text editing application. This is an important consideration when it comes to books, university theses, and other milestone documents that need to be preserved indefinitely.
    
-   Markdown is everywhere. Websites like <a href="https://www.markdownguide.org/tools/reddit/" class="external-link">Reddit</a> and GitHub support Markdown, and lots of desktop and web-based applications support it.</p>
<h2>Kicking the Tires[](https://www.markdownguide.org/getting-started/#kicking-the-tires)</h2>
<p>The best way to get started with Markdown is to use it. That’s easier than ever before thanks to a variety of free tools.</p>
<p>You don’t even need to download anything. There are several online Markdown editors that you can use to try writing in Markdown. <a href="https://dillinger.io/" class="external-link">Dillinger</a> is one of the best online Markdown editors. Just open the site and start typing in the left pane. A preview of the rendered document appears in the right pane.</p>
<p>!<a href="https://mdg.imgix.net/assets/images/dillinger.png" class="external-link">Dillinger Markdown editor</a></p>
<p>You’ll probably want to keep the Dillinger website open as you read through this guide. That way you can try the syntax as you learn about it. After you’ve become familiar with Markdown, you may want to use a Markdown application that can be installed on your desktop computer or mobile device.</p>
<h2>How Does it Work?[](https://www.markdownguide.org/getting-started/#how-does-it-work)</h2>
<p>Dillinger makes writing in Markdown easy because it hides the stuff happening behind the scenes, but it’s worth exploring how the process works in general.</p>
<p>When you write in Markdown, the text is stored in a plaintext file that has an `.md` or `.markdown` extension. But then what? How is your Markdown-formatted file converted into HTML or a print-ready document?</p>
<p>The short answer is that you need a _Markdown application_ capable of processing the Markdown file. There are lots of applications available — everything from simple scripts to desktop applications that look like Microsoft Word. Despite their visual differences, all of the applications do the same thing. Like Dillinger, they all convert Markdown-formatted text to HTML so it can be displayed in web browsers.</p>
<p>Markdown applications use something called a _Markdown processor_ (also commonly referred to as a “parser” or an “implementation”) to take the Markdown-formatted text and output it to HTML format. At that point, your document can be viewed in a web browser or combined with a style sheet and printed. You can see a visual representation of this process below.</p>
<p> <strong>Note:</strong> The Markdown application and processor are two separate components. For the sake of brevity, I\'ve combined them into one element ("Markdown app") in the figure below.</p>
<p>!<a href="https://mdg.imgix.net/assets/images/markdown-flowchart.png" class="external-link">The Markdown Process</a></p>
<p>To summarize, this is a four-part process:</p>
<p>1.  Create a Markdown file using a text editor or a dedicated Markdown application. The file should have an `.md` or `.markdown` extension.
2.  Open the Markdown file in a Markdown application.
3.  Use the Markdown application to convert the Markdown file to an HTML document.
4.  View the HTML file in a web browser or use the Markdown application to convert it to another file format, like PDF.</p>
<p>From your perspective, the process will vary somewhat depending on the application you use. For example, Dillinger essentially combines steps 1-3 into a single, seamless interface — all you have to do is type in the left pane and the rendered output magically appears in the right pane. But if you use other tools, like a text editor with a static website generator, you’ll find that the process is much more visible.</p>
<h2>What’s Markdown Good For?[](https://www.markdownguide.org/getting-started/#whats-markdown-good-for)</h2>
<p>Markdown is a fast and easy way to take notes, create content for a website, and produce print-ready documents.</p>
<p>It doesn’t take long to learn the Markdown syntax, and once you know how to use it, you can write using Markdown just about everywhere. Most people use Markdown to create content for the web, but Markdown is good for formatting everything from email messages to grocery lists.</p>
<p>Here are some examples of what you can do with Markdown.</p>
<h3>Websites[](https://www.markdownguide.org/getting-started/#websites)</h3>
<p>Markdown was designed for the web, so it should come as no surprise that there are plenty of applications specifically designed for creating website content.</p>
<p>If you’re looking for the simplest possible way to create a website with Markdown files, check out <a href="https://blot.im/" class="external-link">blot.im</a>. After you sign up for Blot, it creates a Dropbox folder on your computer. Just drag and drop your Markdown files into the folder and — poof! — they’re on your website. It couldn’t be easier.</p>
<p>If you’re familiar with HTML, CSS, and version control, check out <a href="https://www.markdownguide.org/tools/jekyll/" class="external-link">Jekyll</a>, a popular static site generator that takes Markdown files and builds an HTML website. One advantage to this approach is that <a href="https://www.markdownguide.org/tools/github-pages/" class="external-link">GitHub Pages</a> provides free hosting for Jekyll-generated websites. If Jekyll isn’t your cup of tea, just pick one of the <a href="https://jamstack.org/generators/" class="external-link">many other static site generators available</a>.</p>
<p> <strong>Note:</strong> I used Jekyll to create the _Markdown Guide_. You can view the source code on <a href="https://github.com/mattcone/markdown-guide" class="external-link">GitHub</a>.</p>
<p>If you’d like to use a content management system (CMS) to power your website, take a look at <a href="https://www.markdownguide.org/tools/ghost/" class="external-link">Ghost</a>. It’s a free and open-source blogging platform with a nice Markdown editor. If you’re a WordPress user, you’ll be happy to know there’s <a href="https://wordpress.com/support/wordpress-editor/blocks/markdown-block/" class="external-link">Markdown support</a> for websites hosted on WordPress.com. Self-hosted WordPress sites can use the <a href="https://jetpack.com/support/markdown/" class="external-link">Jetpack plugin</a>.</p>
<h3>Documents[](https://www.markdownguide.org/getting-started/#documents)</h3>
<p>Markdown doesn’t have all the bells and whistles of word processors like Microsoft Word, but it’s good enough for creating basic documents like assignments and letters. You can use a Markdown document authoring application to create and export Markdown-formatted documents to PDF or HTML file format. The PDF part is key, because once you have a PDF document, you can do anything with it — print it, email it, or upload it to a website.</p>
<p>Here are some Markdown document authoring applications I recommend:</p>
<p>-   <strong>Mac:</strong> <a href="https://www.markdownguide.org/tools/macdown/" class="external-link">MacDown</a>, <a href="https://www.markdownguide.org/tools/ia-writer/" class="external-link">iA Writer</a>, or <a href="https://www.markdownguide.org/tools/marked-2/" class="external-link">Marked 2</a>
-   <strong>iOS / Android:</strong> <a href="https://www.markdownguide.org/tools/ia-writer/" class="external-link">iA Writer</a>
-   <strong>Windows:</strong> <a href="https://wereturtle.github.io/ghostwriter/" class="external-link">ghostwriter</a> or <a href="https://markdownmonster.west-wind.com/" class="external-link">Markdown Monster</a>
-   <strong>Linux:</strong> <a href="https://github.com/retext-project/retext" class="external-link">ReText</a> or <a href="https://wereturtle.github.io/ghostwriter/" class="external-link">ghostwriter</a>
-   <strong>Web:</strong> <a href="https://www.markdownguide.org/tools/dillinger/" class="external-link">Dillinger</a> or <a href="https://www.markdownguide.org/tools/stackedit/" class="external-link">StackEdit</a></p>
<p> <strong>Tip:</strong> <a href="https://ia.net/writer/templates/" class="external-link">iA Writer</a> provides templates for previewing, printing, and exporting Markdown-formatted documents. For example, the "Academic – MLA Style" template indents paragraphs and adds double sentence spacing.</p>
<h3>Notes[](https://www.markdownguide.org/getting-started/#notes)</h3>
<p>In nearly every way, Markdown is the ideal syntax for taking notes. Sadly, <a href="https://evernote.com/" class="external-link">Evernote</a> and <a href="https://www.onenote.com/" class="external-link">OneNote</a>, two of the most popular note applications, don’t currently support Markdown. The good news is that several other note applications _do_ support Markdown:</p>
<p>-   <a href="https://www.markdownguide.org/tools/obsidian/" class="external-link">Obsidian</a> is a popular Markdown note-taking application loaded with features.
-   <a href="https://www.markdownguide.org/tools/simplenote/" class="external-link">Simplenote</a> is a free, barebones note-taking application available for every platform.
-   <a href="https://www.markdownguide.org/tools/notable/" class="external-link">Notable</a> is a note-taking application that runs on a variety of platforms.
-   <a href="https://www.markdownguide.org/tools/bear/" class="external-link">Bear</a> is an Evernote-like application available for Mac and iOS devices. It doesn’t exclusively use Markdown by default, but you can enable Markdown compatibility mode.
-   <a href="https://www.markdownguide.org/tools/joplin/" class="external-link">Joplin</a> is a note taking application that respects your privacy. It’s available for every platform.
-   <a href="https://www.markdownguide.org/tools/boostnote/" class="external-link">Boostnote</a> bills itself as an “open source note-taking app designed for programmers.”</p>
<p>If you can’t part with Evernote, check out <a href="https://marxi.co/" class="external-link">Marxico</a>, a subscription-based Markdown editor for Evernote, or use <a href="https://www.markdownguide.org/tools/markdown-here/" class="external-link">Markdown Here</a> with the Evernote website.</p>
<h3>Books[](https://www.markdownguide.org/getting-started/#books)</h3>
<p>Looking to self-publish a novel? Try <a href="https://leanpub.com/" class="external-link">Leanpub</a>, a service that takes your Markdown-formatted files and turns them into an electronic book. Leanpub outputs your book in PDF, EPUB, and MOBI file format. If you’d like to create paperback copies of your book, you can upload the PDF file to another service such as <a href="https://kdp.amazon.com/" class="external-link">Kindle Direct Publishing</a>. To learn more about writing and self-publishing a book using Markdown, read <a href="https://medium.com/techspiration-ideas-making-it-happen/how-i-wrote-and-published-my-novel-using-only-open-source-tools-5cdfbd7c00ca" class="external-link">this blog post</a>.</p>
<h3>Presentations[](https://www.markdownguide.org/getting-started/#presentations)</h3>
<p>Believe it or not, you can generate presentations from Markdown-formatted files. Creating presentations in Markdown takes a little getting used to, but once you get the hang of it, it’s a lot faster and easier than using an application like PowerPoint or Keynote. <a href="https://remarkjs.com/" class="external-link">Remark</a> (<a href="https://github.com/gnab/remark" class="external-link">GitHub project</a>) is a popular browser-based Markdown slideshow tool, as are <a href="https://jdan.github.io/cleaver/" class="external-link">Cleaver</a> (<a href="https://github.com/jdan/cleaver" class="external-link">GitHub project</a>) and <a href="https://marp.app/" class="external-link">Marp</a> (<a href="https://github.com/marp-team/marp" class="external-link">GitHub project</a>). If you use a Mac and would prefer to use an application, check out <a href="https://www.decksetapp.com/" class="external-link">Deckset</a> or <a href="https://hyperdeck.io/" class="external-link">Hyperdeck</a>.</p>
<h3>Email[](https://www.markdownguide.org/getting-started/#email)</h3>
<p>If you send a lot of email and you’re tired of the formatting controls available on most email provider websites, you’ll be happy to learn there’s an easy way to write email messages using Markdown. <a href="https://www.markdownguide.org/tools/markdown-here/" class="external-link">Markdown Here</a> is a free and open-source browser extension that converts Markdown-formatted text into HTML that’s ready to send.</p>
<h3>Collaboration[](https://www.markdownguide.org/getting-started/#collaboration)</h3>
<p>Collaboration and team messaging applications are a popular way of communicating with coworkers and friends at work and home. These applications don’t utilize all of Markdown’s features, but the features they do provide are fairly useful. For example, the ability to bold and italicize text without using the WYSIWYG interface is pretty handy. <a href="https://www.markdownguide.org/tools/slack/" class="external-link">Slack</a>, <a href="https://www.markdownguide.org/tools/discord/" class="external-link">Discord</a>, <a href="https://www.markdownguide.org/tools/wiki-js/" class="external-link">Wiki.js</a>, and <a href="https://www.markdownguide.org/tools/mattermost/" class="external-link">Mattermost</a> are all good collaboration applications.</p>
<h3>Documentation[](https://www.markdownguide.org/getting-started/#documentation)</h3>
<p>Markdown is a natural fit for technical documentation. Companies like GitHub are increasingly switching to Markdown for their documentation — check out their <a href="https://github.com/blog/1939-how-github-uses-github-to-document-github" class="external-link">blog post</a> about how they migrated their Markdown-formatted documentation to <a href="https://www.markdownguide.org/tools/jekyll/" class="external-link">Jekyll</a>. If you write documentation for a product or service, take a look at these handy tools:</p>
<p>-   <a href="https://readthedocs.org/" class="external-link">Read the Docs</a> can generate a documentation website from your open source Markdown files. Just connect your GitHub repository to their service and push — Read the Docs does the rest. They also have a <a href="https://readthedocs.com/" class="external-link">service for commercial entities</a>.
-   <a href="https://www.markdownguide.org/tools/mkdocs/" class="external-link">MkDocs</a> is a fast and simple static site generator that’s geared towards building project documentation. Documentation source files are written in Markdown and configured with a single YAML configuration file. MkDocs has several <a href="https://www.mkdocs.org/user-guide/styling-your-docs/" class="external-link">built in themes</a>, including a port of the <a href="https://readthedocs.org/" class="external-link">Read the Docs</a> documentation theme for use with MkDocs. One of the newest themes is <a href="https://squidfunk.github.io/mkdocs-material/" class="external-link">MkDocs Material</a>.
-   <a href="https://www.markdownguide.org/tools/docusaurus/" class="external-link">Docusaurus</a> is a static site generator designed exclusively for creating documentation websites. It supports translations, search, and versioning.
-   <a href="https://vuepress.vuejs.org/" class="external-link">VuePress</a> is a static site generator powered by <a href="https://vuejs.org/" class="external-link">Vue</a> and optimized for writing technical documentation.
-   <a href="https://www.markdownguide.org/tools/jekyll/" class="external-link">Jekyll</a> was mentioned earlier in the section on websites, but it’s also a good option for generating a documentation website from Markdown files. If you go this route, be sure to check out the <a href="https://idratherbewriting.com/documentation-theme-jekyll/" class="external-link">Jekyll documentation theme</a>.</p>
<h2>Flavors of Markdown[](https://www.markdownguide.org/getting-started/#flavors-of-markdown)</h2>
<p>One of the most confusing aspects of using Markdown is that practically every Markdown application implements a slightly different version of Markdown. These variants of Markdown are commonly referred to as _flavors_. It’s your job to master whatever flavor of Markdown your application has implemented.</p>
<p>To wrap your head around the concept of Markdown flavors, it might help to think of them as language dialects. People in New York City speak English just like the people in London, but there are substantial differences between the dialects used in both cities. The same is true for people using different Markdown applications. Using <a href="https://www.markdownguide.org/tools/dillinger/" class="external-link">Dillinger</a> to write with Markdown is a vastly different experience than using <a href="https://www.markdownguide.org/tools/ulysses/" class="external-link">Ulysses</a>.</p>
<p>Practically speaking, this means you never know exactly what a company means when they say they support “Markdown.” Are they talking about only the <a href="https://www.markdownguide.org/basic-syntax/" class="external-link">basic syntax elements</a>, or all of the basic and <a href="https://www.markdownguide.org/extended-syntax/" class="external-link">extended syntax elements</a> combined, or some arbitrary combination of syntax elements? You won’t know until you read the documentation or start using the application.</p>
<p>If you’re just starting out, the best advice I can give you is to pick a Markdown application with good Markdown support. That’ll go a long way towards maintaining the portability of your Markdown files. You might want to store and use your Markdown files in other applications, and to do that you need to start with an application that provides good support. You can use the <a href="https://www.markdownguide.org/tools/" class="external-link">tool directory</a> to find an application that fits the bill.</p>
<h2>Additional Resources[](https://www.markdownguide.org/getting-started/#additional-resources)</h2>
<p>There are lots of resources you can use to learn Markdown. Here are some other introductory resources:</p>
<p>-   <a href="https://daringfireball.net/projects/markdown/" class="external-link">John Gruber’s Markdown documentation</a>. The original guide written by the creator of Markdown.
-   <a href="https://www.markdowntutorial.com/" class="external-link">Markdown Tutorial</a>. An open source website that allows you to try Markdown in your web browser.
-   <a href="https://github.com/mundimark/awesome-markdown" class="external-link">Awesome Markdown</a>. A list of Markdown tools and learning resources.
-   <a href="https://dave.autonoma.ca/blog/2019/05/22/typesetting-markdown-part-1" class="external-link">Typesetting Markdown</a>. A multi-part series that describes an ecosystem for typesetting Markdown documents using <a href="https://pandoc.org/" class="external-link">pandoc</a> and <a href="https://www.contextgarden.net/" class="external-link">ConTeXt</a>.</p>
<p><a href="https://mdg.imgix.net/assets/images/book-cover.jpg" class="external-link">![Markdown Guide book cover</a>](https://www.markdownguide.org/book/)</p>
<p>##### Take your Markdown skills to the next level.</p>
<p>Learn Markdown in 60 pages. Designed for both novices and experts, _The Markdown Guide_ book is a comprehensive reference that has everything you need to get started and master Markdown syntax.</p>
<p><a href="https://www.markdownguide.org/book/" class="external-link">Get the Book</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>