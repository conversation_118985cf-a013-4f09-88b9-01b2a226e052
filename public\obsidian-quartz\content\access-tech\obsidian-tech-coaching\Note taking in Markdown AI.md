I'd to share a way of taking notes assisted with computer intelligence. Notetaking with <PERSON><PERSON> has changed the way I write and learn, and every aspect of my life for the better. It's completely private, and local to your own computer, is extremely lightweight an extremely low amount of computer bandwidth, and is one of the most time-proofed methods of preserving your notes. 

In this note, I have screen captures, embedded youtube videos, direct excerpts of content from websites with every bit of the hypertext markup preserved. I'm using one of many free and great quality programs for taking notes in Markdown, this one is called Obsidian. It is very popular and the best rated free software for note management. Available on any operating system, works great on a mobile device. 

Another great one that's free and better for collaboration and note sharing among a team is Notion.so. There are tons of free tutorials online for both. They are already easy to use out of the box, but there are many extensions and community plugins available to get that offer more utility from the software. Some of these programs are open-source, and have community-driven development from people who really believe in these softwares.

Text Editors are very different to a Word Processor, like Microsoft Word, but both are used to compile text. But in very different ways, that will compliment or effect the quality of your work in the other. They are nothing new. A few examples of these are Notepad, LaTeX, and Visual Studio code, which I'll share a little about each of those. Just understanding what each of those programs is used for will give context to the power of modern Text Editors and Note Management Systems. None of these are necessary to know, to start using a Note Manager, but you will be glad to have a basic understanding of these.

Notepad most computer users have come into contact with on occasion. It's to create raw text dumps in the form of .txt files. Sometimes softwares will come with a readme.txt with helpful instructions and information from the developers. Unlike Microsoft Word, you are not doing anything to the raw text. 

Notepad is just storing text. And you can store a lot of text. Imagine hundreds of thousands of lines of text, and it not crashing or even causing bandwidth demands on your computer. I have had word documents with less than 100 pages, and not many pictures, crash my computer's RAM use. The text is stored in 8 byte binary encoding, which is as raw that text can be stored to a memory hard drive.

LaTeX is a software that has been around almost as long as Microsoft Word. Both came out in the 1980s, about three years apart. They have both been through decades of development. What LaTex is known for is having templates for documents like manuscripts, school worksheets, math textbooks, to make the material look really nice. But the data that makes the textbooks is easily stored as raw text too. LaTeX is the software that interprets and compiles that raw text into a really nice looking presentation of information. If you were proficient in LaTeX in the 2000's, you could be very invaluable to a school department, being able to make awesome curriculum materials for other teachers to use. 

