Since this and discussion board #1 have almost identical word-for-word prompts, I am going to take a fun creative liberty with this one. 

Since 2022, I have been an avid user of the free software called Obsidian.md. This is a very smart Note Management tool that let's you take frictionless notes, completely private and local to your device. There is a great community behind this software, with tons of plug ins extending what you can do. I have a huge vault of everything in my life, from the personal to the private. Last year I started separating the two (public vs private) so I can focus on publishing the public part to the internet. Obsidian comes with a paid premium feature called Publish, where you pay eight dollars a month to put notes of your choosing on a personal site. 

I highly encourage this program for anyone who takes notes, especially students, and especially teachers. It's rare that a computer program can elevate my quality of life and effectiveness as a person as well as Obsidian can. There are tons of tutorials on Youtube made by users of the tool, my favorite being <PERSON>. 

```embed
title: "How to get started with Obsidian in 2022 - from scratch!"
image: "https://i.ytimg.com/vi/OUrOfIqvGS4/maxresdefault.jpg"
description: "Obsidian is a free note-taking app that has recently exploded in popularity. In this video, I show you the basics of getting started with <PERSON>bs<PERSON>: how to in…"
url: "https://www.youtube.com/watch?v=OUrOfIqvGS4"
```

This is the first of a few posts in this thread. Don't worry, this is coming back to Mass Communication in a really cool way! 🙃 





Because I have been using this software for three years, and i write a lot, I have a lot of notes. Some are completed, some are half-baked. There are a few dozen that relate to Mass Communication. I keep most items well-organized so I can find relevant items pretty easily. I've already been turning several of my collections into websites, and even web applications. 

But the value promise of this is that, you can plot out your brain onto a note taker, and do what you want with that content. Everything exists as text files saved privately on your computer, and are not shared with anyone unless you personally share them somehow. 

So for this, and another course on Digital Marketing, I have been working on Publishing notes online. It's buggy, because I do have to code and program this to work. But I do have a live rough draft. It contains about 40 refined writings on Mass Communication, and what will be my Business Marketing Campaign. That is also Mass Communication in practice. 

I am using a free publishing tool to avoid paying eight dollars a month, called Quartz. This is just a more difficult way to do the same thing for free. You can find examples of these linked note sites both on the page for Obsidian Publish, and the one for Quartz. It's really fun to see what other people do with the technology. The community behind this is very friendly and helpful, and many of the sites are what I call 'Nerddom' collections (people infodumping into their own personal Wikipedia on things they find cool and interesting.)

If you check out my site, and get page errors clicking on links, try adding '.html' at the end of the website URL. 

I am working on figuring out the best way to add images, and PDF file downloads. Over the Summer I want to get into my journaling mode more and make a collection about myself to gift to a young person in my life for if and when I pass away. 



# Obsidian FAQ for Complete Beginners

## 1. What is Obsidian.md and why should I use it?

Obsidian is a free note-taking app that stores your notes as local Markdown files on your device. Unlike traditional note apps, Obsidian excels at connecting ideas through linked notes, essentially creating your personal knowledge management system. It's particularly valuable for students, teachers, researchers, and anyone who takes lots of notes and wants to see connections between ideas.

## 2. Is Obsidian difficult to learn?

While Obsidian has powerful features, its basic functionality is quite simple. You can start by just typing notes in plain text. As you get comfortable, you can gradually explore features like Markdown formatting, linking notes, and plugins. The learning curve is gentle if you take it step by step.

## 3. What is Markdown and why does Obsidian use it?

Markdown is a lightweight markup language that uses simple syntax to format text. Obsidian uses Markdown because it's:

- Easy to learn
- Readable even as plain text
- Future-proof (plain text files never become obsolete)
- Portable across different platforms and applications

## 4. How do I format text in Obsidian?

Obsidian uses standard Markdown syntax:

- **Bold text**: `**bold**`
- _Italics_: `*italics*`
- ~~Strikethrough~~: `~~strikethrough~~`
- Highlight: `==highlight==`
- Headers: `# Heading 1`, `## Heading 2`, etc.
- Lists: Start lines with `-` or `1.`
- Checkboxes: `- [ ]` and `- [x]`

## 5. What are "linked notes" and how do they work?

Linked notes are Obsidian's standout feature. You can create connections between notes by using double brackets: `[[Note Name]]`. This creates a clickable link to that note. If the note doesn't exist yet, clicking the link will create it. Over time, this builds a network of connected ideas—like your personal Wikipedia.

## 6. What's the difference between linking and embedding?

- Linking (`[[Note Name]]`) creates a clickable reference to another note
- Embedding (`![[Note Name]]`) actually displays the content of the referenced note within your current note

Embedding is powerful because any changes to the original note automatically update everywhere it's embedded.

## 7. How do I organize my notes in Obsidian?

Obsidian offers multiple ways to organize:

- **Folders**: Traditional file organization
- **Tags**: Add `#tags` in your notes or in YAML frontmatter
- **Links**: Connect related notes with `[[links]]`
- **Graph View**: Visualize connections between your notes

Most users combine these approaches based on their needs. Remember that the power of Obsidian is in connecting ideas, not just filing them away.

## 8. What are plugins and which ones should I start with?

Plugins extend Obsidian's functionality. Some beginner-friendly core plugins to enable are:

- **Daily Notes**: Automatically creates dated notes
- **Templates**: Reuse common note structures
- **Backlinks**: See which notes link to your current note
- **Graph View**: Visualize your note connections

Community plugins worth exploring early:

- **Calendar**: Visual calendar interface for daily notes
- **Advanced Tables**: Makes working with tables easier
- **Dataview**: Query and display information across your vault

## 9. What is "Learning on Credit" and how does it relate to Obsidian?

"Learning on Credit" is a concept where you process information by translating it into your own words and organizing it within your knowledge system. When you summarize textbook chapters or lecture notes in Obsidian and link related concepts, you're building your "digital brain." The act of transcribing and connecting ideas helps cement your understanding of the material.

## 10. How can I publish or share my Obsidian notes?

There are several options:

- **Obsidian Publish**: Official paid service ($8/month) to publish selected notes online
- **Quartz**: Free alternative that requires some technical setup
- **Export as PDF/HTML**: For sharing individual notes
- **GitHub**: For those comfortable with version control systems

Remember that Obsidian stores notes as simple Markdown files, so they're easy to share across different platforms and with different applications.





## The Magic of Graph View: Your Notes Come Alive

Imagine seeing all your thoughts and ideas not as a list of files, but as a living, breathing network of connections. That's exactly what Obsidian's Graph View offers – a visual representation of your entire knowledge base that resembles either a neural network, a galaxy of stars, or perhaps a thriving bacteria culture under a microscope!

Each note in your vault appears as a node (a dot or circle), and every connection between notes shows up as a line connecting these nodes. The beauty of Graph View is how it reveals relationships you might never have noticed in a traditional folder structure.

You can customize this visualization in countless ways:

- Assign different colors to different categories of notes
- Adjust the size of nodes based on how many connections they have
- Filter to show only certain types of notes
- Change the strength of the "gravitational pull" between connected notes

The most mesmerizing feature might be the animate option, which brings your knowledge network to life – notes gently float and reposition themselves as if moved by invisible currents, clustering around common themes and separating from unrelated concepts.

As your note collection grows, your Graph View evolves into a unique fingerprint of your thinking. Some users find themselves building their notes specifically to create beautiful patterns in their graph, while others use it as a practical tool to identify isolated notes or discover surprising connections between seemingly unrelated ideas.

Whether you use it as a practical navigation tool or just enjoy watching your thoughts dance across the screen, Graph View transforms the sometimes mundane act of note-taking into something that feels almost magical.