<?php
// Auto-generated blog post
// Source: content\access-tech\obsidian-tech-coaching\Obsidian Quartz - My favorite thing this week.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Obsidian FAQ for Complete Beginners';
$meta_description = 'Since this and discussion board 1 have almost identical word-for-word prompts, I am going to take a fun creative liberty with this one.  Since 2022, I...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Obsidian FAQ for Complete Beginners',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'Since this and discussion board 1 have almost identical word-for-word prompts, I am going to take a fun creative liberty with this one.  Since 2022, I...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\obsidian-tech-coaching\\Obsidian Quartz - My favorite thing this week.md',
);

// Post content
$post_content = '<p>Since this and discussion board #1 have almost identical word-for-word prompts, I am going to take a fun creative liberty with this one.</p>
<p>Since 2022, I have been an avid user of the free software called Obsidian.md. This is a very smart Note Management tool that let\'s you take frictionless notes, completely private and local to your device. There is a great community behind this software, with tons of plug ins extending what you can do. I have a huge vault of everything in my life, from the personal to the private. Last year I started separating the two (public vs private) so I can focus on publishing the public part to the internet. Obsidian comes with a paid premium feature called Publish, where you pay eight dollars a month to put notes of your choosing on a personal site.</p>
<p>I highly encourage this program for anyone who takes notes, especially students, and especially teachers. It\'s rare that a computer program can elevate my quality of life and effectiveness as a person as well as Obsidian can. There are tons of tutorials on Youtube made by users of the tool, my favorite being Nicole Van de Hoeven.</p>
<p>```embed
title: "How to get started with Obsidian in 2022 - from scratch!"
image: "https://i.ytimg.com/vi/OUrOfIqvGS4/maxresdefault.jpg"
description: "Obsidian is a free note-taking app that has recently exploded in popularity. In this video, I show you the basics of getting started with Obsidian: how to in…"
url: "https://www.youtube.com/watch?v=OUrOfIqvGS4"
```</p>
<p>This is the first of a few posts in this thread. Don\'t worry, this is coming back to Mass Communication in a really cool way! 🙃</p>


<p>Because I have been using this software for three years, and i write a lot, I have a lot of notes. Some are completed, some are half-baked. There are a few dozen that relate to Mass Communication. I keep most items well-organized so I can find relevant items pretty easily. I\'ve already been turning several of my collections into websites, and even web applications.</p>
<p>But the value promise of this is that, you can plot out your brain onto a note taker, and do what you want with that content. Everything exists as text files saved privately on your computer, and are not shared with anyone unless you personally share them somehow.</p>
<p>So for this, and another course on Digital Marketing, I have been working on Publishing notes online. It\'s buggy, because I do have to code and program this to work. But I do have a live rough draft. It contains about 40 refined writings on Mass Communication, and what will be my Business Marketing Campaign. That is also Mass Communication in practice.</p>
<p>I am using a free publishing tool to avoid paying eight dollars a month, called Quartz. This is just a more difficult way to do the same thing for free. You can find examples of these linked note sites both on the page for Obsidian Publish, and the one for Quartz. It\'s really fun to see what other people do with the technology. The community behind this is very friendly and helpful, and many of the sites are what I call \'Nerddom\' collections (people infodumping into their own personal Wikipedia on things they find cool and interesting.)</p>
<p>If you check out my site, and get page errors clicking on links, try adding \'.html\' at the end of the website URL.</p>
<p>I am working on figuring out the best way to add images, and PDF file downloads. Over the Summer I want to get into my journaling mode more and make a collection about myself to gift to a young person in my life for if and when I pass away.</p>

<h1>Obsidian FAQ for Complete Beginners</h1>
<h2>1. What is Obsidian.md and why should I use it?</h2>
<p>Obsidian is a free note-taking app that stores your notes as local Markdown files on your device. Unlike traditional note apps, Obsidian excels at connecting ideas through linked notes, essentially creating your personal knowledge management system. It\'s particularly valuable for students, teachers, researchers, and anyone who takes lots of notes and wants to see connections between ideas.</p>
<h2>2. Is Obsidian difficult to learn?</h2>
<p>While Obsidian has powerful features, its basic functionality is quite simple. You can start by just typing notes in plain text. As you get comfortable, you can gradually explore features like Markdown formatting, linking notes, and plugins. The learning curve is gentle if you take it step by step.</p>
<h2>3. What is Markdown and why does Obsidian use it?</h2>
<p>Markdown is a lightweight markup language that uses simple syntax to format text. Obsidian uses Markdown because it\'s:</p>
<p>- Easy to learn
- Readable even as plain text
- Future-proof (plain text files never become obsolete)
- Portable across different platforms and applications</p>
<h2>4. How do I format text in Obsidian?</h2>
<p>Obsidian uses standard Markdown syntax:</p>
<p>- <strong>Bold text</strong>: `<strong>bold</strong>`
- _Italics_: `<em>italics</em>`
- ~~Strikethrough~~: `~~strikethrough~~`
- Highlight: `==highlight==`
- Headers: `# Heading 1`, `## Heading 2`, etc.
- Lists: Start lines with `-` or `1.`
- Checkboxes: `- [ ]` and `- [x]`</p>
<h2>5. What are "linked notes" and how do they work?</h2>
<p>Linked notes are Obsidian\'s standout feature. You can create connections between notes by using double brackets: `[[Note Name]]`. This creates a clickable link to that note. If the note doesn\'t exist yet, clicking the link will create it. Over time, this builds a network of connected ideas—like your personal Wikipedia.</p>
<h2>6. What\'s the difference between linking and embedding?</h2>
<p>- Linking (`[[Note Name]]`) creates a clickable reference to another note
- Embedding (`![[Note Name]]`) actually displays the content of the referenced note within your current note</p>
<p>Embedding is powerful because any changes to the original note automatically update everywhere it\'s embedded.</p>
<h2>7. How do I organize my notes in Obsidian?</h2>
<p>Obsidian offers multiple ways to organize:</p>
<p>- <strong>Folders</strong>: Traditional file organization
- <strong>Tags</strong>: Add `#tags` in your notes or in YAML frontmatter
- <strong>Links</strong>: Connect related notes with `[[links]]`
- <strong>Graph View</strong>: Visualize connections between your notes</p>
<p>Most users combine these approaches based on their needs. Remember that the power of Obsidian is in connecting ideas, not just filing them away.</p>
<h2>8. What are plugins and which ones should I start with?</h2>
<p>Plugins extend Obsidian\'s functionality. Some beginner-friendly core plugins to enable are:</p>
<p>- <strong>Daily Notes</strong>: Automatically creates dated notes
- <strong>Templates</strong>: Reuse common note structures
- <strong>Backlinks</strong>: See which notes link to your current note
- <strong>Graph View</strong>: Visualize your note connections</p>
<p>Community plugins worth exploring early:</p>
<p>- <strong>Calendar</strong>: Visual calendar interface for daily notes
- <strong>Advanced Tables</strong>: Makes working with tables easier
- <strong>Dataview</strong>: Query and display information across your vault</p>
<h2>9. What is "Learning on Credit" and how does it relate to Obsidian?</h2>
<p>"Learning on Credit" is a concept where you process information by translating it into your own words and organizing it within your knowledge system. When you summarize textbook chapters or lecture notes in Obsidian and link related concepts, you\'re building your "digital brain." The act of transcribing and connecting ideas helps cement your understanding of the material.</p>
<h2>10. How can I publish or share my Obsidian notes?</h2>
<p>There are several options:</p>
<p>- <strong>Obsidian Publish</strong>: Official paid service ($8/month) to publish selected notes online
- <strong>Quartz</strong>: Free alternative that requires some technical setup
- <strong>Export as PDF/HTML</strong>: For sharing individual notes
- <strong>GitHub</strong>: For those comfortable with version control systems</p>
<p>Remember that Obsidian stores notes as simple Markdown files, so they\'re easy to share across different platforms and with different applications.</p>


<h2>The Magic of Graph View: Your Notes Come Alive</h2>
<p>Imagine seeing all your thoughts and ideas not as a list of files, but as a living, breathing network of connections. That\'s exactly what Obsidian\'s Graph View offers – a visual representation of your entire knowledge base that resembles either a neural network, a galaxy of stars, or perhaps a thriving bacteria culture under a microscope!</p>
<p>Each note in your vault appears as a node (a dot or circle), and every connection between notes shows up as a line connecting these nodes. The beauty of Graph View is how it reveals relationships you might never have noticed in a traditional folder structure.</p>
<p>You can customize this visualization in countless ways:</p>
<p>- Assign different colors to different categories of notes
- Adjust the size of nodes based on how many connections they have
- Filter to show only certain types of notes
- Change the strength of the "gravitational pull" between connected notes</p>
<p>The most mesmerizing feature might be the animate option, which brings your knowledge network to life – notes gently float and reposition themselves as if moved by invisible currents, clustering around common themes and separating from unrelated concepts.</p>
<p>As your note collection grows, your Graph View evolves into a unique fingerprint of your thinking. Some users find themselves building their notes specifically to create beautiful patterns in their graph, while others use it as a practical tool to identify isolated notes or discover surprising connections between seemingly unrelated ideas.</p>
<p>Whether you use it as a practical navigation tool or just enjoy watching your thoughts dance across the screen, Graph View transforms the sometimes mundane act of note-taking into something that feels almost magical.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>