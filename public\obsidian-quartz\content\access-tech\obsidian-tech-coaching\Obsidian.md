https://notes.bryanjenks.dev/Inbox/podcasts/%25+2023-01-12+The+Influence+You+Have

https://github.com/kepano/obsidian-minimal?tab=readme-ov-file#installation

I don't know if these vaults meet your needs, but these are the best vaults/digital gardens I've found so far:

- [<PERSON><PERSON><PERSON> by <PERSON>](https://publish.obsidian.md/bryan-jenks/Z/INDEX).
    
- [Fork my brain by <PERSON>](https://walkintheforest.com/Content/%F0%9F%91%8B%F0%9F%8F%BD+Welcome)
    
- [louis030195 by <PERSON>](https://brain.louis030195.com/README)
    
- [joschua's Garden by <PERSON><PERSON><PERSON>](https://joschuasgarden.com/50+Slipbox/Welcome!)
    
- [justinmeader.com by <PERSON>](https://publish.obsidian.md/justin/Home)
    
- [LYT Kit by <PERSON>](https://notes.linkingyourthinking.com/Cards/The+forest+entrance)
    
- [My second-brain by <PERSON>](https://anthonyamar.fr/Welcome+in+my+mind+%F0%9F%A7%A0)
    
- [ruairimcnicholas by Ruairi McNicholas](https://ruairimcnicholas.com/0+Welcome)
    
- [angelinesoon.com by Angeline Soon](https://notes.angelinesoon.com/%F0%9F%8D%83_In+The+Wild_PublishedNotes/%2B%2B+Welcome)
    
- [The Quantum Well by Boris Stanchev](https://publish.obsidian.md/myquantumwell/Welcome+to+The+Quantum+Well!)
    
- [Mister chad by Chad Bennett](https://publish.obsidian.md/mister-chad/welcome)
    
- [SlRvb by SlRvb](https://publish.obsidian.md/slrvb/90+Site/SlRvb+Home)
    
- [Walk in the forest by Anthony Agbay](https://walkintheforest.com/Content/%F0%9F%91%8B%F0%9F%8F%BD+Welcome)
    

While several of the vaults I mentioned above are available for download, here are a couple of vault templates you may want to check out:

- [SlRvb's Journal Setup zip](https://www.dropbox.com/s/hq02du16bj0itls/BuJo%20Setup.zip?dl=0)
    
- [PARA Starter Kit v2](https://forum.obsidian.md/uploads/short-url/7TtSU9z2RNOJx481Urw37h6AoWE.zip). Small starter kit for Obsidian by [Cotemaxime](https://forum.obsidian.md/t/para-starter-kit/223)
    
- [Obsidian starter templates](https://github.com/masonlr/obsidian-starter-templates). Stater templates for Obsidian.
    
- [Obsidian JG method](https://github.com/joshwingreene/Obsidian-JG-Method). A starter kit that follow how [joshwingreene](https://github.com/joshwingreene) uses Obsidian to manage goals, tasks, notes and software development knowledge base.
    
- [Bible study in Obsidian kit](https://forum.obsidian.md/uploads/short-url/fK2XyRlWvpxGAuZJENEVUZzZUIh.zip). Starter kit by [Joschua](https://forum.obsidian.md/t/bible-study-in-obsidian-kit-including-the-bible-in-markdown/12503) designed to get you hands on with using Scripture in a connected way in your personal notes.
    
- [Obsidian weave](https://github.com/cuken/obsidian-weave). Obsidian vault template for software developers/managers working in the corporate world.
    
- [OSCP notes template](https://github.com/Twigonometry/OSCP-Notes-Template). A template Obsidian vault for storing your OSCP revision notes.
    
- [Obsidian table top templates](https://github.com/mProjectsCode/Obsidian-Table-Top-Templates). Table top/world building templates for Obsidian.
    
- [Lifelong learning system template](https://github.com/jrgilbertson/lifelong-learning-system-template). Getting started kit for a lifelong learning system with Obsidian and Anki,
    
- [My obsidian vault template](https://github.com/Envoy-VC/My-Obsisian-Vault-Template). A knowledge management template for obsidian, in which to grow your ideas.
    
- [Obsidian history vault](https://github.com/erazlogo/obsidian-history-vault). A starter vault for historical research with Zotero and Obsidian.
    
- [Obsidian beginner vault template](https://github.com/andrewmcodes/obsidian-beginner-vault-template). A minimal template for your first Obsidian vault aimed at setting good defaults.
    
- [Obsidian novel starter vault](https://github.com/rrbaker/obsidian-novel-starter-vault). A starter vault for writing novels and other long-form writing projects in Obsidian.
    
- [OB template](https://github.com/llZektorll/OB_Template). Obsidian reference for note templates focused on new users of the application using only core plugins.
    
- [Life disciplines projects](https://github.com/uwidev/life-disciplines-projects). Life-management framework built within Obsidian.
    
- [Obsidian note templates starter pack](https://johnmavrick.gumroad.com/l/obsidian-templates). 15+ templates for Obsidian MD.
    

And although these are not obsidian vaults, they are also great digital gardens:

- [My knowledge wiki by Nikita Voloboev](https://wiki.nikiv.dev/)
    
- [Andy's working notes by Andy Matuschak](https://notes.andymatuschak.org/About_these_notes)
    
- [maggieappleton.com by Maggie Appleton](https://maggieappleton.com/garden)
    

Here are also two curated lists of digital gardens so you may check more:

- [Second-Brain list](https://github.com/KasperZutterman/Second-Brain)
    
- [Digital Gardeners list](https://docs.google.com/spreadsheets/d/1KtEjnuZEHxUmoiA37_MMM4OFyQcbwVUaLBFa12P8cnU/edit#gid=0)
    

Last but not least, I leave you [this post](https://forum.obsidian.md/t/obsidian-gems-of-the-year-2021-nomination-workflows/28227) from the Obsidian forum with a tons of workflowse explained.

Hope it helps! :)