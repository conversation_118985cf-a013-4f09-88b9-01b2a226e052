<?php
// Auto-generated blog post
// Source: content\access-tech\obsidian-tech-coaching\Obsidian.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'https://notes.bryanjenks.dev/Inbox/podcasts/%25+2023-01-12+The+Influence+You+Have';
$meta_description = 'https://notes.bryanjenks.dev/Inbox/podcasts/%25+2023-01-12+The+Influence+You+Have https://github.com/kepano/obsidian-minimal?tab=readme-ov-fileinstall...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'https://notes.bryanjenks.dev/Inbox/podcasts/%25+2023-01-12+The+Influence+You+Have',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'https://notes.bryanjenks.dev/Inbox/podcasts/%25+2023-01-12+The+Influence+You+Have https://github.com/kepano/obsidian-minimal?tab=readme-ov-fileinstall...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\obsidian-tech-coaching\\Obsidian.md',
);

// Post content
$post_content = '<p>https://notes.bryanjenks.dev/Inbox/podcasts/%25+2023-01-12+The+Influence+You+Have</p>
<p>https://github.com/kepano/obsidian-minimal?tab=readme-ov-file#installation</p>
<p>I don\'t know if these vaults meet your needs, but these are the best vaults/digital gardens I\'ve found so far:</p>
<p>- <a href="https://publish.obsidian.md/bryan-jenks/Z/INDEX" class="external-link">Petrichor by Bryan Jenks</a>.
    
- <a href="https://walkintheforest.com/Content/%F0%9F%91%8B%F0%9F%8F%BD+Welcome" class="external-link">Fork my brain by Nicole van der Hoeven</a>
    
- <a href="https://brain.louis030195.com/README" class="external-link">louis030195 by Louis Beaumont</a>
    
- <a href="https://joschuasgarden.com/50+Slipbox/Welcome!" class="external-link">joschua\'s Garden by Joschua</a>
    
- <a href="https://publish.obsidian.md/justin/Home" class="external-link">justinmeader.com by Justin Meader</a>
    
- <a href="https://notes.linkingyourthinking.com/Cards/The+forest+entrance" class="external-link">LYT Kit by Nick Milo</a>
    
- <a href="https://anthonyamar.fr/Welcome+in+my+mind+%F0%9F%A7%A0" class="external-link">My second-brain by Anthony Amar</a>
    
- <a href="https://ruairimcnicholas.com/0+Welcome" class="external-link">ruairimcnicholas by Ruairi McNicholas</a>
    
- <a href="https://notes.angelinesoon.com/%F0%9F%8D%83_In+The+Wild_PublishedNotes/%2B%2B+Welcome" class="external-link">angelinesoon.com by Angeline Soon</a>
    
- <a href="https://publish.obsidian.md/myquantumwell/Welcome+to+The+Quantum+Well!" class="external-link">The Quantum Well by Boris Stanchev</a>
    
- <a href="https://publish.obsidian.md/mister-chad/welcome" class="external-link">Mister chad by Chad Bennett</a>
    
- <a href="https://publish.obsidian.md/slrvb/90+Site/SlRvb+Home" class="external-link">SlRvb by SlRvb</a>
    
- <a href="https://walkintheforest.com/Content/%F0%9F%91%8B%F0%9F%8F%BD+Welcome" class="external-link">Walk in the forest by Anthony Agbay</a></p>
<p>While several of the vaults I mentioned above are available for download, here are a couple of vault templates you may want to check out:</p>
<p>- <a href="https://www.dropbox.com/s/hq02du16bj0itls/BuJo%20Setup.zip?dl=0" class="external-link">SlRvb\'s Journal Setup zip</a>
    
- <a href="https://forum.obsidian.md/uploads/short-url/7TtSU9z2RNOJx481Urw37h6AoWE.zip" class="external-link">PARA Starter Kit v2</a>. Small starter kit for Obsidian by <a href="https://forum.obsidian.md/t/para-starter-kit/223" class="external-link">Cotemaxime</a>
    
- <a href="https://github.com/masonlr/obsidian-starter-templates" class="external-link">Obsidian starter templates</a>. Stater templates for Obsidian.
    
- <a href="https://github.com/joshwingreene/Obsidian-JG-Method" class="external-link">Obsidian JG method</a>. A starter kit that follow how <a href="https://github.com/joshwingreene" class="external-link">joshwingreene</a> uses Obsidian to manage goals, tasks, notes and software development knowledge base.
    
- <a href="https://forum.obsidian.md/uploads/short-url/fK2XyRlWvpxGAuZJENEVUZzZUIh.zip" class="external-link">Bible study in Obsidian kit</a>. Starter kit by <a href="https://forum.obsidian.md/t/bible-study-in-obsidian-kit-including-the-bible-in-markdown/12503" class="external-link">Joschua</a> designed to get you hands on with using Scripture in a connected way in your personal notes.
    
- <a href="https://github.com/cuken/obsidian-weave" class="external-link">Obsidian weave</a>. Obsidian vault template for software developers/managers working in the corporate world.
    
- <a href="https://github.com/Twigonometry/OSCP-Notes-Template" class="external-link">OSCP notes template</a>. A template Obsidian vault for storing your OSCP revision notes.
    
- <a href="https://github.com/mProjectsCode/Obsidian-Table-Top-Templates" class="external-link">Obsidian table top templates</a>. Table top/world building templates for Obsidian.
    
- <a href="https://github.com/jrgilbertson/lifelong-learning-system-template" class="external-link">Lifelong learning system template</a>. Getting started kit for a lifelong learning system with Obsidian and Anki,
    
- <a href="https://github.com/Envoy-VC/My-Obsisian-Vault-Template" class="external-link">My obsidian vault template</a>. A knowledge management template for obsidian, in which to grow your ideas.
    
- <a href="https://github.com/erazlogo/obsidian-history-vault" class="external-link">Obsidian history vault</a>. A starter vault for historical research with Zotero and Obsidian.
    
- <a href="https://github.com/andrewmcodes/obsidian-beginner-vault-template" class="external-link">Obsidian beginner vault template</a>. A minimal template for your first Obsidian vault aimed at setting good defaults.
    
- <a href="https://github.com/rrbaker/obsidian-novel-starter-vault" class="external-link">Obsidian novel starter vault</a>. A starter vault for writing novels and other long-form writing projects in Obsidian.
    
- <a href="https://github.com/llZektorll/OB_Template" class="external-link">OB template</a>. Obsidian reference for note templates focused on new users of the application using only core plugins.
    
- <a href="https://github.com/uwidev/life-disciplines-projects" class="external-link">Life disciplines projects</a>. Life-management framework built within Obsidian.
    
- <a href="https://johnmavrick.gumroad.com/l/obsidian-templates" class="external-link">Obsidian note templates starter pack</a>. 15+ templates for Obsidian MD.</p>
<p>And although these are not obsidian vaults, they are also great digital gardens:</p>
<p>- <a href="https://wiki.nikiv.dev/" class="external-link">My knowledge wiki by Nikita Voloboev</a>
    
- <a href="https://notes.andymatuschak.org/About_these_notes" class="external-link">Andy\'s working notes by Andy Matuschak</a>
    
- <a href="https://maggieappleton.com/garden" class="external-link">maggieappleton.com by Maggie Appleton</a></p>
<p>Here are also two curated lists of digital gardens so you may check more:</p>
<p>- <a href="https://github.com/KasperZutterman/Second-Brain" class="external-link">Second-Brain list</a>
    
- <a href="https://docs.google.com/spreadsheets/d/1KtEjnuZEHxUmoiA37_MMM4OFyQcbwVUaLBFa12P8cnU/edit#gid=0" class="external-link">Digital Gardeners list</a></p>
<p>Last but not least, I leave you <a href="https://forum.obsidian.md/t/obsidian-gems-of-the-year-2021-nomination-workflows/28227" class="external-link">this post</a> from the Obsidian forum with a tons of workflowse explained.</p>
<p>Hope it helps! :)</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>