Word Processor vs Text Editors
You can save a document as an HTML file on a Word Processor, but there are too many missing developer features for it to be useful in anyway.

Useful Tools:
- WYSIWYG Editors: Adobe Dreamweaver, Muse, Blue Griffin. Makes Bloated Code.
- Text Editors: VS Code, Sublime Text, Atom.
- File naming conventions: lowercase no spaces.
- HTML ignores white space, except for one space.
- Don't put comments before the DOCTYPE declaration. Mostly issue with Internet Explorer.

Setting up my workspace:
- eTextbook on one side of my screen.
- Obsidian for note taking and screen captures on the right.
- VS Code for coding matters.
- Considering getting a second monitor for more space.

