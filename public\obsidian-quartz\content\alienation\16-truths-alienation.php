<?php
// Auto-generated blog post
// Source: content\alienation\16-truths-alienation.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '16 Things I Would Tell Myself (While Alienated)';
$meta_description = 'Dealing with alienation, especially from family, is an incredibly painful experience. The emotional turmoil and confusion can feel overwhelming. Inspired by the insights of the Anti-Alienation Project, here are 16 truths to hold onto as you navigate this challenging journey.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '16 Things I Would Tell Myself (While Alienated)',
  'author' => 'The Anti-Alienation Project',
  'date' => '2024-07-25',
  'excerpt' => 'Dealing with alienation, especially from family, is an incredibly painful experience. The emotional turmoil and confusion can feel overwhelming. Inspired by the insights of the Anti-Alienation Project, here are 16 truths to hold onto as you navigate this challenging journey.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\alienation\\16-truths-alienation.md',
);

// Post content
$post_content = '<p><a href="https://www.youtube.com/watch?v=D_QLhJUzD5k" class="external-link">16 Things I Would I Tell Myself (While Alienated) - YouTube</a></p>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/D_QLhJUzD5k?si=lJn4g6Slro8XOyYh" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></p>
<h2>Navigating Alienation: 16 Truths to Tell Yourself</h2>
<p>Dealing with alienation, especially from family, is an incredibly painful experience. The emotional turmoil and confusion can feel overwhelming. Inspired by the insights of the Anti-Alienation Project, here are 16 truths to hold onto as you navigate this challenging journey:</p>
<p>1. The parent you believe is "bad" might actually love you. It\'s possible that their actions stem from their own unresolved issues or unhealthy patterns, not a lack of love for you.
    
2. Consider their efforts. If a parent continues to reach out, despite the distance, it could be a sign that they do care and want you in their life.
    
3. Document your experiences with each parent. Create separate lists of specific instances you\'ve directly seen or heard that caused you pain. Avoid hearsay. This factual evidence is crucial, especially when considering significant decisions like cutting off contact.
    
4. You are not defined by your parent\'s beliefs about you. Their negative perceptions do not dictate your worth or who you truly are.
    
5. Challenge your own thoughts and beliefs. Question what you\'ve been told and what you\'ve come to believe. Ensure your understanding is based on facts, not just ingrained narratives.
    
6. It\'s not okay for a parent to treat you as a best friend, therapist, or confidant. If you\'re consistently responsible for managing their emotions, establishing strong boundaries is a healthy and necessary step.
    
7. Reflect on your future. Consider how your current situation might impact your own future family. Would you treat your children the way you\'re being treated?
    
8. You are not broken. Your reactions – feeling anxious, confused, or on edge – are normal responses to difficult circumstances and unhealthy treatment. You are doing your best to survive a challenging reality.
    
9. Understand Cognitive Dissonance. The discomfort you feel when your positive memories of a family member clash with their hurtful actions is likely cognitive dissonance. It arises when your beliefs don\'t align with your real experiences.
    
10. Question the origins of your feelings and beliefs about the alienating parent. Where did these ideas truly come from? Are they based on your direct experiences or external influences?
    
11. Take control of your own life. Don\'t let the alienation dictate your choices and your future.
    
12. It\'s possible to love both parents, even amidst difficult circumstances. Your feelings are valid, regardless of the complexities of the situation.
    
13. The situation is not your fault. You are not responsible for the alienating behaviors of others.
    
14. This is incredibly difficult, and it\'s okay to acknowledge that. Keep the responsibility for the situation where it belongs – with those whose actions caused it. You had no control over this.
    
15. You are not defined by this experience, but your response to it will shape your future. You have the power to choose how you move forward.
    
16. Healing is a lifelong journey, not a quick fix. Be patient with yourself. Once you begin to understand the truth of your situation, you\'ll have the opportunity to build a fulfilling life aligned with your own values and purpose. Consider spending time alone in nature to reflect on the life you want to create.
    
Remember, you are not alone. There are many others out there who have lived through similar experiences and carry this. Acknowledging these truths is a powerful first step towards healing and reclaiming your life.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>