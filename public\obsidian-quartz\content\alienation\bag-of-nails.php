<?php
// Auto-generated blog post
// Source: content\alienation\bag-of-nails.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'A Bag of Nails';
$meta_description = 'A Bag of Nails - A. A. Chips';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'A Bag of Nails',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'A Bag of Nails - A. A. Chips',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\alienation\\bag-of-nails.md',
);

// Post content
$post_content = '<p><strong>A Bag of Nails</strong></p>
<p>There once was a young boy with a very bad temper. The boy’s father wanted to teach him a lesson, so he gave him a bag of nails and told him that every time he lost his temper he must hammer a nail into their wooden fence.</p>
<p>On the first day of this lesson, the little boy had driven 37 nails into the fence. He was really mad!</p>
<p>Over the course of the next few weeks, the little boy began to control his temper, so the number of nails that were hammered into the fence dramatically decreased.</p>
<p>It wasn’t long before the little boy discovered it was easier to hold his temper than to drive those nails into the fence.</p>
<p>Then, the day finally came when the little boy didn’t lose his temper even once, and he became so proud of himself he couldn’t wait to tell his father.</p>
<p>Pleased, his father suggested that he now pull out one nail for each day that he could hold his temper.</p>
<p>Several weeks went by and the day finally came when the young boy was able to tell his father that all the nails were gone.</p>
<p>Very gently, the father took his son by the hand and led him to the fence. “you have done very well, my son,” he smiled, “but look at the holes in the fence. The fence will never be the same.”</p>
<p>The little boy listened carefully as his father continued to speak.</p>
<p>“When you say things in anger, they leave permanent scars just like these. And no matter how many times you say you’re sorry, the wounds will still be there.”</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>