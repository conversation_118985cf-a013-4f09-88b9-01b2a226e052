<?php
// Auto-generated blog post
// Source: content\alienation\discord-minors-safety.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Connecting on Discord and Safety Tips for Minors';
$meta_description = 'Guidelines and safety practices for young people using Discord and other online platforms';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Connecting on Discord and Safety Tips for Minors',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'Guidelines and safety practices for young people using Discord and other online platforms',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\alienation\\discord-minors-safety.md',
);

// Post content
$post_content = '<p>I use Discord regularly as a communication platform (handle: `aprilaplcyr`), and I recognize its popularity among young people. As someone who administers an online support group for teens, adults, and parents—particularly within the autism community—I prioritize safety, transparency, and support. If welfare or safety concerns arise, I am a mandated reporter for abuse, neglect, and exploitation.</p>
<p>For minors using Discord, safety begins with open dialogue and collaboration between young users and their trusted adults. Discord offers built-in safety and privacy features, but these are only effective when users provide accurate information (like their real age) and when families work together to configure appropriate settings.</p>
<p>Parents and guardians can link their child’s account to monitor friends, messages, calls, and server participation while respecting the minor’s privacy. If trust or communication barriers prevent this collaboration, it may be helpful to seek additional support, as healthy online habits start with strong offline relationships.</p>
<p>Discord is a powerful tool for connection, but like any platform, it works best when used thoughtfully and with guidance.</p>
<p>Recommended Reading: https://www.safetydetectives.com/blog/is-discord-safe-for-kids/</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>