<?php
// Auto-generated blog post
// Source: content\climate\action-items.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Actually Actionable Items';
$meta_description = 'A collection of actually actionable items for climate action.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Actually Actionable Items',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'A collection of actually actionable items for climate action.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\climate\\action-items.md',
);

// Post content
$post_content = '<p><a href="https://www.reddit.com/r/solarpunk/comments/rlik91/actually_actionable_items/" class="external-link">https://www.reddit.com/r/solarpunk/comments/rlik91/actually_actionable_items/</a></p>

<h1><strong>Actually Actionable Items</strong></h1>
<p><a href="https://www.reddit.com/r/solarpunk/?f=flair_name%3A%22action%2FDIY%22" class="external-link">action/DIY</a></p>
<p>Hi friends! I wrote this list when I saw someone (<a href="https://www.reddit.com/user/powerspank/" class="external-link">u/powerspank</a>) ask about what individuals can do TODAY to be more solarpunk.</p>
<p>What are some things that you\'ve done to make the world a better place? things you actually have control over? I\'d love to add any suggestions to this list and help it keep growing and growing.</p>
<p><strong>Level One</strong></p>
<p>- Vote
    
- Remind other people to vote
    
- Always join an available union
    
- Never cross a picket line. Do not support businesses that have striking employees
    
- Cary a sharpie to deface fascist propaganda you find
    
- Stop buying fast fashion/ Buy second hand
    
- Research how your local area sorts recyclables
    
- Challenge yourself to cut down your trash output
    
- Go vegetarian/ vegan (or just consider meat-free meats sometimes, Impossible Beef is usually only slightly more expensive than normally priced beef.)
    
- If your city doesn\'t have recycle/composting, write them about it
    
- Donate goods to a thrift store instead of throwing them out
    
- See if there\'s a textile recycling facility around for anything ripped/not worth donating
    
- Wash your clothes less- it not only saves water, but also makes your clothes live longer
    
- Switch from cows milk to non-dairy milk (but be wary of almond milk, it\'s bad for bees)
    
- Research your local zoo, how they treat animals and who they donate to. Consider getting a zoo membership. It\'s good self care to walk around the zoo, and zoos always need the money
    
- Switch to more sustainable or compostable products where you can (toothbrushes, cat liter, etc)
    
- Avoid businesses like Walmart, Hobby Lobby, Chick-fil-A, Kelloggs, Nestle, etc
    
- Research your local land\'s Indigenous People
    
- Delete your Facebook
    
- Visit your favorite park/ beach/ roadway and pick up trash as you walk
    
- See if your area has a Fix-It-Fair, places where people skilled in repair volunteer their services for free and people bring in broken items
    
- Visit your local farmers market
    
- Check where your company sources products and suggest sustainable alternatives
    
- Talk to your coworkers, neighbors, and family about solarpunk values and how we can work together
    
- Leave room for ecological grieving. We are all stressed by simply living in this time period. Let yourself feel those emotions and release them</p>
<p><strong>Level Two</strong></p>
<p>- <a href="https://www.reddit.com/r/guerillagardening/" class="external-link">r/guerillagardening</a>
    
- Look into repair skills, like soldering, masonry patch-ups, mechanics, sewing, darning, etc. Then you can prioritize repairing items over replacing them
    
- <a href="https://www.reddit.com/r/visiblemending/" class="external-link">r/visiblemending</a>
    
- Phase out single-use items in your household (water bottles, straws, coffee cups, ziplocks, saran wrap etc)
    
- Consider cups or reusable pads for your menstrual cycle
    
- Learn to mend items so you can keep your clothes and other items longer
    
- Walk/Bike/Bus more</p>
<p><strong>Level Three</strong></p>
<p>- Donate to Indigenous Land Defenders and support them in-person when asked
    
- Leave notes in the grocery store for calls to action like boycotting Kelloggs or buying a re-usable keurig cup
    
- Try and organize a Fix-It-Fair. Start small, even just a sock darning party
    
- See if your company can encourage walking/biking to work with things like adding bike lockers for security
    
- Encourage your company to get free bus passes for employees
    
- Consider (and research!) companies like Loop or Imperfect Produce to reduce food and packaging waste.
    
- Consider (and research!) specialty recycling companies like Ridwell
    
- If you have some kind of pension or 401(k), ask your manager if they can include options for ESG investments/options divested from fossil fuel companies
    
- Switch from your bank to your local credit union
    
- Look into your work\'s recycling and composting habits. Try to start a recycling program if there is none in place. Remember there is also e-waste recycling
    
- Apply for jobs at businesses that have striking workers as a tactic to waste as much of the businesses time and resources as you can</p>
<p><strong>Level Four</strong></p>
<p>- Get involved with your local city/town politics, as little as just tuning into the Zoom meetings
    
- Volunteer at a senior center/ soup kitchen/ park/ anywhere
    
- Write to companies you do love, praise them for what they do well and ask them to do even better
    
- Apply to be a poll worker
    
- Join a community garden if you don\'t have space of your own to grow
    
- Contact a Union Organizer if your workspace doesn\'t have a union
    
- Talk to your union about a Green Ban
    
- Organize a strike! You and your coworkers are worth it!
    
- Set aside money for bail if your friend wants to sabotage a power plant
    
- Join your local MakerSpace
    
- Work with your local Food Not Bombs</p>
<p><strong>For Apartment Dwellers:</strong></p>
<p>- Join your tenants union
    
- If you cannot find one, research making one
    
- Send a professional email your landlord about solar panels
    
- Start a free "thrift store" in your laundry room. Make sure to clean it up regularly and throw out anything that\'s not worth taking home
    
- Start a community board/ Borrow Board for people to post things they want to borrow or other needs they have
    
- Start a food drive in your laundry room with a big cardboard box
    
- Put voting reminders on your mailbox wall for local and federal elections with due dates</p>
<p><strong>For Home Owners:</strong></p>
<p>- Put up a bird feeder
    
- Install solar panels
    
- Start a vegetable or bee garden in any free space you have
    
- Replace your grass lawn with clover
    
- Start a Little Free Library
    
- Install a microplastics filter in your dryer
    
- Install energy/water efficient appliances/shower heads
    
- Check your homes insulation! This can save a boat load of energy
    
- Replace all of the machines you own that burn fossil fuels with machines that don\'t (cars, stoves, heaters, etc)
    
- Go to town meetings and advocate for good policy/zoning reform (Unfortunately, your voice holds more weight than renters. Make sure you use that power!)</p>
<p>I understand the futility of knowing that individuals are a speck when compared to the pollution of corporations, and I know the US political system is broken and feels helpless. But there has to be <strong>some way</strong> to help us feel more in control?</p>
<p>It\'s very disheartening when it\'s been proven time and time again that peaceful protests don\'t do anything. That signing petitions doesn\'t do anything. That writing letters to politicians doesn\'t do anything. That speaking up doesn\'t do anything. I made this list because <strong>maybe actions will do something.</strong></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>