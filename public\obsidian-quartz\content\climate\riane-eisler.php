<?php
// Auto-generated blog post
// Source: content\climate\riane-eisler.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Riane Eisler - The Real Wealth of Nations';
$meta_description = 'Riane Eisler - The Real Wealth of Nations';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Riane Eisler - The Real Wealth of Nations',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'Riane Eisler - The Real Wealth of Nations',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\climate\\riane-eisler.md',
);

// Post content
$post_content = '<p><p>Riane Eisler, author of The Real Wealth of Nations, proposes that a good society is one that invests in its communities, the environment, and the wellbeing of its people, especially women and caregivers. She argues that these are the best long-term investments we can make, and that a society that marginalizes these people and their needs is not a good one. We make life better by affirming the wellbeing of those who do the invisible labor of care, community service, and childrearing. Many \'civilized cultures\' just marginalize those same people and their needs. That\'s not a good idea, and leads to everyone suffering. ("If momma wasn\'t happy, no one\'s happy")</p>
<p>
    <a href="https://www.youtube.com/watch?v=f9cMcTWWDkU">https://www.youtube.com/watch?v=f9cMcTWWDkU</a>
</p>
<p>The Gross Domestic Product (GDP) is an outdated and very inaccurate way to measure well-being. It measures the value of all the monetary transactions that have occurred in a fiscal year. This means that it includes things like the cost of rebuilding a house after a natural disaster, the cost of getting married or divorced, and the cost of cutting down a forest. This means that the GDP can go up even if people are not actually better off. For example, if you have a terrible year where you lose everything and have to rebuild from scratch, the GDP will go up. However, if you have an uneventful year where you spend time with family, make meals from home, and live a peaceful life, the GDP will go down.</p>
<p>There are many other factors that contribute to well-being that are not included in the GDP, such as health, education, and social relationships. The GDP is not a good measure of how well people are doing.</p>
<p>Every time money changes hands, it is an exchange of energy. Either immediately, or in the future. When you buy a pizza, it has to be cooked in an oven, which requires energy. When you buy a bicycle, it requires energy to manufacture. When you pay someone to clean your house, they are expending energy. When we look at sustainability, we need to find ways to reduce our energy consumption while maintaining a good quality of life. Economics and sustainability do not have to conflict, but they often do.</p></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>