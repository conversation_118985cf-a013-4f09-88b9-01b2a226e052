<?php
// Auto-generated blog post
// Source: content\climate\walk-crossroads.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Walk Crossroads';
$meta_description = 'Walk Crossroads';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Walk Crossroads',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'Walk Crossroads',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\climate\\walk-crossroads.md',
);

// Post content
$post_content = '<h2>Late 2013: A Crossroads</h2>
<p>Three and a half years into college, just five credits shy of a Psychology degree (though my true passion lay in Political Ecology), I found myself deeply immersed in a world of young queer farmers, local landowners battling fracking injection wells, and environmental non-profit campaigns. As a lifestyle intern at the Office of Sustainability, I led facility tours and maintained a 3.3 GPA. My Tuesdays were spent facilitating a well-attended vegan cooking workshop, where volunteer efforts helped feed 50 to 100+ people weekly.</p>
<p>Despite this engaging life, the prospect of leaving the inclusive atmosphere I had found – a space where I didn\'t feel the need to perform gender or sexual expression – terrified me. Eager to postpone a return to my hometown, I sought sponsorship for an upcoming climate justice walk from LA to DC as an independent study, but my request was denied. Uncertainty clouded my vision of surviving the journey. Fortuitously, a college trust established by my grandparents shielded me from debt. In my mind, the pursuit of peace-walking for climate justice and healing stood as the highest calling, a direct contrast to my belief that jobs fueled environmental destruction.
<h2>The Eight-Month Journey</h2></p>
<p>With $3,500 raised from family and friends, I embarked on the eight-month walk. Our ever-changing group of around 40 people camped nightly, supported by a Uhaul housing our mobile kitchen for preparing 2.5 daily meals and hauling water. Along the way, we connected with numerous local landowners, hearing firsthand accounts of climate change\'s impact. My worldly possessions were reduced to the essentials carried in a baby stroller, a testament to my commitment to movement and well-being.</p>
<p>The journey was marked by profound experiences. Halfway through the Sonora Desert, a call to my Dad was made from an unexpected location: the Zuni Correctional Facility, where the tribe generously hosted us for the night. Our pastor faced an unfounded police stop prompted by a "karen call" alleging he was dangerously pushing a baby along the highway. I faced the raw power of nature, getting lost and nearly dying in the wilderness on at least two occasions.</p>
<p>The walk also served as a powerful platform for activism. I participated in approximately one hundred environmental rallies, street marches, workshops, and movie screenings, contributing my energy to dozens of unconventional kitchens. Local media featured my involvement twice, and my image appeared in three major city newspapers. My social media following grew, adding to the extensive network of over 2,500 environmentally progressive contacts across Turtle Island and several other countries, compiled through the walk and my college organizing efforts. Living on a meager five dollars a day, often drinking from creeks and foraging from dumpsters, I thrived in the company of the person I loved, free from the responsibilities of dependents, spending most waking hours in their presence.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>