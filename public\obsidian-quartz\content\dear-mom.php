<?php
// Auto-generated blog post
// Source: content\dear-mom.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Dear Mom,';
$meta_description = 'Dear Mom, I\'m writing this out of choice, mercy, and for me. I don\'t want your response. I want you to read it, and sit with it. I am addressing it to...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Dear Mom,',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'Dear Mom, I\'m writing this out of choice, mercy, and for me. I don\'t want your response. I want you to read it, and sit with it. I am addressing it to...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\dear-mom.md',
);

// Post content
$post_content = '<p>Dear Mom,</p>
<p>I\'m writing this out of choice, mercy, and for me. I don\'t want your response. I want you to read it, and sit with it. I am addressing it to \'Mom\' not because I feel anything motherly towards you, but because if I write it to your first name, I don\'t think you will read it with any sincerity at all.</p>
<p>I call you Mom because, you put on a very good show, and I wish to give you your flowers for that. I was convinced on face value for a very long time that you meant what you said when you say you love me.</p>
<p>The whole time I know without a doubt that you hated and resented everything about me. My choices, the way I live my life, my relationships. My struggles and accomplishments were never real to you because they were different than yours.</p>
<p>I\'m convinced that deep down, you would have preferred that I had died. Pay attention to my words. I think you would have <em>preferred</em> me dying, than the idea of me going my own way without you. You would have preferred me dying over me choosing myself and being at war with you.</p>
<p>I\'m not a spiteful person. I don\'t like living that way. But I made a choice a long time ago that I don\'t want to keep people in my life, who are too good to learn anything new. People who are set in their ways. I\'ve never had that luxury in my life, nor have I wanted that. I\'m not someone who chooses sides. I don\'t need a boogeyman, or an enemy at all times in my life.</p>
<p>You have consistently made decisions that at best, push me away, and at worst, have put my life and safety in danger out of no necessity except that of your own ego. When you \'ruthlessly advocated\' for me as a kid, it was not for me, it was for your stupid ego to have a gifted child.</p>
<p>It was to play a stupid game, that was not at all about my best interests, or having the skillsets and coping strategies to have a good quality of life and a long life worth living. They say when you play stupid games, you win stupid prizes.</p>
<p>My entire life was a lie until I split from you. I fail to see you as motherly. I don\'t even think you wanted your children. You embraced your children. Every step of the way you\'d make comments about how much of a pain in the ass we were.</p>
<p>I see you as a threat to the wellbeing and safety of your grandchild. Heck, I don\'t know if you have multiple grandchildren at this time. I really hope you don\'t. When I chose to not have children, it was not an easy choice. I love kids. I don\'t want to be responsible for bringing one into the world.</p>
<p>Out of our immediate family, I am the only one who has never been married, who has not had kids. Yet I have been from a young age on the clean up crew for all the broken marriages and regretful conceptions. We choose to own our decisions and embrace them with a level of joy, because the alternative to that, quite frankly, sucks.</p>
<p>I think you genuinely enjoy the aspect of motherhood of young children. When they get old enough to question you and not be a carbon copy of you, it\'s no longer fun. Or maybe it is fun, talking shit about their character and acting like the victim of your own decisions and choices.</p>
<p>I\'m not writing this for you. I\'m writing this so that if I get hit by a bus tomorrow, people who will take the time to read and hear my words will know that I don\'t like you or endorse any of your decisions.</p>
<p>There is nothing dramatic or fun about this message. It\'s not an easy story from start to finish. It\'s broken like our relationship. Nothing about it will change who you are, or your behavior. I\'m at peace with that.</p>
<p>I\'m not wasting my precious time on this Earth trying to fix or change what\'s not meant to be fixed or changed.</p>
<p>I have lots of great mothers in my life that I celebrate and find joy with. I actually intimately understand what it is to be a parent, and that\'s why I chose not to be one. Scoff at that, I don\'t care.</p>
<p>If I ever deliver this message, it will be in the most publicly intimate way possible. Not because I\'ve taken decades to come to peace with this, but because when I do, I will be confident that you will hold no power to weaponize the police against me. You won\'t be able to cry to your law enforcement lovers to do a home welfare check with guns, or make a complaint of someone I love to the immigration police.</p>
<p>I don\'t want to be at war with you. I have literally done everything in my power to coexist and have a functional relationship. And doing that cost me everything in my life. And when it did, you had the audacity to say to say I was never homeless. You waged my sibling against me, and presented me as a danger to children. None of it was ever true. It wasn\'t until I accepted you were my adversary, and that living my truth meant your eventual self-destruction, that I began to experience joy in my life.</p>
<p>When my sibling wrote an angry letter to me in 2019, I took a few years before reading it. It wasn\'t for me. In the letter she blamed me for you having to go into counseling. How long did that last? My guess is long enough to learn about how it works, so you can wield it as a weapon against your unruly children.</p>
<p>If you choose to continue doing what you have been doing for decades, which I have no credible evidence to believe you won\'t, I promise that you will die alone. I won\'t be at your funeral. I will cry for a day, and have a designated space for you on my Ofrenda. If you want to try going a different way. To repent and reparate harm, you are welcome to hire a mediator. A family therapist or a legal mediator, I don\'t care. I will only match the effort you put in.</p>
<p>I have no desire to engage with you directly. I will gladly engage with a third party and speak my truth, in that so they can better help you. But chances are, I am just going to direct them to this, and other written or spoken messages that are publicly available.</p>
<p>But right now, you are my enemy. You are bringing the same lifelong damaging bullshit to your grandchildren, that you brought to my adolescence and early adulthood.  They will turn out very much like me, not because they are role modeling me during the worst time of my life, but because they are withheld from me when I am at my best.</p>
<p>If I ever see our shared young person again in my lifetime, I have no desire to trash talk you. Not because you deserve that grace, but because kids don\'t deserve that. They deserve to love the family members they are going to love, without being brought into grown up issues.</p>
<p>I have no way of protecting them against what I put on the internet. If they want to seek it out they can. For comparison, your father started including me on racist anti-immigrant chain emails when I was 11.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>