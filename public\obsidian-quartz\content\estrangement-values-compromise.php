<?php
// Auto-generated blog post
// Source: content\estrangement-values-compromise.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '**Dear Abby: I Chose Estrangement Over Compromising My Values**';
$meta_description = 'Dear Abby: I Chose Estrangement Over Compromising My Values DEAR ABBY: I made the painful decision to estrange myself from both of my parents, first ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '**Dear Abby: I Chose Estrangement Over Compromising My Values**',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'Dear Abby: I Chose Estrangement Over Compromising My Values DEAR ABBY: I made the painful decision to estrange myself from both of my parents, first ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\estrangement-values-compromise.md',
);

// Post content
$post_content = '<h3><strong>Dear Abby: I Chose Estrangement Over Compromising My Values</strong></h3>
<p><strong>DEAR ABBY:</strong> I made the painful decision to estrange myself from both of my parents, first my mother eight years ago and recently my father. These were the hardest choices of my life, and they came with tremendous consequences. Yet, I know I am better off.</p>
<p>This is where my story differs from what people often expect. My childhood wasn’t abusive. We were the “perfect family” on paper—I was well-fed, supported, and loved. For years, I told myself that having some emotional baggage was normal.</p>
<p>The rift happened in adulthood. As I grew into my own person and formed my own values, I realized we inhabit completely incompatible realities. The kindest way I can put it is that my parents worship money above all else and support political movements and ideologies that I find deeply harmful. Their choices, and the politicians they endorse, support systems that make life incredibly difficult for vulnerable people like me. When I struggle with the consequences of the world they help create, I am blamed for my own struggles.</p>
<p>They see my stance as being “overly political.” I see it as a fundamental difference in ethics. I cannot reconcile loving my parents with the deep moral injury I feel from their beliefs. I am now the “ungrateful villain” in their story for choosing a life of integrity—one that prioritizes kindness and environmental responsibility over the pursuit of wealth.</p>
<p>Is there a name for this? How do I explain that estrangement can happen not because of direct abuse, but because of a values chasm so wide that trust and communication become impossible? — <strong>VILLAINIZED IN VIRGINIA</strong></p>
<p><strong>DEAR VILLAINIZED:</strong> Thank you for your courage in writing this. Your experience is more common than you know, especially in our current polarized climate. What you are describing is often called <strong>“value-driven estrangement”</strong> or <strong>“ideological estrangement.”</strong></p>
<p>You are navigating a particularly difficult form of <strong>disenfranchised grief</strong>—the grief that society doesn’t easily acknowledge or validate. People understand cutting off parents who were overtly abusive or neglectful. They have a harder time understanding a choice based on a profound ethical incompatibility, even though the pain is just as real.</p>
<p>You are not a villain. You are someone who made an impossible choice to protect your peace and integrity. You chose to stop providing emotional labor for people whose worldview you find destructive. This is not ungrateful; it is an act of self-preservation.</p>
<p>The phrase “I love you, but I cannot be in relationship with the choices you make and the things you support” is a complete and valid reason. Your task now is to build a “chosen family” of people who share your values and offer the support and understanding you need. Continue to live your life with integrity, and know that your difficult decision resonates with many others walking the same lonely path.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>