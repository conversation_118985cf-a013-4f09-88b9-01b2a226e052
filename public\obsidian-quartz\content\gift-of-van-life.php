<?php
// Auto-generated blog post
// Source: content\gift-of-van-life.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Gift of Van Life';
$meta_description = 'Living in my van is a deeply enjoyable and enriching experience. I’ve grown to love winter, finding cozy ways to stay warm in my car bed. Each night, I fall asleep to the unique sounds of nature surrounding me, and the rain on the roof is particularly soothing.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Gift of Van Life',
  'author' => 'A. A. Chips',
  'date' => '2025-05-11',
  'excerpt' => 'Living in my van is a deeply enjoyable and enriching experience. I’ve grown to love winter, finding cozy ways to stay warm in my car bed. Each night, I fall asleep to the unique sounds of nature surrounding me, and the rain on the roof is particularly soothing.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\gift-of-van-life.md',
);

// Post content
$post_content = '<h2>Van Life: A Gift with Challenges</h2>
<p>Living in my van is a deeply enjoyable and enriching experience. I’ve grown to love winter, finding cozy ways to stay warm in my car bed. Each night, I fall asleep to the unique sounds of nature surrounding me, and the rain on the roof is particularly soothing. My sensitivity to dust and indoor mold, common in many homes I’ve visited, is no longer a concern. My car offers me a personal sanctuary for thought, writing, sleep, and solitude. My car bed is surprisingly comfortable, much preferable to traditional mattresses. While others speak fondly of their beds, my car is my happy sleep haven. Though, on pleasant days and nights, my camping hammock is a close second, despite lacking rain and bug protection currently. My vehicle isn\'t fuel-efficient, but it serves as a remarkably energy-efficient dwelling.</p>
<p>However, this lifestyle presents its own set of challenges. The absence of indoor plumbing means relying on public facilities, generous friends, or mindful practices in nature for bathroom needs, showering, and laundry. Storing perishable food for extended periods is not feasible. Summer brings difficulties with heat and insects, and sleeping with open windows requires vigilance due to safety concerns. While I carry mace and a blunt object for self-defense, verbal communication has always been sufficient in the few encounters I’ve had to establish boundaries or respect others\' space. I miss having a personal kitchen for cooking, an activity I love for its therapeutic value and the joy of experimenting with vegan recipes alongside cooking companions in shared kitchens.</p>
<p>Ultimately, I cherish the personal space my car provides when I need it, as healthy boundaries are essential to how I connect with others. I also value the interdependence and the opportunity to contribute to others\' well-being.My Identity and Reality</p>
<p>Unknown Date, 2018</p>
<p>The only tragedy in my houselessness<em> is the inability to cook. I need serious help. I need the right kind of help though. I have an extremely intense and powerful energy to my being that I cannot cope and handle with most of the time. That is why I spent long times hermetic in the library. It\'s a safe place for me to study and feel normal for a chunk of my days. </em>(auto-correct=soullessness)</p>
<p>I\'m frustrated when I do not have a kitchen. I am creating a lifestyle by doing this. I want to encourage others to do the same. This is a culmination of my disastrous life choices, mainly the choices of wanting to live in a van down by the river. I don\'t mind living out of my vehicle. What is the \'shit sandwich\' of that?</p>
<p>There are a lot of reasons I like cooking, like being around food, learning new creations, feeding people, but really, the secret reason I really cook,, is because I have the hope something I will make, will transcend into so someone’s sexy thoughts, after eating. I wan hope somebody thinks of my food when they touch themselves, or at least that the food plays an extra role in their dirty ass fantasy. I want somebody\'s wife to call them \'peanut sauce\' on accident during sex.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>