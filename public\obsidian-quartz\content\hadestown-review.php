<?php
// Auto-generated blog post
// Source: content\hadestown-review.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Hadestown. Teen Edition. My Review.';
$meta_description = 'Someone I know was performing in Hadestown. Teen Edition. I got gifted tickets. I went to see it. I am not a theater or musical person, but I listen to musical soundtracks and knew every other word. There was not a dry eye that night.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Hadestown. Teen Edition. My Review.',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'Someone I know was performing in Hadestown. Teen Edition. I got gifted tickets. I went to see it. I am not a theater or musical person, but I listen to musical soundtracks and knew every other word. There was not a dry eye that night.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\hadestown-review.md',
);

// Post content
$post_content = '<p>Someone I know was performing in Hadestown. Teen Edition. I got gifted tickets. I went to see it. I am not a theater or musical person, but I listen to musical soundtracks and knew every other word. There was not a dry eye that night.</p>
<p>Hadestown takes place in a post-apocalyptic world, where the river Styx has dried up, and the gates of the Underworld are crumbling. The story follows Orpheus, a troubadour, and Eurydice, a young woman, as they navigate the treacherous journey to the Underworld to rescue Eurydice from Hades, the god of the Underworld.</p>

<p><figure>
    <img src="../../img/self/hadestown.jpg" width="400" alt="Selling the flower table for Hadestown.">
    <figcaption>Selling flowers for Hadestown. We sold out during the intermission.</figcaption>
</figure></p>
<p>Two lovers, Orpheus and Eurydice, are separated by death. Orpheus embarks on a perilous journey to the Underworld to bring Eurydice back to the world of the living. However, Hades, the god of the Underworld, has other plans. He falls in love with Eurydice and kidnaps her, leading to a tragic confrontation between Orpheus and Hades.</p>
<p>Check out the Tiny Desk Concert done by the original cast of Hadestown.</p>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/XKwDFDDr_VA?si=nAMoefXW4c0q4dPR" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></p>
<p>Here\'s my favorite song from the show.</p>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/MUQSEXyQsw4?si=NEpM-mSyW0Q_8mOI" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></p>
<p>Do you get it? I am Chips. I make Chips.</p>
<p>I also really like Wait for Me.</p>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/ww6_FO8QKt0?si=YBgWMoRPvc0TlYtI" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>