<?php
// Auto-generated blog post
// Source: content\healing-homelessness.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Healing Homelessness - Reflection';
$meta_description = 'In January 2017, I packed my car and drove south without telling anyone. To some, it looked like I’d "chosen" homelessness. The truth? I was running to survive.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Healing Homelessness - Reflection',
  'author' => 'A. A. Chips',
  'date' => '2025-05-01',
  'excerpt' => 'In January 2017, I packed my car and drove south without telling anyone. To some, it looked like I’d "chosen" homelessness. The truth? I was running to survive.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\healing-homelessness.md',
);

// Post content
$post_content = '<h3><strong>Leaving to Survive: My Journey from Homelessness to Healing</strong></h3>
<p>In January 2017, I packed my car and drove south without telling anyone. To some, it looked like I’d "chosen" homelessness. The truth? I was running to survive.</p>
<p>After years of family conflict, my mother threatened to call the police for a "wellness check"—a dangerous risk for someone living in their car, especially when staying with a friend targeted by law enforcement. I wasn’t just leaving a toxic home; I was fleeing potential harm. With $300 and no plan, I headed for the mountains of North Carolina, where I knew no one.
<h3><strong>Homelessness Isn’t Always What You Think</strong></h3></p>
<p>I didn’t sleep on sidewalks. I lived in my car—a mobile refuge I called my "car-partment." But "car living" isn’t a lifestyle choice when it’s your only option. I relied on public restrooms, free meal programs, and the kindness of strangers. I volunteered at kitchens like Kairos Center, where I found community among others facing similar struggles.</p>
<p>People said:</p>
<p>- _"You’re not _really_ homeless—you have a car."_
- _"You chose this."_</p>
<p>But choosing between danger and instability isn’t a choice. It’s survival.</p>
<h3><strong>Rebuilding, One Step at a Time</strong></h3>
<p>Asheville became my reset. I reconciled with my dad, who helped cover car repairs while I volunteered for food. I found work in kitchens, though chronic pain and PTSD made steady employment difficult. Slowly, I rebuilt—not just a home, but myself.</p>
<h3><strong>What I Want You to Know</strong></h3>
<p>1. <strong>Homelessness isn’t a monolith.</strong> It’s cars, couches, tents, and shelters. It’s invisible.</p>
<p>2. <strong>"Choice" is a privilege.</strong> No one chooses hunger or fear. They choose the least terrible option.</p>
<p>3. <strong>Kindness is lifeline.</strong> A meal, a safe parking spot, or just listening can change someone’s trajectory.</p>
<h3><strong>Where I Am Now</strong></h3>
<p>Today, I’m stable—but I haven’t forgotten. My project, <a href="https://www.aachips.co/heartwarmers" class="external-link">Heartwarmers</a>, is the resource I needed back then: a guide for those facing impossible choices.</p>
<p>To anyone in that place now: <strong>You’re not alone. Your pain is valid. And leaving to save yourself isn’t abandonment—it’s courage.</strong></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>