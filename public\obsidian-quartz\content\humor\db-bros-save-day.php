<?php
// Auto-generated blog post
// Source: content\humor\db-bros-save-day.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The Diarrhea Brothers Save the Day';
$meta_description = 'John, Jonathan and Leonard Diarrhea are delivery drivers for their family business. Winston, a new hire, gets wrapped up in their world of chaos and diarrhea. A film by Joel Haver';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The Diarrhea Brothers Save the Day',
  'author' => 'Joel Haver',
  'date' => '2025-10-10',
  'excerpt' => 'John, Jonathan and Leonard Diarrhea are delivery drivers for their family business. Winston, a new hire, gets wrapped up in their world of chaos and diarrhea. A film by Joel Haver',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\humor\\db-bros-save-day.md',
);

// Post content
$post_content = '<p>This is by far the best stupidest film I have ever watched. 
<iframe width="560" height="315" src="https://www.youtube.com/embed/h3UA8yfKRFY?si=rmyNnaQmxwRxOmqm" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></p>
<h1>The Diarrhea Brothers Save The Day</h1>
<p>Joel Haver</p>
<p>2.06M subscribers
120,914 views Nov 23, 2024</p>
<p>John, Jonathan, and Leonard Diarrhea are delivery drivers for their family business. Winston, a new hire, gets wrapped up in their world of chaos. A film by Joel Haver, featuring Trent Lenkarski, Alex Martens, Mason Carter, Sethward, Paulette Jones, Lars Midthun, Brittney Rae, Paulina Gregory, Syd Smith, Benny Ball, Ryan The Leader, Magenta Squash, Yehslacks, Sven Johnson, Gus Toonz, Daxflame, Tom Goulet, Firas Catler, Sharhar Hillel, Walt Lusk, StaggerLee Cole. Original Score by Trent Zulkiewicz and Droodle. Additional Music by Kevin, Cam Raleigh, and MyKey The Artist. Special Effects by Tom Goulet. Special Effects Makeup by Jenn Osborne and Leigh Mader.</p>
<p>Other Channels:
- Joel Talks About Movies
- goodlongpee</p>
<p>Support:
- Patreon: Joel Haver
- Paypal: bit.ly/2ZI7uff</p>
<p>Merch: joelstuff.store</p>
<p>Social:
- Instagram: joelhaver
- Twitter: joelhaver
- Drawings: joeldrawsandwriteshaver
- Letterboxd: joelhaver</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>