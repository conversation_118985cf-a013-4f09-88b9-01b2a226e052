<?php
// Auto-generated blog post
// Source: content\humor\theonion-ten-weirdest-people.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'FBI Releases List of 10 Weirdest People Who Are Actually Harmless Once You Get to Know Them';
$meta_description = 'Emphasizing that it was important to always stay vigilant but not freak out about them or anything, the FBI released a list Tuesday of the 10 weirdest people who are actually harmless once you get to know them.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'FBI Releases List of 10 Weirdest People Who Are Actually Harmless Once You Get to Know Them',
  'author' => 'The Onion',
  'date' => '2025-10-10',
  'excerpt' => 'Emphasizing that it was important to always stay vigilant but not freak out about them or anything, the FBI released a list Tuesday of the 10 weirdest people who are actually harmless once you get to know them.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\humor\\theonion-ten-weirdest-people.md',
);

// Post content
$post_content = '<p><img src="../../img/weirdestpeople.webp" width="400" alt="mock fbi press briefing."></p>

<p>WASHINGTON—Emphasizing that it was important to always stay vigilant but not freak out about them or anything, the FBI released a list Tuesday of the 10 weirdest people who are actually harmless once you get to know them.</p>
<p>“After countless hours of surveillance and research, we have determined that the following people are the most off-putting freaks in America but if you see them, you ultimately have nothing to worry about,” said FBI director Christopher Wray, adding that while each individual had extensive histories of saying odd phrases, pacing, or dressing in crazy outfits, they ultimately wouldn’t hurt anyone.</p>
<p>“We’d like to reiterate that the top wackadoos on this list definitely freaked us out at first, with their odd hairdos, the big books they were always reading, and the fact that many of them would randomly skip down the street and sing a song. But in the end, we determined they were absolutely not a danger to anyone, and were actually pretty friendly. You maybe just don’t want to engage with them for too long.”</p>
<p>At press time, Wray announced that the FBI had assassinated the highest ranking weird guy after he put on a funky hat and started walking towards a nice suburban neighborhood.</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>