<?php
// Auto-generated blog post
// Source: content\inspiration\oldest-complaint.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The Oldest Customer Complaint';
$meta_description = 'The oldest written customer complaint was stolen by the British Museum in 1953, and has lived there ever since. It is written about in the Guinness Book of World Records. This complaint is 3767 years old, and was found in the ancient city of Ur in Southern Iraq.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The Oldest Customer Complaint',
  'author' => 'A. A. Chips',
  'date' => '2020-06-26',
  'excerpt' => 'The oldest written customer complaint was stolen by the British Museum in 1953, and has lived there ever since. It is written about in the Guinness Book of World Records. This complaint is 3767 years old, and was found in the ancient city of Ur in Southern Iraq.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\inspiration\\oldest-complaint.md',
);

// Post content
$post_content = '<h3>The Oldest Written Customer Complaint</h3>
<p>The oldest written customer complaint was stolen by the British Museum in 1953, and has lived there ever since. It is written about in the Guinness Book of World Records. This complaint is 3767 years old, and was found in the ancient city of Ur in Southern Iraq. The tablet is written in old Sumerian, a language not spoken by anyone alive today. The complaint is regarding a merchant named Ea-Nasir, and is written by a customer named Nanni. Nanni had a longstanding feud with this copper ingot merchant, having to travel to enemy territory, and receiving the wrong kind, and substandard quality of copper. The complaint has been translated and published in _Letters from Mesopotamia: Official, Business, and Private Letters on Clay Tablets from Two Millenni_ by Assyriologist A. Leo Oppenheim. Below is the translation </p>
<p>!<a href="https://allthatsinteresting.com/wordpress/wp-content/uploads/2018/09/tablet-complaint-1.jpg" class="external-link">Four thousand year old tablet containing customer complaint in ancient Sumerian. Photo by National Geographic</a></p>
<p><blockquote>"Tell Ea-nasir: Nanni sends the following message:</p>
<p>When you came, you said to me as follows : “I will give Gimil-Sin (when he comes) fine quality copper ingots.” You left then but you did not do what you promised me. You put ingots which were not good before my messenger (Sit-Sin) and said: “If you want to take them, take them; if you do not want to take them, go away!”<br><br></p>
<p>What do you take me for, that you treat somebody like me with such contempt? I have sent as messengers gentlemen like ourselves to collect the bag with my money (deposited with you) but you have treated me with contempt by sending them back to me empty-handed several times, and that through enemy territory. Is there anyone among the merchants who trade with Telmun who has treated me in this way? You alone treat my messenger with contempt! On account of that one (trifling) mina of silver which I owe(?) you, you feel free to speak in such a way, while I have given to the palace on your behalf 1,080 pounds of copper, and umi-abum has likewise given 1,080 pounds of copper, apart from what we both have had written on a sealed tablet to be kept in the temple of Samas.<br><br>
 
 How have you treated me for that copper? You have withheld my money bag from me in enemy territory; it is now up to you to restore (my money) to me in full. Take cognizance that (from now on) I will not accept here any copper from you that is not of fine quality. I shall (from now on) select and take the ingots individually in my own yard, and I shall exercise against you my right of rejection because you have treated me with contempt.”</blockquote></p>
<p>Imagine a business receiving this customer complaint as an online review. This particular story went viral on the internet. It has become a \'meme\' on the internet, which is arguably, the most effective kind of mass communication in this digitalized era in 2025. There are dozens of pages on the internet dedicated to laughing at this story, including a \'sub Reddit\' called r/ReallyS<em>*</em>tyCopper. </p>
<p>!<a href="https://preview.redd.it/ea-nasir-where-is-my-copper-v0-bpbvx4la3c7c1.png?auto=webp&s=cdcc251cc33a490ade54c24e0f5646f421f48bff" class="external-link">meme of customer complaint to Ea-Nasir, copper sales person in ancient Sumeria who sold bad copper</a></p>
<p>Think about the differences between that time and place in the world and where we are right now. How a scathing business review from four thousand years ago is a source of humor for thousands of people today. How many figures from ancient Mesopotamian times do you know? An engraved rock took four thousand years to be seen by the world. </p>
<h3>Sources</h3>
<p><a href="https://www.nationalgeographic.com/history/article/ea-nasir-copper-merchant-ur" class="external-link">Blakemore, E. (2023, October 16). Meet Ea-Nasir, a Shady Ancient Merchant—and Modern Meme. History. National Geographic</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>