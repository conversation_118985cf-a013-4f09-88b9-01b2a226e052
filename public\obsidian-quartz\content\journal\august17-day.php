<?php
// Auto-generated blog post
// Source: content\journal\august17-day.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'August 17th, 2017';
$meta_description = 'A day in the life of a car-bound homeless person in Asheville, NC.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'August 17th, 2017',
  'author' => 'A. A. Chips',
  'date' => '2017-08-17',
  'excerpt' => 'A day in the life of a car-bound homeless person in Asheville, NC.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\journal\\august17-day.md',
);

// Post content
$post_content = '<p><strong>Morning: The Shower Incident</strong></p>
<p>Five days without a shower. The car life has its perks—mobility, solitude, no rent—but hygiene isn’t one of them. Downtown, AHOPE’s morning line snakes out the door: ten people waiting for the holy grail of hot water and soap. I clutch my towel like a boarding pass.</p>
<p>Then, a man in line locks eyes with someone passing by, a guy just trying to piss. "I’ll _kill_ you," he snarls, flipping a knife open like it’s a pocket lighter.</p>
<p>I nope out. Tell the front desk—"Bathroom. Knife. Maybe murder?"—and bolt, accidentally stealing their towel.</p>
<p>Plan B: the university pool showers. Denied—no student ID. Plan C: a creek in the woods, where I scrub with a sponge like a raccoon washing stolen candy. Judge me all you want, but creek water doesn’t charge admission.</p>
<p><strong>Afternoon: The Hustle</strong>  
Gas is gold, so I cram every errand into one trip. Today’s haul:</p>
<p>A busted bicycle ($50). At the co-op, a mechanic teaches me to wrestle a derailleur into submission. I leave with grease under my nails and a bike worth double what I paid. A crock pot, tent, smartphone (don’t ask), and earrings that look like they were made by a creative kindergartener. Chinese food from the grocery store eaten on a park bench, the soy sauce packet my finest china.</p>
<p>The mechanic gets a gift bag of produce on Monday. Mentorship tastes like slightly bruised apples.
 
Sam’s in town, sleeping in his Prius. I take him to Linda and Joe’s trailer—Fairview’s version of a five-star resort, complete with indoor plumbing and whiskey breath. I cook stir-fry with the precision of a chef who knows this meal might be the only nice thing someone does for Sam all year. Baked tofu, peanut sauce, rice kissed with lime. Joe strums his guitar; Sam plucks a banjo. For three hours, we’re not homeless. We’re just people who know how to make a feast out of nothing.</p>
<p>I dream of my father. He throws a birthday party—for me, but I’m not invited. My Facebook friends raft down a river while my uncle hands me a Bible like it’s a grenade. Two old crushes gift me a squirrel and a bird. "Here," they say. "Now you have pets to abandon too."</p>
<p>I wake at 4:45 AM, moon glaring through the car window, cheeks wet. The day had been so _simple_: fix, trade, cook, survive. But my brain, ever the traitor, dredges up the past like it’s trying to remind me: _You can’t outrun the mess you left behind._</p>
<p>Homelessness is a series of small victories and smaller humiliations. You steal towels, bathe in creeks, and fix bikes just to prove you’re still capable of creating value. You feed strangers to remember you’re more than your circumstances. And sometimes, in the quietest hours, you cry—not because life is hard, but because it’s _not_, and that’s the most confusing thing of all.</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>