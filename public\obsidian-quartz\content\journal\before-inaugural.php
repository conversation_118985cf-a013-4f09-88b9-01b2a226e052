<?php
// Auto-generated blog post
// Source: content\journal\before-inaugural.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Four Days Before the Inaugural - 1/16/2017';
$meta_description = 'I was always more comfortable retreating to a hidden spot and losing myself in video games. They would completely absorb my time and became my only refuge during a period where I felt powerless. I had almost forgotten what that felt like until I started playing again.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Four Days Before the Inaugural - 1/16/2017',
  'author' => 'A. A. Chips',
  'date' => '2016-01-17',
  'excerpt' => 'I was always more comfortable retreating to a hidden spot and losing myself in video games. They would completely absorb my time and became my only refuge during a period where I felt powerless. I had almost forgotten what that felt like until I started playing again.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\journal\\before-inaugural.md',
);

// Post content
$post_content = '<p>Four days before the Inaugural, I\'m sitting here terrified and choosing not to participate in the demonstrations. To those who are, please be careful. This brings me back to the past, and I\'ve found myself escaping into video games again. Growing up with a helicopter parent meant it felt pointless to leave the house; it was simply too much effort. I was always more comfortable retreating to a hidden spot and losing myself in video games. They would completely absorb my time and became my only refuge during a period where I felt powerless. I had almost forgotten what that felt like until I started playing again. I had convinced myself that helpless time as a minor, when I had no legal agency, was over.</p>
<p>I\'m not sharing this publicly for attention, to play the victim, or to blame my mother. I\'m sharing this because at 24, this helicopter parent is still hovering, and I feel trapped. It’s time to end this cycle of abuse. My mother\'s upbringing wasn\'t easy. Her parents were young and poor, had four children in quick succession, and both struggled with daily drinking. My grandfather is still alive, a highly skilled woodworker with an incredible work ethic, but also filled with immense anger and prejudice. I remember hearing awful stories about him from my mother and her brothers\' childhood. He would even forward me these terrible chain emails when I was younger – essentially Trump propaganda before it became mainstream. He particularly despises non-English speakers, believing we shouldn\'t have to learn a second language in America. Ironically, English itself is a second language here, as the native tongues were eradicated. People who are multilingual benefit significantly mentally, and I consider it a valuable trait to speak more than one language. Plus, it\'s incredibly attractive.</p>
<p>My mother used to hate him, but now they are best friends. She claims to be more empathetic and compassionate but, in my opinion, validates my grandfather\'s hateful views and enables him. I believe my mother was the first in her family to pursue education beyond high school, going into nursing. I obtained a liberal arts degree, and it\'s clear that her family doesn\'t value education. Despite her specialized nursing knowledge, my mother can be incredibly unintelligent. However, she is a complete chameleon, adapting to whatever is most convenient at the moment. She’s a staunch Clinton feminist. She has always championed breaking the glass ceiling for women and has genuinely faced misogynistic behavior throughout her career. I firmly believe women should hold positions of power within society, that sexual violence needs to be destigmatized, boys should be encouraged to express their emotions, and that rigid gender roles shouldn\'t be imposed on children. I don\'t believe Clinton feminism focuses on these issues but is primarily concerned with engaging in the same harmful behaviors as men, especially in the corporate world. This scares me and feels dangerous.</p>
<p>Gaming allows me an escape, control and agency over my life. I was obsessed. Addicted, as a kid. Come back now to play as a 24 year old male. These games shaped me and gave me coping mechanisms for situations at the time. </p>
<p><img src="../../img/art/the-worlds-not-bad.jpg" alt="The world\'s not all that bad, it\'s your government that sucks. Banksy graffiti in destroyed home."></p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>