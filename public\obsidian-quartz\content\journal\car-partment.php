<?php
// Auto-generated blog post
// Source: content\journal\car-partment.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Car-Partment - A Poem';
$meta_description = 'Through rough times and good, Bounty and hunger, embrace and alienation, I’ve always had wheels, always had a net, I’ve always had my car-partment…';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Car-Partment - A Poem',
  'author' => 'A. A. Chips',
  'date' => '2018-06-06',
  'excerpt' => 'Through rough times and good, Bounty and hunger, embrace and alienation, I’ve always had wheels, always had a net, I’ve always had my car-partment…',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\journal\\car-partment.md',
);

// Post content
$post_content = '<p><img src="../../img/self/car-bed.jpg" alt="Bed in back of my old car with sheet and stuffed animal and pillows." width="250"></p>
<p>Through rough times and good,
Bounty and hunger, embrace and alienation,
I’ve always had wheels, always had a net,
I’ve always had my car-partment…</p>
<p>I love my car-partment, she cares for me well,
Always a nest, a roof for me to dwell,
A place to keep snacks on the go,
Change my clothes, and de-fur them like a pro,</p>
<p>A safe getaway when a situation is bad,
Sometimes being a tran gets some folks mad,
I know I have shelter, I know I have warmth,
With thanks to my car-partment…</p>
<p>When my car-partment breaks I get stressed and blue,
Arms have too much play, or steering gets loose
I knew I must learn mechanics, else I be on the streets,
Fight before admitting defeat.</p>
<p>Before the past couple years, I couldn’t change a wheel,
Or top my oil, the shops would take me for a fool,
I’ve learned some things, like rotating my wheels and service my brakes,
Keep fluids up and replace some bushings when she starts to shake
I’d like her to live forever, but that’s not my car-partment’s fate.</p>
<p>I’ve never been homeless, just caught without a house,
There are many worse off, but my life is to espouse,
Her steering is failing and my life falling apart,
For a time I’d like a place for my house to park,</p>
<p><img src="../../img/self/carbookshelf1.jpg" width="250" alt="Milk crate bookshelf in carpartment."></p>
<p>For the rest of my <a href="index.php">Journal Archives</a> vault go here.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>