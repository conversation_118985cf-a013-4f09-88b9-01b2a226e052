<?php
// Auto-generated blog post
// Source: content\journal\quiet-after.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The Quiet After - June 4th, 2025';
$meta_description = 'The house is quiet now. Not the good kind of quiet—not the easy silence of shared comfort. This quiet is thin, fragile, like ice over a deep lake.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The Quiet After - June 4th, 2025',
  'author' => 'A. A. Chips',
  'date' => '2025-06-04',
  'excerpt' => 'The house is quiet now. Not the good kind of quiet—not the easy silence of shared comfort. This quiet is thin, fragile, like ice over a deep lake.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\journal\\quiet-after.md',
);

// Post content
$post_content = '<p><strong>The house is quiet now.</strong></p>
<p>Not the good kind of quiet—not the easy silence of shared comfort. This quiet is thin, fragile, like ice over a deep lake. I sit very still, afraid to crack it.</p>
<p>I feel like the dog again. The way she freezes when voices rise, or when they are in trouble, hiding in their crate.—waiting for the storm to pass. _Don’t move. Don’t make a sound. Maybe no one will notice you._</p>
<p>I brought them here. Two women I love, two survivors, two people who know what it means to sleep uneasy and wake up fighting. I thought we could build something safe together. But safety looks different to each of us.</p>
<p>For Mira, safety is control—no strong smells, no surprises, no traces of the past she’s still outrunning. Her nose, sharpened by chemo and fear, catches dangers I can’t even smell. When she’s calm, she thanks me for the smallest things. When she’s scared, her voice is a knife, and I am suddenly the enemy.</p>
<p>For Shanna, safety is action—clean the mess, fix the problem, _do something_. She scrubbed the floors today, thinking she was helping. She didn’t know the alcohol would smell like a threat. Now she’s locked in her room, wondering if she’s the villain in a story she didn’t mean to write.</p>
<p>For <strong>me</strong>, safety is… I don’t know anymore. Maybe just the absence of yelling. Maybe the hope that if I stay small enough, careful enough, the house won’t splinter again.</p>
<p>But I can’t people-please my way out of this. I can’t scrub every surface or mute every scent or explain every misunderstanding fast enough.</p>
<p><strong>This isn’t about good or bad people.</strong> It’s about three broken radios tuned to different frequencies, all playing the same static of _I just want to feel safe_.</p>
<p>Mira might leave. Shanna might leave. I might be left holding the keys to an empty house, wondering if I failed or if this was always how it would end.</p>
<p>But tonight, the quiet holds. The dog sighs in her sleep. Somewhere, a floorboard creaks.</p>
<p>We’re all still here.</p>
<p>For now, that’s enough.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>