<?php
// Auto-generated blog post
// Source: content\journal\sirs-gift.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Sir\'s Gift - There\'s a Mouse in the House';
$meta_description = 'Sir\'s Gift - There\'s a Mouse in the House';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Sir\'s Gift - There\'s a Mouse in the House',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'Sir\'s Gift - There\'s a Mouse in the House',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\journal\\sirs-gift.md',
);

// Post content
$post_content = '<p>I have a tenant. He’s a furry, nocturnal drunk who sleeps on my couch and eats my leftovers. I call him Sir. He is not my cat in the way dogs are _your_ dog. He is a force of nature that arrived one day and decided my life was his hotel. Our relationship is complex. He wakes me from good dreams to demand tribute, and when I play music, he attempts to harmonize with the sound of a broken accordion.</p>
<p>Last Sunday, Sir came home from his nightly "drinking" and presented me with his tribute: a very alive, very terrified mouse. He dropped it on the rug with a look of profound generosity, as if he’d just handed me the keys to a new car.</p>
<p>The mouse, ungrateful for the introduction, immediately vanished into the sprawling metropolis of clutter I call a bedroom. My partner, upon hearing the news, handled it with the quiet grace of someone who believes mice are made of pure horror. The mantle of "strong one" fell upon my shoulders, a title I accepted with the courage of a man who squeals when a shadow moves too fast.</p>
<p>For two days, we lived under siege. We barricaded the doors. Sir and I conducted frantic, clumsy patrols, a mismatched militia armed with a broom and a complete lack of dignity. I built a Rube Goldberg-esque bucket trap from internet instructions—a rolling can of peanut butter poised over a watery doom. I was sure it was my masterpiece.</p>
<p>But the mouse was a ghost. The only trap in the room, it turned out, was my own mess. My makeshift laundry bin—a mountain of clothes atop a bin of recycling—was a functional deathtrap. The mouse had sought refuge in the soft folds of a t-shirt and met its end in the agitator of the washing machine. My partner’s scream was the announcement of its discovery.</p>
<p>My cat-person friend says Sir did this because he loves me and thinks I don\'t eat enough. I’ve been fasting, thank you very much. His concern is noted, and his rating as a hunter has been updated to: "Excellent at procurement, catastrophically bad at delivery."</p>
<p>I will be very sad when Sir eventually leaves. But for now, I’m rewashing an entire load of plague-adjacent laundry, and he’s sitting on the couch, looking terribly pleased with himself. He believes he provided the food and I, his clumsy human, simply bungled the preparation.</p>
<p>He’s probably not entirely wrong.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>