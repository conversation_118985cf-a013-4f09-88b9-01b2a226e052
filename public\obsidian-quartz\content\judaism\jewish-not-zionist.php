<?php
// Auto-generated blog post
// Source: content\judaism\jewish-not-zionist.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '**Judaism is not Zionism**';
$meta_description = 'Judaism is not Zionism    I am a Jewish American person. I’d like to take a few minutes to write about one of the most dangerous lies I was brought ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '**Judaism is not Zionism**',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'Judaism is not Zionism    I am a Jewish American person. I’d like to take a few minutes to write about one of the most dangerous lies I was brought ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\judaism\\jewish-not-zionist.md',
);

// Post content
$post_content = '<p><strong>Judaism is not Zionism</strong></p>

<p>I am a Jewish American person. I’d like to take a few minutes to write about one of the most dangerous lies I was brought up with. Like many lies, it is often built on good intentions. But the road to hell is paved with good intentions. My immediate ancestors grew up during and after the Holocaust. They lived in a different part of the world, after leaving Eastern Europe to avoid the Russian Draft in World War I. Judaism is almost 6,000 years old. The political movement that is called Zionism, or the belief in establishing a Jewish State in the Middle East, began in 1897. Judaism has existed far</p>

<p>I am not Zionist. I abhor nationhood and consider it to be a grand evil of our time. "It is a lie which has brought many innocent people untold suffering and if unchecked has the potential to create extraordinary tragedy in the future. It is the lie that declares that Judaism and Zionism are identical."* I still think we can learn from our past.</p>

<p>[Neturei Karta]</p>

<p>Aytzim - Ecological & Zionist Judaism</p>
<p>They’ve successfully banned fracking in Israel.</p>
<p><a href="http://aytzim.org/resources/articles/414" class="external-link">http://aytzim.org/resources/articles/414</a></p>


<p>Leviticus 19:33-34</p>
<p>“When a foreigner resides among you in your land, do not mistreat them. The foreigner residing amongst you must be treated as your native-born. Love them as yourself, for you were foreigners in Egypt.”</p>

<p>\'Just a quick reminder that Islamist extremists WANT you to distrust and mistreat all muslims, so that they\'ll have an easier time drawing disenfranchised people into their ranks. If you really wanna fight ISIS, start treating your muslim neighbors like human beings. Also, you know, they\'re human beings, so they kinda deserved that in the first place.\'</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>