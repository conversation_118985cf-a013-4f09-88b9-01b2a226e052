<?php
// Auto-generated blog post
// Source: content\judaism\nothing-remained.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'palestine #homeless #poems #repost #library';
$meta_description = 'palestine homeless poems repost library   Ahmed Miqdadhttps://www.facebook.com/ahmed.noor.737001?cft0=AZUkRd-wu5eHbxoQs2fDEYalG89pkozpVFgA5RGdoY-JrNBv...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'palestine #homeless #poems #repost #library',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'palestine homeless poems repost library   Ahmed Miqdadhttps://www.facebook.com/ahmed.noor.737001?cft0=AZUkRd-wu5eHbxoQs2fDEYalG89pkozpVFgA5RGdoY-JrNBv...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\judaism\\nothing-remained.md',
);

// Post content
$post_content = '<p>#palestine #homeless #poems #repost #library</p>
<p>#### <strong><a href="https://www.facebook.com/ahmed.noor.737001?__cft__[0]=AZUkRd-wu5eHbxoQs2fDEYalG89pkozpVFgA5RGdoY-JrNBvz8HlnlTpV92PaXNVtNmSGNoaKdR6TMFlsbL2eY6ArDaHNv7nc9n2mRh11NGH0nA7Pc97SdUcIl66JI9_sG06XVAva4KJoMGcKe_rmyRkGPdou3vt4ZgK8KqWWmPG2_4OH0TXVafnMKJPjPmxmsIoBe9MxbKw4m2az0rWE55eQZVwSe17GrNhOE9kjkZlfEXO1c_KHludMnXNz9554-gjecMc34c3k-2--_I5H6Hf2onrjunp566R1_Yr0lP71VNdoxzRgXvIhwhwe9WcKxBfbj7c_EcvxE_uDn8GGYg9Z_MB4-DOEkTtkRyo2abY3XzSwIavrNK4E9VPVZ0UXGVYbfXvEjV_ZDB9MyisXDy42l1fShXB5AOPHf7qG-z8mQ&__tn__=-]C%2CP-R" class="external-link">Ahmed Miqdad</a></strong> is with <strong><a href="https://www.facebook.com/profile.php?id=100073402266832&__cft__[0]=AZUkRd-wu5eHbxoQs2fDEYalG89pkozpVFgA5RGdoY-JrNBvz8HlnlTpV92PaXNVtNmSGNoaKdR6TMFlsbL2eY6ArDaHNv7nc9n2mRh11NGH0nA7Pc97SdUcIl66JI9_sG06XVAva4KJoMGcKe_rmyRkGPdou3vt4ZgK8KqWWmPG2_4OH0TXVafnMKJPjPmxmsIoBe9MxbKw4m2az0rWE55eQZVwSe17GrNhOE9kjkZlfEXO1c_KHludMnXNz9554-gjecMc34c3k-2--_I5H6Hf2onrjunp566R1_Yr0lP71VNdoxzRgXvIhwhwe9WcKxBfbj7c_EcvxE_uDn8GGYg9Z_MB4-DOEkTtkRyo2abY3XzSwIavrNK4E9VPVZ0UXGVYbfXvEjV_ZDB9MyisXDy42l1fShXB5AOPHf7qG-z8mQ&__tn__=-]C%2CP-R" class="external-link">Naimah Burhanuddin</a></strong> and **</p>
<p>My latest poem reflects what remains after months of brutal aggression. Please read and write your review.</p>
<p>Have a blessed Friday my friends.</p>
<h2>"Nothing has remained"</h2>
<p>Everything has gone
The homes, the souls and feelings.
Our joyful summer became a frightening winter
With its long-darked and terrifying nights.</p>
<p>The ghost of death eradicated our hearts
And stole our souls.</p>
<p>Our beautiful spring became a lifeless autumn
Our children fall Like the leaves of the green trees,
So quietly with the breeze of death.</p>
<p>Their souls fly
As the hovering and glamorous butterflies
Lost in the vast universe
And increase the number
Of the shining stars.</p>
<p>Our feelings turned into a dry valley
And a burden desert
They\'re frozen as an ice bar.</p>
<p>We don\'t feel the loss
As it\'s numerous
And no feelings to joy
We\'re still alive
But nothing has remained.</p>
<p><a href="https://www.facebook.com/ahmed.noor.737001?__cft__[0]=AZUkRd-wu5eHbxoQs2fDEYalG89pkozpVFgA5RGdoY-JrNBvz8HlnlTpV92PaXNVtNmSGNoaKdR6TMFlsbL2eY6ArDaHNv7nc9n2mRh11NGH0nA7Pc97SdUcIl66JI9_sG06XVAva4KJoMGcKe_rmyRkGPdou3vt4ZgK8KqWWmPG2_4OH0TXVafnMKJPjPmxmsIoBe9MxbKw4m2az0rWE55eQZVwSe17GrNhOE9kjkZlfEXO1c_KHludMnXNz9554-gjecMc34c3k-2--_I5H6Hf2onrjunp566R1_Yr0lP71VNdoxzRgXvIhwhwe9WcKxBfbj7c_EcvxE_uDn8GGYg9Z_MB4-DOEkTtkRyo2abY3XzSwIavrNK4E9VPVZ0UXGVYbfXvEjV_ZDB9MyisXDy42l1fShXB5AOPHf7qG-z8mQ&__tn__=-]K-R" class="external-link">Ahmed Miqdad</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>