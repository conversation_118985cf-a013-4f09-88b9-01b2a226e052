<?php
// Auto-generated blog post
// Source: content\kids-content\books-queer-authors.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Queer Authors of Classic Children\'s Books';
$meta_description = 'Favorite books by queer people';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Queer Authors of Classic Children\'s Books',
  'author' => 'Lindz Amer',
  'date' => '2025-05-20',
  'excerpt' => 'Favorite books by queer people',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\kids-content\\books-queer-authors.md',
);

// Post content
$post_content = '<p>Hey there, folks! I want to share something that I think is super important and not talked about enough. I bet most of the people flipping out about drag queens and kids have no idea that their favorite classic children\'s books were written by queer people.</p>
<p>Let\'s start with Arnold Lobel, the author of Frog and Toad. Did you know that he came out to his family in the mid-70s? I think it\'s amazing that he was able to be open and honest about his sexuality, especially during a time when it was much more difficult to do so.</p>
<p>Another amazing author is Maurice Sendak, who lived with his male partner for 50 years. Sendak is most famous for his book Where the Wild Things Are, which has become a beloved classic around the world.</p>
<p>Margaret Wise Brown is an iconic author who was also chaotic and bisexual. If you read through the "personal life and death" section of her Wikipedia page, you\'ll see that she lived a wild and wonderful life. I\'ve wanted to write a biopic screenplay about her for ages, and I think it would make for an amazing film.</p>
<p>James Marshall was another queer author who wrote many amazing books, including George and Martha. Sadly, he died of AIDS, but his legacy lives on through his wonderful stories.</p>
<p>Finally, there\'s Tommy dePaola, who was gay and came out later in life. The New York Times gave him a beautiful obituary after his death in 2020, and I think it\'s a great way to honor his memory.</p>
<p>I hope this helps to show that queer people have been making important contributions to literature and culture for a long time. Let\'s celebrate and appreciate these amazing authors and their work!</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>