<?php
// Auto-generated blog post
// Source: content\kids-content\frogs-candle-people-dinosaurs.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Frogs, candle people and dinosaurs. Strange Christmas cards from the past';
$meta_description = 'Frogs, candle people and dinosaurs. Strange Christmas cards from the past';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Frogs, candle people and dinosaurs. Strange Christmas cards from the past',
  'author' => 'Artsmarts',
  'date' => '2018-12-30',
  'excerpt' => 'Frogs, candle people and dinosaurs. Strange Christmas cards from the past',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\kids-content\\frogs-candle-people-dinosaurs.md',
);

// Post content
$post_content = '<p><a href="https://arthive.com/sl/publications/3784~Ljagushki_ljudisvechi_i_dinozavry_Strannye_novogodnie_otkrytki_iz_proshlogo?fbclid=IwAR0tZ8hneV-E9fRct3dfcoHkM0UAoUxZ23dvuk7Uf3P9BJaQYXk5AOh35TQ" class="external-link">Frogs, candle people and dinosaurs. Strange Christmas cards from the past | Arthive</a></p>
<p>By using our website you accept our conditions of use of cookies to track data and create content (including advertising) based on your interest. Find out more about what data we collect and use at <a href="https://arthive.com/about/privacy#cookie" class="external-link">here</a>.</p>
<p>Accept</p>
<p>[](https://arthive.com/sl)</p>
<p>Welcome to the brand new Arthive! Discover a full list of new features <a href="https://arthive.com/news/4832~new_arthive" class="external-link">here</a>.</p>
<p>!<a href="https://arthive.com/res7/img/through-blocks/button-pro-crown.png" class="external-link">button-pro-crown</a></p>
<p>PRO accounts for artists</p>
<p>!<a href="https://arthive.com/res7/img/through-blocks/arrow-top.svg" class="external-link">arrow-top</a></p>
<p><a href="https://arthive.com/sl/publications" class="external-link">Artsmarts</a> • 30 December 2018</p>
<h1>Frogs, candle people and dinosaurs. Strange Christmas cards from the past</h1>
<p>Like19</p>
<p>We live in an era of visual diversity. Modern artists invent new ways of self-expression – sometimes shocking, puzzling and puzzling. And many of us sometimes still involuntarily flash the thought: "But before it was different ..." I have to disappoint you: it wasn\'t. It turns out that in the second half of the XIX - early XX century, when the fine arts still obeyed rather strict rules, the producers of greeting cards "broke away" as much as they could. We have collected for you a whole collection of unusual and absurd postcards on which Christmas trees kiss with snowmen, pigs dance with gnomes, and Santa Claus looks not so cute and kind.</p>
<p>!<a href="https://arthive.net/res/media/img/oy800/article/668/<EMAIL> "Лягушки, люди-свечи и динозавры. Странные новогодние открытки из прошлого"" class="external-link">Frogs, candle people and dinosaurs. Strange Christmas cards from the past</a></p>
<p>Among all the companies that printed Christmas and New Year cards in the Victorian era, two stand out in particular – Raphael Tuck & Sons and De La Rue. But if the former most often just took an attractive picture and decorated it with a congratulatory inscription (sometimes completely inappropriate), the second, it seems, deliberately chose eccentric images. De La Rue printed greeting cards for only one decade (from 1875 to 1885), but during this time they managed to produce an impressive number of images that raise many questions.</p>
<p>!<a href="https://arthive.net/res/media/img/oy1200/article/326/<EMAIL> "За этим изображением явно скрыта очень загадочная история."" class="external-link">За этим изображением явно скрыта очень загадочная история.</a></p>
<p>За этим изображением явно скрыта очень загадочная история.</p>
<p>!<a href="https://arthive.net/res/media/img/oy800/article/c85/<EMAIL> "О том, что это новогодняя открытка, говорит только надпись «The Compliments of the Season» — одно из"" class="external-link">О том, что это новогодняя открытка, говорит только надпись «The Compliments of the Season» — одно из</a></p>
<p>О том, что это новогодняя открытка, говорит только надпись «The Compliments of the Season» — одно из традиционных английских поздравлений с зимними праздниками.</p>
<p>!<a href="https://arthive.net/res/media/img/oy800/article/f2b/<EMAIL> "А здесь поздравительная надпись хорошо замаскирована — ее можно разглядеть в левом нижнем углу. И от"" class="external-link">А здесь поздравительная надпись хорошо замаскирована — ее можно разглядеть в левом нижнем углу. И от</a></p>
<p>А здесь поздравительная надпись хорошо замаскирована — ее можно разглядеть в левом нижнем углу. И от этого сюжет становится еще менее понятным.</p>
<p>Why, instead of traditional Christmas stories, did De La Rue and similar companies choose anthropomorphic insects, flower fairies and dinosaurs? The thing is that the market of greeting cards at the end of the XIX century was oversaturated. And, in order to stand out against the general background of fabulous winter landscapes, blonde angels and cute kittens with bows, De La Rue hired artists who could create something new (by the way, this company was one of the very few, who collaborated with women artists). And the Victorians tried to buy as many unnecessary things as possible for Christmas (does not remind anyone?) and were more willing to choose non-standard images.</p>
<p>!<a href="https://arthive.net/res/media/img/oy800/article/e0c/<EMAIL> "За интерес викторианцев к доисторическим существам (и за появление таких открыток" class="external-link">For the interest of the Victorians in prehistoric creatures (and for the appearance of such cards), it is worth thanking</a> стоит благодарить")</p>
<p>For the interest of the Victorians in prehistoric creatures (and for the appearance of such cards), it is worth thanking Charles Darwin.</p>
<p>!<a href="https://arthive.net/res/media/img/oy800/article/bb7/<EMAIL> "Может быть, это ранний концепт-дизайн костюмов для эльфов Санта Клауса?"" class="external-link">Maybe it\'s an early concept costume design for Santa Claus\'s elves?</a></p>
<p>Maybe it\'s an early concept costume design for Santa Claus\'s elves?</p>
<p>Among other things, at that time, postcards were a source of visual novelty. Not everyone could afford to buy a picture, and postcards were inexpensive and colorful. People collected them, inserted them into frames, exchanged them. It was a way to share cute or funny pictures with others – something like Instagram is now.</p>
<p>!<a href="https://arthive.net/res/media/img/orig/article/6bc/<EMAIL> "Очень празднично, не правда ли?"" class="external-link">Very festive, isn\'t it?</a></p>
<p>Very festive, isn\'t it?
Very festive, isn\'t it?</p>
<p>!<a href="https://arthive.net/res/media/img/oy800/article/835/<EMAIL> "Очень милые котики, которые… сидят на мундштуке?"" class="external-link">Very cute cats who... sitting on the mouthpiece?</a></p>
<p>Very cute cats who... sitting on the mouthpiece?</p>
<p>!<a href="https://arthive.net/res/media/img/oy800/article/5ef/<EMAIL> "А вот факельное шествие птиц выглядит немного зловеще. Хотя, может быть, они несут огоньку котикам с"" class="external-link">But the torchlight procession of birds looks a little ominous. Though maybe they\'re bringing fire to the cats with</a></p>
<p>But the torchlight procession of birds looks a little ominous. Although, maybe they are bringing fire to the cats from the previous postcard?</p>
<p>Let\'s return briefly to the company Raphael Tuck & Sons, who printed hundreds of postcards on a variety of occasions, not always caring that the picture somehow correlated with the text. In their heritage, you can also find a lot of strange postcards, although they are lost in the general flow. Just look at the revived Christmas pudding or dogs playing the bagpipes.</p>
<p>!<a href="https://arthive.net/res/media/img/oy800/article/7b6/<EMAIL> "Рыдающие дети — лучший способ поздравить близких. Хотя плачут они из-за того, что угощение закончило"" class="external-link">Sobbing children are the best way to congratulate loved ones. Although they cry because the treat has ended.</a></p>
<p>Sobbing children are the best way to congratulate loved ones. Although they cry because the treat is over.</p>
<p>!<a href="https://arthive.net/res/media/img/orig/article/0cc/<EMAIL> "Может быть, именно так в Шотландии раньше праздновали Рождество?"" class="external-link">Maybe that\'s how Scotland used to celebrate Christmas?</a></p>
<p>Maybe that\'s how Scotland used to celebrate Christmas?</p>
<p>!<a href="https://arthive.net/res/media/img/orig/article/977/<EMAIL> "«Алиса, это пудинг. Пудинг, это Алиса»."" class="external-link">"Alice, it\'s pudding. Pudding, it\'s Alice."</a></p>
<p>«Alice, it\'s pudding. Pudding, it\'s Alice."</p>
<p>Frequent guests on vintage New Year\'s cards were pigs - a symbol of the coming 2019. And often they were depicted with bags of money or surrounded by coins. The fact is that in many Teutonic and Scandinavian traditions, the pig symbolized good luck and prosperity. So, giving loved ones "pig" cards, people did not mean anything bad, but wished each other financial well-being.</p>
<p>!<a href="https://arthive.net/res/media/img/orig/article/131/<EMAIL> "Это мини-пиг или просто очень большой мухомор?"" class="external-link">Is it a mini-pig or just a very large fly agaric?</a></p>
<p>Is it a mini-pig or just a very large fly agaric?</p>
<p>!<a href="https://arthive.net/res/media/img/orig/article/be6/<EMAIL> "Так люди поздравляли друг друга с наступлением 1910 года. Не забывайте, это пожелание благополучия."" class="external-link">So people congratulated each other on the onset of 1910. Do not forget, this is a wish for well-being.</a></p>
<p>So people congratulated each other on the onset of 1910. Do not forget, this is a wish for well-being.</p>
<p>!<a href="https://arthive.net/res/media/img/oy1000/article/867/<EMAIL> "Четырехлистный клевер — знаменитый символ удачи — тоже часто встречается на открытках с поросятами."" class="external-link">Four-leaf clover - the famous symbol of good luck - is also often found on postcards with piglets.</a></p>
<p>Four-leaf clover - the famous symbol of good luck - is also often found on postcards with piglets. But is there anything in this picture that can distract from the gnome\'s facial expression?</p>
<p>!<a href="https://arthive.net/res/media/img/orig/article/f7b/<EMAIL> "Так вот что происходит в хлеву, пока никто не видит!"" class="external-link">So that\'s what happens in the barn, while no one sees!</a></p>
<p>So that\'s what happens in the barn, while no one sees!</p>
<p>But why there are so many frogs and insects on Christmas cards is not entirely clear. -   [](https://www.instagram.com/arthivecom/)</p>
<p>-   <a href="https://medium.com/@artchive" class="external-link">Medium page</a>
-   <a href="https://telegram.me/arthivebot" class="external-link">Telegram Bot</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>