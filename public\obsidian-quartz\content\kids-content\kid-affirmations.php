<?php
// Auto-generated blog post
// Source: content\kids-content\kid-affirmations.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '20 WORDS of Affirmation FOR EVERY CHILD';
$meta_description = '20 WORDS of Affirmation FOR EVERY CHILD';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '20 WORDS of Affirmation FOR EVERY CHILD',
  'author' => 'ParentlineQLDNT',
  'date' => '2025-05-20',
  'excerpt' => '20 WORDS of Affirmation FOR EVERY CHILD',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\kids-content\\kid-affirmations.md',
);

// Post content
$post_content = '<p>20 WORDS of Affirmation FOR EVERY CHILD</p>
<p>1. You make me smile.
2. You\'re so interesting.
3. I love how your mind works.
4. I\'m so grateful to have you.
5. You are so beautiful on the inside and out.
6. You give the best hugs.
7. You\'re so brave.
8. That was such a kind thing to do.
9. You\'re the kind of friend I wanted when I was your age.
10. I appreciate you more than you know.
11. You have such great ideas.
12. I just know you\'re going to be successful.
13. I love how you never give up.
14. I\'m so happy when you\'re around.
15. There is no one quite like you.
16. I love that I can rely on you.
17. You are so easy to love.
18. I can see your inner strength.
19. You have some amazing gifts.
20. I am so proud of you.</p>
<p>@ParentlineQLDNT</p>
<p><figure class="sketch-frame">
<!-- vintage-frame, minimal-frame, sketch-frame -->
<img src="../../img/edutainment/affirmation.jpg" alt="" data-commentary="20 WORDS of Affirmation FOR EVERY CHILD">
  <figcaption>Your caption text</figcaption>
</figure></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>