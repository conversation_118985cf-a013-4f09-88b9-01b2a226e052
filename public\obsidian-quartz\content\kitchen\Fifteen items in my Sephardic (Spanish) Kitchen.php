<?php
// Auto-generated blog post
// Source: content\kitchen\Fifteen items in my Sephardic (Spanish) Kitchen.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '1) Clamato Tomato Cocktail';
$meta_description = 'I\'m putting this together for a class I am taking. But there was no shopping before hand. These are just Spanish related items that exist in my Kitche...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '1) Clamato Tomato Cocktail',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'I\'m putting this together for a class I am taking. But there was no shopping before hand. These are just Spanish related items that exist in my Kitche...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\kitchen\\Fifteen items in my Sephardic (Spanish) Kitchen.md',
);

// Post content
$post_content = '<p>I\'m putting this together for a class I am taking. But there was no shopping before hand. These are just Spanish related items that exist in my Kitchen.</p>
<p>![[wholeparty.jpg]]</p>
<h2>1) Clamato Tomato Cocktail </h2>
<p>![[clamato.jpg]]</p>
<p>Some might recognize this if they drink Bloody Mary\'s. I don\'t drink alcohol more than once in a blue moon. These are good on their own. I used to think tomato juice was nasty. But I loved spaghetti-os. Try it it\'s good.</p>
<h2>2) Vegetarian Refried Beans</h2>
<p>![[beans.jpg]]</p>
<p>Normally refried beans contains lard / pork fat. If you are vegetarian, kosher, or halal, these are a godsend.</p>
<h2>3) Chili Powder</h2>
<p>![[chilipowder.jpg]]</p>
<p>I really actually don\'t like Chili Powder all that much. I\'ll use it sometimes in rice or in meat marinades. This is how much I have used out of the bottle in four years.</p>
<h2>4) Corn Husks </h2>
<p>![[cornhusk.jpg]]</p>
<p>I bought these years ago and have never made tamales. Tamales are awesome and made from maze / corn meal being steamed in these husks. Think about all the disposable plates and plastic packaging we use, and know that native Mexicans used Corn Husks in many of the same ways to package and serve food with.</p>
<h2>5) Crema </h2>
<p>![[crema.jpg]]</p>
<p>This is like sour cream, but so much better. You got to buy it at the Tiendas, and there are a handful around Asheville. I like los Nenes on Patton and Haywood.</p>
<h2>6) Lebanese Blossom & Rose Waters and Sesame Crackers</h2>
<p>![[lebanese.jpg]]</p>
<p>Okay. Okay. I hear you asking. What does Lebanon have to do with the Spanish language? Many Sephardics fleeing the Iberian Peninsula in 1492 from the genocide of the Spanish Inquisition found acceptance and a chance to start over in the Ottoman Empire, which included Turkey, Syria, Palestine, and Lebanon. Spanish did live in these places. If you want to learn more about that overlap, two endangered/extinct languages to learn about are Mozarabic, and Haketia. From learning a bit about those languages you can learn a lot about Spanish in the Islamic world. The orange blossom water does have a spanish name translation agua de azahar on the front.</p>
<h2>7) Limon</h2>
<p>![[limon.jpg]]</p>
<p>Every Spanish household will have some flavor of citrus fruit around. I have a lemon, a grape fruit, and Nellie & Joe\'s Key Lime Juice. This product is produced in Key West, Florida. Likely through Migrant Farming labor.</p>
<h2>8) Queso Blanco</h2>
<p>![[quesoblanco.jpg]]</p>
<p>This stuff is good cold, but even better when cooked till it\'s gooey. I like cooking a big piece with a sandwich patty and it makes the sandwich ten times better. You can also fry it. It goes good with sweets too.</p>
<h2>9) Sopapilla Dust</h2>
<p>![[sopapilladust.jpg]]</p>
<p>If you have ever had Churros, you likely had this. I grew up with this made from sugar and cinnamon and not already mixed. This type uses confectionary sugar. Use granulated sugar if you want the texture of sugar crystal.</p>
<h2>9) Salsas - herdez Guacamole, and Salsa mild</h2>
<p>![[herdezsalsa.jpg]]</p>
<h2>10) Fila Manila</h2>
<p>![[filamanila.jpg]]</p>
<p>Believe it or not, there is a lot of Spanish in the Phillipines. For over a century the Phillipines was conquered by the Spanish where the people, land, and resources were exploited and ravaged by colonialism. Part of colonialism is imposing a more dominant language. The English had an all or nothing approach where you speak their language perfectly, or you were nothing to them. For the Spanish, they would bring in loan words and take over other languages bit by bit. So there are dozens of languages spoken in the Phillipines. Two that have the most resemblance to Spanish are Cebuano and Tagalog. I haven\'t opened this sauce or know what it tastes like yet, but I wanted to buy it so I did.</p>
<h2>11) San Jose Kandela</h2>
<p>![[sanjose.jpg]]</p>
<p>This is a very roman catholic thing to find in a Spanish household. But Saint Joseph was also a significant figure to Sephardic Jews. San Jose is named ofter Saint Joseph.</p>
<h2>12) Sopes</h2>
<p>![[sope.jpg]]</p>
<p>These are great. They are like really thick tortillas you can pile other foods on and cook to be super tasty Sopes.</p>
<h2>13) Tostadas</h2>
<p>![[tostada.jpg]]</p>
<p>These are one of those perfect snack items that I can enjoy and give my dog as a treat. They are unsalted. These are crushed and stale, however.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>