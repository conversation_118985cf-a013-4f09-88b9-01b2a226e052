<?php
// Auto-generated blog post
// Source: content\kitchen\First Ever Fern Fest - June 28, 2019.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'First Ever Fern Fest - June 28, 2019';
$meta_description = 'First Ever Fern Fest - June 28, 2019 April will be leading the kitchen operations for the inaugural Fern Fest. Drawing on experience staffing kitchens...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'First Ever Fern Fest - June 28, 2019',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'First Ever Fern Fest - June 28, 2019 April will be leading the kitchen operations for the inaugural Fern Fest. Drawing on experience staffing kitchens...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\kitchen\\First Ever Fern Fest - June 28, 2019.md',
);

// Post content
$post_content = '<p>First Ever Fern Fest - June 28, 2019</p>
<p>April will be leading the kitchen operations for the inaugural Fern Fest. Drawing on experience staffing kitchens at several small Asheville-area festivals, her culinary approach emphasizes environmental responsibility and anti-oppression principles. The kitchen welcomes anyone interested in assisting, contributing their skills, collaborating on dishes, or even offering new techniques. However, harassment and inappropriate behavior will not be tolerated to ensure a safe and inclusive environment for everyone. Anyone asked to leave is expected to do so respectfully.</p>
<p>Our kitchen thrives on the generosity of food lovers. Donations of fresh, high-quality ingredients will directly enhance the meals we share. Food donation drop-offs will begin on _____.</p>
<p>Please ensure all potatoes are washed before entering the kitchen and before eating! Let\'s all help maintain clean communal spaces to prevent illness while camping, as not everyone has the same digestive resilience.</p>
<p>Fern Hollow\'s journey began in 2013 when a cherished friend planted nearly one hundred saplings, laying the foundation for a future food forest. For over six years, this 5.5-acre plot on the Miller family farm, the vision of Erik and Olive, has served as a vital center for permaculture, rehabilitation, and education. It’s located in a small, rural community facing economic challenges in accessing nutritious food. In 2018, Erik and Olive, the dedicated partners behind Fern Hollow, celebrated their marriage on this land with 170 of their closest loved ones. Recognizing the shared passion for music and farm-fresh food within their community, a group of friends decided to organize a small music festival.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>