<?php
// Auto-generated blog post
// Source: content\mind-mapping.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Mind Mapping';
$meta_description = 'Mind Mapping - A. A. Chips';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Mind Mapping',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'Mind Mapping - A. A. Chips',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\mind-mapping.md',
);

// Post content
$post_content = '<p>I do not identify as <em>Creative</em>. I believe it is a misleading belief system. I support and encourage <em>Decomposition</em>. <em>Decomposition</em> is necessary to our day to day lives. When we benefit from and are moved by Art, that Art was a process of <em>Creative Decomposition</em>. As Designers we are often taking elements that are already created, and <em>\'Breaking them Down\'</em> into new meaning. This has always been a crucial role in any ecosystem, natural or otherwise, for <em>Decomposition</em> by fungi. Incubation at its finest. In the past century we have filled our planet with garbage. It can be a moral and even sacred duty to play the role as a Decomposer in this weird time that we are all alive. This belief system, which I have and believe to be better grounded in my educational background, impacts how I navigate some of these subject matters. 
<h2>Creative Thinking</h2>
Creativity is built on foundations. It requires having consumed and reflected on bricks of other peoples ideas. Anecdotally you can ask artists you deem successful and many will tell you that there are no original ideas. Those of us that exist in this ecosystem are constantly recycling new meaning and perspective into byproducts of what came before us. Do you see where this is going..?</p>
<p>A helpful visual metaphor for this may be worms in compost.
<a href="https://laidbackgardener.blog/2020/03/29/composting-with-worms/" class="external-link">Composting With Worms - Laidback Gardener</a></p>
<h2>Mind Mapping</h2>
A Mind Map is a helpful depiction, diagram, or visualization of a complex thought process. Diverse brain type \'<em>Creatives</em>\' may go about this process in many different ways, or not at all. In the advanced digital age, it\'s easier than ever to do Mind Mapping, AKA <em>Linking your Thinking</em> <a href="https://notes.linkingyourthinking.com/Cards/%2B+Start+Here" class="external-link">+ Start Here Building your Brainforest - LYT Kit (linkingyourthinking.com)</a>
<h3>Visual Metaphor</h3>
I have been using Obsidian as a Markdown Note Editor for the past year. As someone who writes a lot, I have thousands of notes with linked attachments. One feature of Obsidian.md is Graph View. This nodes out every single note document in your Vault folder. If those notes are linked together with features like hash tags or anchor tags, there are links drawn in this map.

<h3>Attribute Listing</h3>
Decomposers often abide several the following qualities and principles:
+ Can think of themselves, others, and world around in Ecological terms
+ Resourcefulness and sometimes stubbornness around repurposing the wastes of society
+ Often incompatible with figures of authority and power, and oppressive social systems. 
+ May not have a lot of money or privileges in civilized cultures. 
+ May live very quiet lives. Can become a center of attention when witnessed by normality, of whom is both fascinated and terrified.
+ Can have a lot of experience living outdoors in urban and wooded settings. 
+ Very frugal with money, values relationships and experiences over decadence and luxury.
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>