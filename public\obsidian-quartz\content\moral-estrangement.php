<?php
// Auto-generated blog post
// Source: content\moral-estrangement.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Moral Estrangement';
$meta_description = 'Moral Estrangement - A. A. Chips';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Moral Estrangement',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'Moral Estrangement - A. A. Chips',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\moral-estrangement.md',
);

// Post content
$post_content = '<p>Moral Estrangement: When Cutting Ties is About More Than Abuse</p>
<p>If you\'ve ever felt like you needed to choose between your family and your values, you\'re not alone. Moral estrangement is a deeply painful and complex form of estrangement – one where the rupture isn\'t just about overt abuse or neglect, but about fundamental moral and existential incompatibility.</p>
<p>In this post, we\'ll explore what moral estrangement is, how it can affect you, and how to hold onto yourself when you\'ve made the difficult decision to cut ties with toxic family members.</p>
<h3><strong>What is Moral Estrangement?</strong></h3>
<p>Moral estrangement is a form of estrangement that occurs when family members hold values or beliefs systems that are fundamentally incompatible with your own. This can manifest in many ways, such as:</p>
<p>* Parents who uphold harmful systems or ideologies that actively undermine your identity, relationships, or sense of justice.
* Family members who engage in or enable cruelty, bigotry, or discrimination against people you love.
* Parents who are complicit in or turn a blind eye to harm or abuse, making their "care" for you feel conditional or hypocritical.</p>
<h3><strong>How to Recognize Moral Estrangement</strong></h3>
<p>Moral estrangement can be difficult to recognize, especially when it\'s not about overt abuse or neglect. Here are some signs that you may be experiencing moral estrangement:</p>
<p>* You feel a deep sense of discomfort or anxiety when discussing certain topics with your family members.
* You feel like you need to hide parts of yourself or your relationships in order to avoid conflict or judgment.
* You feel like your family members are incapable of truly understanding or loving you for who you are.</p>
<h3><strong>Why Moral Estrangement is So Isolating</strong></h3>
<p>Moral estrangement can be incredibly isolating because it feels invisible. People may not understand why you\'re cutting ties with your family, or they may see it as a personal failing rather than a necessary act of self-preservation.</p>
<p>Here are some common phrases that can make moral estrangement feel even more isolating:</p>
<p>* "But they\'re your parents!"
* "Can\'t you just avoid certain topics?"
* "They didn\'t beat you, so how bad could it be?"</p>
<h3><strong>How to Hold Onto Yourself</strong></h3>
<p>1. <strong>Name it as a form of violence.</strong>  
    Recognize that moral estrangement is a form of violence, even if it\'s not physical. Acknowledge that you\'re not just losing your family members, you\'re losing the illusion that they could ever truly see or love you.
2. <strong>Seek "witnesses."</strong>  
    Look for communities where others have cut ties over values (e.g., anti-racist or LGBT+ spaces, ex-religious groups). Phrases like "estranged due to political/moral differences" or "chosen family over blood" might help you find your people.
3. <strong>Let grief and rage coexist with relief.</strong>  
    It\'s okay to ache for the parents they could\'ve been while knowing you made the only possible choice. Acknowledge your feelings and give yourself permission to feel whatever comes up.
4. <strong>Reclaim your lineage on your terms.</strong>  
    You can honor your family members\' positive qualities while rejecting their harmful beliefs systems. You can choose to define what it means to be part of your family, rather than letting their worst parts define you.</p>
<h3><strong>Conclusion</strong></h3>
<p>Moral estrangement is a difficult and complex topic, but it\'s not impossible. By recognizing the signs of moral estrangement, seeking out supportive communities, and holding onto yourself, you can make it through this difficult time. Remember that you\'re not alone, and that your decision to cut ties is not a failing – it\'s a necessary act of self-preservation.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>