<?php
// Auto-generated blog post
// Source: content\playlists\assets\90s\Fields of Gold (cover) by GandaBoys.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Fields of Gold (cover) by GandaBoys';
$meta_description = 'https://www.youtube.com/watch?v=Fbk5gtZvh9Q  Fields of Gold cover by GandaBoys !https://yt3.ggpht.com/ytc/AIdromJm29L6zXVb5zWkzolE0Z9naAxcPiwETfSTFU-B...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Fields of Gold (cover) by GandaBoys',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'https://www.youtube.com/watch?v=Fbk5gtZvh9Q  Fields of Gold cover by GandaBoys !https://yt3.ggpht.com/ytc/AIdromJm29L6zXVb5zWkzolE0Z9naAxcPiwETfSTFU-B...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\90s\\Fields of Gold (cover) by GandaBoys.md',
);

// Post content
$post_content = '<p>https://www.youtube.com/watch?v=Fbk5gtZvh9Q</p>
<h1>Fields of Gold (cover) by GandaBoys</h1>
<p><a href="https://yt3.ggpht.com/ytc/AIdro_mJm29L6zXVb5zWkzolE0Z9naAxcPiwETfST_FU-BF8mQ=s48-c-k-c0x00ffffff-no-rj" class="external-link">![</a>](https://www.youtube.com/@TheGandaBoys)
<em>*</em>*
<a href="https://www.youtube.com/@TheGandaBoys" class="external-link">The Ganda Boys</a></p>
<p>13.6K subscribers</p>
<p><__slot-el></p>
<p>Subscribe</p>
<p><__slot-el></p>
<p>161</p>
<p>Share</p>
<p>Download</p>
<p>Clip</p>
<p>Save</p>
<p>6,837 views Apr 26, 2021</p>
<p>“We turn our Ganda Boys distinctive voices to a well known song by Sting “Fields of Gold” using the Adungu from Ugandan folk music. Recorded live in London, with the Orchestra, we’ve kept the folk approach in doing this song (such descriptive lyrics!), thus creating a unique and different interpretation of the song. String arrangement again by Craig Pruess. Lead vocals by Denis Mugagga; Adungu and harmony singing by Daniel Sewagudde.” www.gandaboys.com www.gandafoundation.co.uk Facebook: Ganda Boys Instagram: @gandaboys Twitter: @Gan</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>