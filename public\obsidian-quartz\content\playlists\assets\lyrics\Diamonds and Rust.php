<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\Diamonds and Rust.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Dm A# F Gm C Dm';
$meta_description = 'Intro Dm A F Gm C Dm d|---------------------------------------------------------------------| A|------------------------------------------------------...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Dm A# F Gm C Dm',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'Intro Dm A F Gm C Dm d|---------------------------------------------------------------------| A|------------------------------------------------------...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Diamonds and Rust.md',
);

// Post content
$post_content = '<p>[Intro]
Dm A# F Gm C Dm</p>
<p>d|---------------------------------------------------------------------|
A|---------------------------------------------------------------------|
F|--------------7h9~---7/9--7---7--------------------------------------|
C|---------7/9---------------10----9-----9h10-9--7-9-------------------|
G|-7-9-10-----------------------------10--------------10--9---5-/-7~---|
D|---------------------------------------------------------------------|</p>
<p>Dm                             A#
I\'ll be damned, here comes your ghost again 
               F
But that\'s not unusual 
                   C
It\'s just that the moon is full 
                   Dm
And you decided to call</p>
<p>Dm                          A#
And here I sit, hand on the telephone
                F
Hearing the voice I\'d known 
            C
A couple of light years ago 
                    Dm
Headed straight for a fall</p>
<p>As I remember your eyes were bluer than robins eggs,</p>
<p>My poetry was lousy you said..</p>
<p>Where are you calling from?</p>
<p>A booth in the Midwest..</p>
<p>Ten years ago I bought you some cufflinks,</p>
<p>YOu bought me something,</p>
<p>A#                        C
But we both know what memories can bring 
                    Dm
They bring diamonds and rust 
       A#                         C
Yes we both know what memories can bring 
                       Dm
They bring diamonds and rust</p>
<p>Well you burst on the scene, already a legend</p>
<p>The unwashed phenomenon,</p>
<p>The original Vagabond,</p>
<p>Strayed right into my arms..</p>
<p>And there you stayed,</p>
<p>Temporarily lost at sea,</p>
<p>The madonna was yours for free,</p>
<p>Yes the girl on the half shell, could keep you unharmed 
Am                                                               Gm
Now I see you standing with brown leaves all around and snow in your hair 
           Am                                                         Gm
Now we\'re smiling out the window of the crummy hotel over Washington Square 
A#                                                      F
Our breath comes in white clouds, mingles and hangs in the air 
D#maj7                                                      Am    A# C Dm
Speaking strictly for me we both could\'ve died then and there</p>
<p>Dm                               A#
Now you\'re telling me you\'re not nostalgic 
                           F
Then give me another word for it 
                       C
You were so good with words
                       Dm
And at keeping things vague</p>
<p>Dm
Cause I need some of that vagueness now 
                        A#               F
It\'s all come back too clearly, yes, I love you dearly 
                          C                               Dm
And if you\'re offering me diamonds and rust, I\'ve already paid</p>
<p>A#                        C
But we both know what memories can bring 
                    Dm
They bring diamonds and rust 
       A#                         C
Yes we both know what memories can bring 
                       Dm
They bring diamonds and rust</p>
<p>A#           C           Dm
Diamonds, diamonds and rust
 A#           C           Dm
Diamonds, diamonds and rust 
A#           C           Dm
Diamonds, diamonds and rust 
A#           C           Dm
Diamonds, diamonds and rust</p>
<p>This tab is from the acoustic versions of Judas Priest.
Any questions let me know email (<EMAIL>)</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>