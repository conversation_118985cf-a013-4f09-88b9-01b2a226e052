<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\Everything Stays & Todo se Queda.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Everyting Stays';
$meta_description = 'Everyting Stays Rebecca Sugar Cover English & Spanish    Let\'s go in the garden You\'ll find something waiting Right there where you left it Lying upsi...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Everyting Stays',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'Everyting Stays Rebecca Sugar Cover English & Spanish    Let\'s go in the garden You\'ll find something waiting Right there where you left it Lying upsi...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Everything Stays & Todo se Queda.md',
);

// Post content
$post_content = '<p>**</p>
<p>Everyting Stays</p>
<p>Rebecca Sugar Cover English & Spanish</p>

<p>Let\'s go in the garden</p>
<p>You\'ll find something waiting</p>
<p>Right there where you left it</p>
<p>Lying upside down</p>

<p>When you finally find it</p>
<p>You\'ll see how it\'s faded</p>
<p>The underside is lighter</p>
<p>When you turn it around</p>

<p>Everything stays</p>
<p>Right where you left it</p>
<p>Everything stays</p>
<p>But it still changes</p>
<p>Ever so slightly</p>
<p>Daily and nightly</p>
<p>In little ways</p>
<p>When everything stays</p>

<p>Go down to the ocean</p>
<p>The crystal tide is rising</p>
<p>Water\'s gotten higher</p>
<p>As the shore washes out</p>

<p>Keep your eyes wide open</p>
<p>Even when the sun is blazing</p>
<p>The moon controls the tide</p>
<p>It could cause you to drown</p>

<p>Everything stays</p>
<p>Right where you left it</p>
<p>Everything stays</p>
<p>But it still changes</p>
<p>Ever so slightly</p>
<p>Daily and nightly</p>
<p>In little ways</p>
<p>When everything stays</p>

<p>Baja al oceano</p>
<p>Subiendo Máreacristale</p>
<p>Agua sehavuelto masalta</p>
<p>Desde la orilla lávase</p>

<p>Manténojos abiertos</p>
<p>Pesar deSol quemando</p>
<p>La Luna y Marea</p>
<p>Podría te ahogar</p>
<p>**</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>