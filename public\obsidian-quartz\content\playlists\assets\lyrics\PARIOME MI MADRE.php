<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\PARIOME MI MADRE.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'PARIOME MI MADRE';
$meta_description = 'PARIOME MI MADRE Cuando yo nací     nació la tristura.   Pariome mi madre     en una noche oscura,   ni gallo cantaba     ni perro ladraba   s...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'PARIOME MI MADRE',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'PARIOME MI MADRE Cuando yo nací     nació la tristura.   Pariome mi madre     en una noche oscura,   ni gallo cantaba     ni perro ladraba   s...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\PARIOME MI MADRE.md',
);

// Post content
$post_content = '<h1>PARIOME MI MADRE</h1>
<p>Cuando yo nací     nació la tristura.  
Pariome mi madre     en una noche oscura,  
ni gallo cantaba     ni perro ladraba  
sino la aguilía     negras voces daba.  
  
Pariome mi madre,     criome mi tía  
con yerbas del campo     hizo cama y cuna,  
púsome pañales     y echome en la cuna  
por nombre me puso     niña sin fortuna.  
  
En aquel navío     mi bien se embarcó,  
Alzara las velas     se fue y me dejó,  
vicios y regalos     con él los llevó.  
  
Ya crecen las hierbas     y dan de color,  
este, mi corazón,     vive con dolor.  
Ya crecen las hierbas     y dan de morado,  
este mi corazón     vive con cuidado.  
  
Ya crecen las hierbas     y dan de amarillo,  
este, mi corazón,     vive con suspiros.  
Ya crecen las yerbas     y dan de verdura,  
este mi corazón     vive con tristura.  
  
Ansias y cuidados     a mí me dejó  
mi bien y no mal,     mi cirio pascual,  
mi antorcha encendida,     semana y sabbat.</p>
<p>Poesía oral sefardí, ladino, judeo-español</p>
<p>---</p>
<p>[versión de Tánger cantada por Simhá Melul a Manuel Manrique de Lara en 1915]</p>
<p>Notas de Paloma Díaz-Mas:</p>
<p>- aguilía «aguililla», cuyos graznidos se consideran aquí de mal agüero
- vicios «deleites, placeres»
- cuidados «preocupaciones»</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>