<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\Passenger - Let her go.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Well, you only need the light when it\'s burning low';
$meta_description = 'Chorus   Well, you only need the light when it\'s burning low   Only miss the sun when it starts to snow   Only know you love her when you let her go  ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Well, you only need the light when it\'s burning low',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'Chorus   Well, you only need the light when it\'s burning low   Only miss the sun when it starts to snow   Only know you love her when you let her go  ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Passenger - Let her go.md',
);

// Post content
$post_content = '<p>[Chorus]  
Well, you only need the light when it\'s burning low  
Only miss the sun when it starts to snow  
Only know you love her when you let her go  
Only know you\'ve been high when you\'re feeling low  
Only hate the road when you\'re missing home  
Only know you love her when you let her go</p>
<p>[Post-Chorus]  
<a href="https://genius.com/1821009/Passenger-let-her-go/Only-know-you-love-her-when-you-let-her-go" class="external-link">And you let her go</a>  
  
[Verse 1]  
Staring at the bottom of your glass  
Hoping one day you\'ll make a dream last
But dreams come slow and they go so fast  
You see her when you close your eyes
Maybe one day you\'ll understand why  
Everything you touch surely dies
  
[Chorus]  
But you only need the light when it\'s burning low  
Only miss the sun when it starts to snow  
Only know you love her when you let her go  
Only know you\'ve been high when you\'re feeling low  
Only hate the road when you\'re missing home
Only know you love her when you let her go</p>
<p>[Verse 2]  
Staring at the ceiling in the dark  
Same old empty feeling in your heart  
\'Cause love comes slow and it goes so fast  
Well, you see her when you fall asleep  
But never to touch and never to keep  
\'Cause you loved her too much and you dived too deep
  
[Chorus]  
Well, you only need the light when it\'s burning low  
Only miss the sun when it starts to snow  
Only know you love her when you let her go  
Only know you\'ve been high when you\'re feeling low  
Only hate the road when you\'re missing home  
Only know you love her when you let her go</p>
<p>[Post-Chorus]  
And you let her go, oh, woah, mm, oh  
And you let her go, oh, woah, uh, no  
Well, you let her go  
  
[Chorus]  
\'Cause you only need the light when it\'s burning low  
Only miss the sun when it starts to snow  
Only know you love her when you let her go  
Only know you\'ve been high when you\'re feeling low  
Only hate the road when you\'re missing home  
Only know you love her when you let her go</p>
<p>[Outro]  
[\'Cause you only need the light when it\'s burning low  
Only miss the sun when it starts to snow  
Only know you love her when you let her go  
Only know you\'ve been high when you\'re feeling low  
Only hate the road when you\'re missing home
Only know you love her when you let her go  
And you let her go</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>