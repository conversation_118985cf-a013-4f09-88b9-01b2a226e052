{"playlistTitle": "Mixed Favorites", "playlistDescription": "A diverse collection of favorite songs across genres", "songs": [{"id": 1, "title": "Turn The World Around", "artist": "<PERSON>", "album": "Paradise in Gazankulu", "year": "1988", "duration": "6:29", "mp3Path": "assets/ashlist/TurntheWorldAround.mp3", "thumbnailPath": "assets/thumbs/turntheworldaround.jpg", "lyricsPath": "assets/lyrics/<PERSON> - Turn the World Around (Official Audio).md", "commentary": "This powerful song by <PERSON> speaks to the interconnectedness of all people and the importance of community. The lyrics emphasize how we all depend on each other and how our actions affect the world around us.", "genre": "Folk"}, {"id": 2, "title": "Heroes on Fire", "artist": "<PERSON><PERSON> & the Age of Wonderbeasts", "album": "<PERSON><PERSON> and the Age of Wonderbeasts Soundtrack", "year": "2020", "duration": "4:44", "mp3Path": "assets/ashlist/HeroesonFire-KipoWonderbeasts.mp3", "thumbnailPath": "assets/thumbs/heroesonfire.jpg", "lyricsPath": "", "genre": "Soundtrack"}, {"id": 3, "title": "Running with the Wolves", "artist": "Aurora", "album": "All My Demons Greeting Me as a Friend", "year": "2015", "duration": "3:49", "mp3Path": "assets/ashlist/RunningwiththeWolves.mp3", "thumbnailPath": "assets/thumbs/runningwiththewolves.jpg", "lyricsPath": "", "commentary": "Aurora's ethereal voice and mystical lyrics create a haunting atmosphere. This song explores themes of freedom, wildness, and connection with nature.", "genre": "Indie Pop"}, {"id": 4, "title": "Through Heaven's Eyes", "artist": "<PERSON>", "album": "The Prince of Egypt Soundtrack", "year": "1998", "duration": "5:25", "mp3Path": "assets/ashlist/heavenseyes.mp3", "thumbnailPath": "assets/thumbs/throughheavenseyes.jpg", "lyricsPath": "", "genre": "Soundtrack"}, {"id": 5, "title": "Rainbow Connection", "artist": "<PERSON><PERSON><PERSON> the <PERSON>", "album": "The Muppet Movie", "year": "1979", "duration": "4:00", "mp3Path": "assets/ashlist/RainbowConnection.mp3", "thumbnailPath": "assets/thumbs/kermit.avif", "lyricsPath": "", "commentary": "A timeless song about hope, dreams, and the magic of believing in possibilities. <PERSON><PERSON><PERSON>'s gentle delivery makes this both nostalgic and inspiring.", "genre": "Children's"}, {"id": 6, "title": "King Without a Crown", "artist": "<PERSON><PERSON><PERSON><PERSON>", "album": "Live at Stubb's", "year": "2005", "duration": "5:31", "mp3Path": "assets/ashlist/kingwithoutacrown.mp3", "thumbnailPath": "assets/thumbs/matisyahu.jpg", "lyricsPath": "", "genre": "Reggae"}, {"id": 7, "title": "<PERSON>", "artist": "<PERSON><PERSON><PERSON>", "album": "<PERSON>", "year": "2015", "duration": "5:56", "mp3Path": "assets/ShakeSugaree-R<PERSON>nonG<PERSON>dens-CottenCover.mp3", "thumbnailPath": "assets/thumbs/rhiannongiddens.jpg", "lyricsPath": "", "genre": "Folk"}, {"id": 8, "title": "Something Inside So Strong", "artist": "<PERSON><PERSON>", "album": "So Strong", "year": "1987", "duration": "5:29", "mp3Path": "assets/ashlist/SomethingInsideSoStrong.mp3", "thumbnailPath": "assets/thumbs/labisiffre.jpg", "lyricsPath": "", "genre": "Pop"}, {"id": 9, "title": "<PERSON><PERSON>'s Song", "artist": "<PERSON><PERSON><PERSON><PERSON>", "album": "Solace and Sorrow", "year": "2007", "duration": "6:07", "mp3Path": "assets/ashlist/SorrowsSongSJTucker.mp3", "thumbnailPath": "assets/thumbs/sjtucker.jpg", "lyricsPath": "", "genre": "Folk"}, {"id": 10, "title": "Yellow Brick Road", "artist": "Angus & Julia Stone", "album": "Down the Way", "year": "2010", "duration": "3:36", "mp3Path": "assets/ashlist/yellowbrickroad.mp3", "thumbnailPath": "assets/thumbs/yellowbrickroad.jpg", "lyricsPath": "", "genre": "Indie Folk"}, {"id": 11, "title": "Everything Stays", "artist": "<PERSON>", "album": "Adventure Time", "year": "2015", "duration": "1:31", "mp3Path": "assets/rainbow/everythingstays-oliviaolson.mp3", "thumbnailPath": "assets/thumbs/everythingstays.jpg", "lyricsPath": "", "genre": "Folk Rock"}, {"id": 12, "title": "Do it for Her", "artist": "<PERSON>", "album": "Steven <PERSON> Soundtrack", "year": "2014", "duration": "2:16", "mp3Path": "assets/doitforher-stevenuniverse.mp3", "thumbnailPath": "assets/thumbs/doitforher.jpg", "lyricsPath": "", "genre": "Folk Rock"}]}