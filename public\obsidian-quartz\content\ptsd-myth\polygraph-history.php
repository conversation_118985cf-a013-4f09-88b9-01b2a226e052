<?php
// Auto-generated blog post
// Source: content\ptsd-myth\polygraph-history.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '```cardlink';
$meta_description = 'At one time, Polygraph testing was seen as a gold standard of lie detection, and was used in interrogation with detained suspects of crimes. All the p...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '```cardlink',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'At one time, Polygraph testing was seen as a gold standard of lie detection, and was used in interrogation with detained suspects of crimes. All the p...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\ptsd-myth\\polygraph-history.md',
);

// Post content
$post_content = '<p>At one time, Polygraph testing was seen as a gold standard of lie detection, and was used in interrogation with detained suspects of crimes. All the polygraph did, was measure body indicators, like breathing, heart rate, brain activity. They can tell if these measures are elevated at any given moment, but this is not a reliable or valid indicator of if someone is lying. It may be because the person is scared and stressed out while being illegally detained for 16 hours and threatened into confessing to a crime they didn\'t do for a plea deal. If you want to learn more about exoneration cases over the past decades, the Innocence Project does amazing work on this.</p>
<p>```cardlink
url: https://innocenceproject.org/all-cases/
title: "Cases - Innocence Project"
host: innocenceproject.org
favicon: https://innocenceproject.org/wp-content/themes/ip-website/_include/img/favicons/favicon-32x32.png
```
<a href="https://innocenceproject.org/all-cases/" class="external-link">Cases - Innocence Project</a></p>
<p>One of my favorite political songs of all time is a biographical song about Cornelius Dupree by Cuban-American artist Lou Dominguez. Cornelius Dupree spent 30 years behind bars for a crime they did not commit, and refused to confess to.</p>
<p>```cardlink
url: https://www.youtube.com/watch?v=DXwB-h_BiQE
title: "Cornelius Dupree"
description: "Provided to YouTube by CDBabyCornelius Dupree · Lou DominguezWe the People℗ 2013 Lou DominguezReleased on: 2013-10-28Auto-generated by YouTube."
host: www.youtube.com
favicon: https://www.youtube.com/s/desktop/7029e4b3/img/logos/favicon_32x32.png
image: https://i.ytimg.com/vi/DXwB-h_BiQE/maxresdefault.jpg
```
<a href="https://www.youtube.com/watch?v=DXwB-h_BiQE" class="external-link">Cornelius Dupree - YouTube</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>