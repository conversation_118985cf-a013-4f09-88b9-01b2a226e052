<?php
// Auto-generated blog post
// Source: content\ptsd-myth\ptsd-outdated.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'PTSD is Outdated Language';
$meta_description = 'Challenging conventional wisdom about PTSD and mental health through research, advocacy, and lived experience. A collection of writings on mental health reform.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'PTSD is Outdated Language',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'Challenging conventional wisdom about PTSD and mental health through research, advocacy, and lived experience. A collection of writings on mental health reform.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\ptsd-myth\\ptsd-outdated.md',
);

// Post content
$post_content = '<h2>The Problem with "Post" Traumatic Stress</h2>
<p>The term _Post-Traumatic Stress Disorder_ (PTSD) is a relic of the 1980s, born from a narrow understanding of trauma as a linear, isolated event. Yet research consistently shows that trauma is rarely confined to the past:</p>
<p>- For survivors of domestic violence, systemic racism, or chronic illness, trauma is _ongoing_. The "post" erases their reality (Herman, 1992; van der Kolk, 2014).
- Moral injury—a collapse of one’s ethical framework due to betrayal by trusted institutions (e.g., military, clergy)—blurs the line between "event" and "aftermath" (Litz et al., 2009).
- Anticipatory trauma / \'Pre-TSD\' (e.g., police violence, climate anxiety) creates stress _before_ harm occurs, a phenomenon documented in marginalized communities (Carter, 2007).
    
The DSM’s "post" framing centers combat veterans while excluding those for whom trauma is cyclical or persistent.</p>
<h2>**Every Letter of "PTSD" Fails Survivors</h2>
<h3>"P" (Post): Trauma Isn’t Linear</h3>
<p>The myth of "post" trauma ignores:</p>
<p>- Ongoing Traumatic Stress Disorder (OTSD): Proposed by advocates for refugees and abuse survivors (Briere & Spinazzola, 2005).
- Pre-Traumatic Stress: Marginalized groups live with hypervigilance toward _future_ harm (e.g., transgender youth fearing harassment).</p>
<h3>"T" (Traumatic): The DSM’s Narrow Definition</h3>
<p>The DSM-5 requires trauma to involve "actual or threatened death, serious injury, or sexual violence." This excludes:</p>
<p>- Structural trauma: Poverty, racism, or ableism (APA, 2013; Williams et al., 2021).
- Emotional abuse: Gaslighting or coercive control, which can be as debilitating as physical violence (Stark, 2007).</p>
<h3>"S" (Stress): More Than a Stress Response</h3>
<p>Trauma reshapes the brain and body:</p>
<p>- Neurobiological changes: Hippocampal shrinkage, dysregulated HPA axis (Yehuda et al., 2015).
- Dissociation: A survival strategy, not a "symptom" (Lanius et al., 2010).
    
<h3>"D" (Disorder): Pathologizing Survival</h3></p>
<p>Calling trauma responses "disordered" ignores their adaptive roots:</p>
<p>- Hypervigilance: A rational response to danger (Perry, 2006).
- Post-Traumatic Growth: Many survivors integrate trauma into resilience (Tedeschi & Calhoun, 2004).</p>
<h3>From "Shell Shock" to "PTSD": A Political History</h3>
<p>The term _PTSD_ replaced _Shell Shock_ (WWI) and _Gross Stress Reaction_ (WWII) not for scientific clarity, but to:</p>
<p>1. Medicalize suffering, shifting blame from war to individual "disorder" (Young, 1995).
2. Exclude non-combat trauma, particularly affecting women and minorities (Scott, 1990).</p>
<p>"Shell Shock"—with its visceral imagery of shattered bodies—was arguably _more accurate_ for trauma’s systemic impact.</p>
<p>---</p>
<h2>A Call for Reform: Alternatives to "PTSD"</h2>
<h3>Adopt More Precise Language</h3>
<p>- For chronic trauma: _Persistent Traumatic Stress Disorder_ (PTSD) or _Complex Trauma_ (Herman, 1992).
    
- For moral injury: _Betrayal Trauma Syndrome_ (Freyd, 1996).
    
- For structural harm: _Structural Trauma Syndrome_ (Galtung, 1969).</p>
<h3>Decouple Trauma from "Disorder"</h3>
<p>- Trauma Spectrum Condition: Acknowledges diversity in responses.
- Injury Model: _Traumatic Stress Injury_ (TSI) mirrors physical trauma frameworks.</p>
<h3>Center Lived Experience in Diagnosis</h3>
<p>Survivor-led frameworks, like the <strong>Power Threat Meaning Framework</strong> (Johnstone & Boyle, 2018), reject pathologizing labels and ask:</p>
<p>- _What happened to you?_
    
- _How did you survive?_
    
- _What meaning did you make of it?_</p>
<p>---</p>
<h2>Language as Liberation</h2>
<p>The term _PTSD_ is outdated, exclusionary, and often harmful. By redefining trauma language, we can:</p>
<p>- Validate ongoing and anticipatory suffering.
- Challenge systems that cause trauma (e.g., militarism, racism).
- Empower survivors to frame their own experiences.
    
Our words shape our world. It’s time to speak trauma into truth.</p>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/Rorgjdvphek?si=yCIHeumEkD7Ikihm" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></p>
<h2>References</h2>
<p>- Herman, J. (1992). _Trauma and Recovery_.
    
- van der Kolk, B. (2014). _The Body Keeps the Score_.
    
- Litz, B. et al. (2009). "Moral injury and moral repair in war veterans."
    
- Carter, R. (2007). "Racism and psychological and emotional injury."
    
- Johnstone, L. & Boyle, M. (2018). _The Power Threat Meaning Framework_.</p>
<p>- Briere, J. & Spinazzola, J. (2005). "Ongoing traumatic stress disorder."
    
- Stark, E. (2007). _The Batterer as a Parent_.
    
- Yehuda, R. et al. (2015). "Neurobiological consequences of early-life trauma."
    
- Lanius, R. et al. (2010). "Dissociation and the dissociative disorders: past, present, and future."
    
- Perry, B. (2006). _The Boy Who Was Raised as a Dog_.
    
- Tedeschi, R. & Calhoun, L. (2004). _Posttraumatic Growth_.
    
- Young, J. (1995). _The Harmony of Illusions_.
    
- Scott, J. (1990). _Only Connect_.
    
- Freyd, J. (1996). "Betrayal trauma theory."
    
- Galtung, J. (1969). "Violence, peace, and peace research."</p>
<p>- Dr. Joy De Gruy Leary\'s work on Post Traumatic Slave Syndrome.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>