<?php
// Auto-generated blog post
// Source: content\ptsd-myth\rethink-ptsd.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Rethinking PTSD';
$meta_description = 'Challenging conventional wisdom about PTSD and mental health through research, advocacy, and lived experience. A collection of writings on mental health reform.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Rethinking PTSD',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'Challenging conventional wisdom about PTSD and mental health through research, advocacy, and lived experience. A collection of writings on mental health reform.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\ptsd-myth\\rethink-ptsd.md',
);

// Post content
$post_content = '<p>Is "Post-Traumatic" a misnomer? The traditional understanding of Post-Traumatic Stress Disorder (PTSD) often follows a linear timeline, assuming trauma occurs, then symptoms follow. However, trauma isn\'t always so straightforward. This linear view significantly affects our research and understanding.</p>
<p>The term "post" implies the event is over, but for many, trauma is ongoing. Flashbacks, nightmares, avoidance, and trust issues can persist for years after a traumatic event. This has led to the proposal of terms like "Ongoing" or "Complex" PTSD to better capture the experience of recurring trauma.</p>
<p>Furthermore, "Pre-TSD" suggests that individuals anticipating a traumatic event, such as a life-threatening illness or war zone deployment, might experience PTSD symptoms beforehand. These evolving terms reflect a shift towards acknowledging trauma\'s complexity and long-lasting impacts.</p>
<p>Dr. Joy DeGruy Leary\'s work on Post Traumatic Slave Syndrome highlights intergenerational trauma and its relevance to Black American history. Her insights underscore the importance of considering broader historical and societal contexts in trauma studies. Explore her work through her lecture on YouTube or a brief interview on AJ+. </p>

<p><a href="https://www.youtube.com/watch?v=BGjSday7f_8" class="external-link">Post Traumatic Slave Disorder</a>. </p>
<p><a href="https://www.youtube.com/watch?v=Rorgjdvphek" class="external-link">Dr. Joy’s six minute interview with AJ+.</a></p>
<p>https://www.joydegruy.com/post-traumatic-slave-syndrome</p>

<p>In any field, especially Medicine and Human Services, understanding trauma is crucial. Human error can lead to serious consequences. Professionals have a duty to minimize harm, which goes beyond following standard procedures. A process of inquiry is essential for finding the best solutions for clients. Awareness and up-to-date knowledge of trauma\'s complexities are vital for responsible practice.</p>
<p><a href="https://psychcentral.com/ptsd/types-of-ptsd" class="external-link">https://psychcentral.com/ptsd/types-of-ptsd</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>