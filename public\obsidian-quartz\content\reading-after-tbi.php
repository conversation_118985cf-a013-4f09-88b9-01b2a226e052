<?php
// Auto-generated blog post
// Source: content\reading-after-tbi.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'My Reading Journey after Traumatic Brain Injury';
$meta_description = 'My relationship with reading has fundamentally changed after a series of traumatic brain injuries. Dense blocks of text present a genuine barrier to my learning.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'My Reading Journey after Traumatic Brain Injury',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'My relationship with reading has fundamentally changed after a series of traumatic brain injuries. Dense blocks of text present a genuine barrier to my learning.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\reading-after-tbi.md',
);

// Post content
$post_content = '<h2>Reading after Traumatic Brain Injury</h2>
<p>As a kid I hated reading. As a young adult I loved reading. Now I have trouble reading.
I am always dedicated to learning new things and improve myself. I enjoy everything about reading. But it\'s gotten to the point where I haven\'t touched my books in a few years.</p>
<p>After a series of traumatic brain injuries, it\'s been harder to focus on reading text. I can look at the text for a few minutes, and realize I haven\'t read a word. Multiple incidents—from physical assaults to pedestrian bike accidents—left me with lasting cognitive challenges. Now, dense blocks of text present a genuine barrier to my learning.</p>
<h2>The New Reality</h2>
<p>My relationship with reading has fundamentally changed:</p>
<p>- Loud noises, crowds, and confrontational situations trigger mental breakdowns
- Processing long text passages has become significantly more difficult
- Traditional textbooks represent both a physical and cognitive burden</p>
<p>This isn\'t about devaluing literacy—it\'s about recognizing diverse learning needs in our digital age.</p>
<h2>Beyond the Financial Burden</h2>
<p>While cost is often discussed (and rightfully so), my concern extends beyond money:</p>
<p>- I don\'t want bulky, heavy books occupying my limited space
- The physical presence of books I struggle to read becomes a constant reminder of my limitations
- For materials I\'ll only reference once or twice, purchasing a full textbook is wasteful</p>
<h2>Practical Alternatives for Educators</h2>
<p>Instead of mandatory textbook purchases, consider:</p>
<p>1. <strong>Require texts with e-book options</strong> - Digital formats work with screen readers and accessibility tools
2. <strong>Share essential excerpts</strong> - If only specific sections are needed, provide digital copies of those pages
3. <strong>Embrace student-discovered resources</strong> - Students often find excellent open-source materials that explain concepts in beginner-friendly ways
4. <strong>Create accessible assignment prompts</strong> - Don\'t tie assignments to specific textbook pages without providing alternatives
5. <strong>Remember accessibility is equity</strong> - Required textbooks can create barriers for first-generation students, those with disabilities, and those facing financial hardships</p>
<h2>A Simple Request</h2>
<p>You don\'t need to announce that textbooks are optional. Just provide alternative pathways for students who face barriers—whether financial, physical, or cognitive.</p>
<p>The future of education isn\'t about abandoning valuable resources—it\'s about making knowledge accessible to everyone, regardless of their circumstances.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>