<?php
// Auto-generated blog post
// Source: content\safeguarding\olmsted-decision-supreme-court.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'This Supreme Court Case Changed Disability Rights';
$meta_description = 'This Supreme Court Case Changed Disability Rights - Section 8 Consulting Youtube Video Notes';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'This Supreme Court Case Changed Disability Rights',
  'author' => 'PBS Origins',
  'date' => '2024-07-24',
  'excerpt' => 'This Supreme Court Case Changed Disability Rights - Section 8 Consulting Youtube Video Notes',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\safeguarding\\olmsted-decision-supreme-court.md',
);

// Post content
$post_content = '<p><a href="https://www.youtube.com/watch?v=JTVwTuuqVkQ" class="external-link">How These Disabled Women Won Their Supreme Court Case - YouTube</a></p>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/JTVwTuuqVkQ?si=YwT44C6Es8D70ql6" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></p>
<p><strong>How These Disabled Women Won Their Supreme Court Case</strong></p>
<p>In 1999, two brave women with disabilities took on the state of Georgia. What happened next was a groundbreaking Supreme Court decision and a pivotal moment in disability rights. Despite this victory, challenges persist in transportation, employment, voting, reproductive rights, and online accessibility today.
The Olmsted Decision: A Landmark Supreme Court Case for Disability Rights</p>
<p>In 1995, the Supreme Court made a landmark decision in the Olmsted case, which greatly impacted the lives of people with disabilities. Two women, Lois Curtis and Elaine Wilson, both with cognitive and developmental disabilities, took on the state of Georgia for denying them the proper care and support they needed to live independently.</p>
<p>The women\'s case was a result of the lack of resources and support from the state, which led to them being confined to institutions and hospitals. They were not given the choice to live in their communities, despite the availability of community care programs that could provide them with the necessary support and resources.</p>
<p>The Olmsted decision expanded on the rights of Section 504 of the Rehabilitation Act, which prohibited discrimination against people with disabilities. The court ruled that the state of Georgia had to provide the necessary resources and support for Lois and Elaine to live independently, including the right to choose their own accommodations and inclusivity.</p>
<p>The case was a major victory for disability rights and paved the way for future activism and legislation. It showed that people with disabilities have the right to choose their own accommodations and to live independently, and that the state has a responsibility to provide the necessary resources and support to make this possible.</p>
<p>The Olmsted decision also highlighted the importance of community care programs and the need for states to provide the necessary resources and support for people with disabilities to live independently. It emphasized the need for people with disabilities to have a say in their own lives and to be able to make their own choices about their care and support.</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>