<?php
// Auto-generated blog post
// Source: content\safeguarding\trans-teens.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Trans Teens are the Strongest People Alive';
$meta_description = 'Trans Teens are the Strongest People Alive - Luxander Youtube Video Notes';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Trans Teens are the Strongest People Alive',
  'author' => 'Luxander',
  'date' => '2025-05-20',
  'excerpt' => 'Trans Teens are the Strongest People Alive - Luxander Youtube Video Notes',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\safeguarding\\trans-teens.md',
);

// Post content
$post_content = '<p><a href="https://www.youtube.com/shorts/tLIR7aKN5Ss" class="external-link">trans teens are the strongest people alive - YouTube</a></p>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/tLIR7aKN5Ss?si=gMYznJRmpxqLrl3w" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></p>
<p><a href="https://www.youtube.com/@LuxanderReal" class="external-link">Luxander</a>
1,952 views Mar 5, 2024</p>
<h1>The Unimaginable Strength of Trans Teens</h1>
<p>I recently watched a video by Luxander, a trans YouTuber, where he shared his thoughts on what it\'s like to be a trans teenager today. His words were so powerful that I felt compelled to share them with you.</p>
<p>As a non-binary person who grew up in a time when people were mostly neutral towards the idea of trans folks, I can only imagine how difficult it must be for trans teens today. The world has changed so much since I was a teenager, and not always for the better.</p>
<p>Luxander\'s words resonated deeply with me. As someone who has struggled with suicidal thoughts and feelings of hopelessness, I can only imagine how much harder it must be for trans teens today. The constant barrage of negativity and hatred from the outside world must be crushing at times.</p>
<p>But despite all of this, trans teens are still finding ways to survive and thrive. They are the strongest of us, and their resilience is truly inspiring.</p>
<p>As Luxander said, "I think it would be crushing. I think I would be deeply suicidal. I have come through a lot of that and I\'m on the other side of it." He also acknowledged the importance of having a therapist, saying "I would probably be very much on that side of like, I don\'t know if I can make it in this world now."</p>
<p>I am angry and hardened by my experiences, but I also know that I am one of the lucky ones. I grew up in a time when things were starting to get better, when acceptance was slowly growing. But today, trans youth are facing a world that is often hostile and unforgiving.</p>
<p>And yet, they are still finding ways to survive and thrive. They are the strongest of us, and their resilience is truly inspiring.</p>
<p>I hope that we can all learn from their strength and courage. I hope that we can all work towards creating a world where trans teens can grow up safely and happily, without fear of persecution or violence. We owe it to them to try.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>