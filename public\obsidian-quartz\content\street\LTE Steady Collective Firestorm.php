<?php
// Auto-generated blog post
// Source: content\street\LTE Steady Collective Firestorm.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'January 11, 2018';
$meta_description = 'January 11, 2018 Dear Editor, Before I was homeless, I was afraid of people who used hard drugs. I had a fear of the unknown and implicit bias which c...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'January 11, 2018',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'January 11, 2018 Dear Editor, Before I was homeless, I was afraid of people who used hard drugs. I had a fear of the unknown and implicit bias which c...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\street\\LTE Steady Collective Firestorm.md',
);

// Post content
$post_content = '<p>January 11, 2018</p>
<p>Dear Editor,</p>
<p>Before I was homeless, I was afraid of people who used hard drugs. I had a fear of the unknown and implicit bias which clouded my judgment. I learned that people who use hard drugs, are people escaping insurmountable pain and trauma. Drug addictions do not happen in a vacuum. They occur in situations of hopelessness, desperation, and social disconnect. A local opiod epidemic occurs not as a moral failing of many individuals, but in municipalities where large amounts of people are in crisis. </p>

<p>There was a recent op-ed in the _ titled the town which responded to its opiod crisis like a natural disaster. The city doubled down on the resources invested in their populaces wellbeing, deployed law enforcement to distribute resources, offer transportation, and listen to the local perspectives with the intent of shaping better policy. </p>

<p>When I think of the opioid crisis in Asheville, I think of the gorgeous old growth tree in front of the Haywood Street Congregation that was cut down by the city. The tree was in perfect health, and a wonderful spot of congregation for many of the local homeless. I believe that tree was cut down because the homeless hanging out there were not a good selling point for the million dollar condos being erected across the street. Cutting down that tree made me want to pick up a needle.</p>

<p>I urge the City of Asheville to support the Steady Collective, and invest in resources for the towns most downtrodden. Don\'t sell your soul for wealth and development. </p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>