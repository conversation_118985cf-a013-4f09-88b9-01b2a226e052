<?php
// Auto-generated blog post
// Source: content\street\austin-ubi.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Austin experimented with giving people $1,000 a month. They said they spent the no-strings-attached cash mostly on housing.';
$meta_description = 'A guaranteed basic income plan in one of Texas\'s largest cities reduced rates of housing insecurity. But some Texas lawmakers are not happy.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Austin experimented with giving people $1,000 a month. They said they spent the no-strings-attached cash mostly on housing.',
  'author' => 'Kenneth Niemeyer - Business Insider',
  'date' => '2025-10-10',
  'excerpt' => 'A guaranteed basic income plan in one of Texas\'s largest cities reduced rates of housing insecurity. But some Texas lawmakers are not happy.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\street\\austin-ubi.md',
);

// Post content
$post_content = '<p>A guaranteed basic income plan in one of Texas\'s largest cities reduced rates of housing insecurity. But some Texas lawmakers are not happy.
<h3>Story by <a href="https://www.businessinsider.com/author/kenneth-niemeyer" target="_blank">Kenneth Niemeyer</a></h3></p>
<p>* A guaranteed basic income program in Austin gave people $1,000 a month for a year.
* Most of the participants spent the no-string-attached cash on housing, a study of the program found.
* Participants who said they could afford a balanced meal also increased by 17%.</p>
<p>A <a href="https://www.businessinsider.com/denver-basic-income-project-ubi-extended-homelessness-poverty-2024-1" target="_blank">guaranteed basic income plan</a> in one of Texas\'s largest cities reduced rates of housing insecurity. But some Texas lawmakers are not happy.</p>
<p>Austin was the first city in Texas to launch a <a href="https://www.businessinsider.com/universal-basic-income-texas-houston-harris-county-covid-relief-2023-12" target="_blank">tax-payer-funded basic income program</a> when the Austin Guaranteed Income Pilot kicked off in May 2022. The program served 135 low-income families, each receiving up to $1,000 monthly. Funding for 85 families came from the City of Austin while philanthropic donations funded the other 50.</p>
<p>Read more at <a href="https://www.businessinsider.com/austin-guarunteed-basic-income-gbi-ubi-housing-security-homeless-2024-1" target="_blank">this link</a>.</p>
<p><img src="https://schoolworkhelper.net/wp-content/uploads/2022/06/Universal-Basic-Income.webp" alt="money graphic stair ladder." width="400"></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>