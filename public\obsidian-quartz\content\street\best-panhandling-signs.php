<?php
// Auto-generated blog post
// Source: content\street\best-panhandling-signs.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '\'Just one more dollar and you will be happier\'';
$meta_description = 'One day i was eating at a free meal. Breaking bread with somebody. This person on their own accord draws out on sharpie on a napkin all of the most ef...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '\'Just one more dollar and you will be happier\'',
  'author' => 'A. A. Chips',
  'date' => '2025-10-10',
  'excerpt' => 'One day i was eating at a free meal. Breaking bread with somebody. This person on their own accord draws out on sharpie on a napkin all of the most ef...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\street\\best-panhandling-signs.md',
);

// Post content
$post_content = '<p>One day i was eating at a free meal. Breaking bread with somebody. This person on their own accord draws out on sharpie on a napkin all of the most effective panhandling signs they know. I still have this napkin as I write this, and think fondly of the situation. I\'d like to share some of these sign ideas. So if you are on hard times, try some of these data-driven solutions for some money to pay for pizza, beer, and a hotel room.</p>
<p>\'Just one more dollar and you will be happier\'
\'Bad day? Cuss me out ($1)\'
\'Dreaming of a cheeseburger\'
\'Punch a bum ($10)\'
\'I came to AVL for the FUN and all I got is this lousy toilet paper\' (referring to the cardboard, the message is too long for the sign and the end sloops)
\'Will work to get highered\'</p>
<p>Keep in mind that what people in that situation with money given is still none of your business. Unless you have lived it, you may not understand what it\'s like not having enough for a hotel room, but having enough for food and beer or drugs when you are in an unthinkable situation.</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title gradient-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>