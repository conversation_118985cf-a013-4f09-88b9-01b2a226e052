---
title: Testing Dynamic Image References
author: <PERSON><PERSON> <PERSON><PERSON>
excerpt: Testing the new dynamic image reference system that pulls data from gallery.json
date: 2025-01-08
categories:
  - Test
tags:
  - test
  - images
---

# Testing Dynamic Image References

This post tests the new dynamic image reference system. Instead of manually writing `<img>` tags with paths and alt text, I can now just reference images from my gallery using simple syntax.

## Self Images

Here's me angling in a hammock:
{{img:30degrees.png}}

And here's me proving I'm not Mr. Smee:
{{img:not-smee.png}}

## Humor Images

A classic meme about social media validation:
{{img:22-likes.png}}

## Art Images

Some thought-provoking art:
{{img:MiniCarMiniProfit.png}}

## Educational Content

Important information about trauma responses:
{{img:traumamanifesto.jpg}}

## Testing Different Reference Styles

### By basename only
{{img:30degrees}}

### By full path
{{img:self/30degrees.png}}

### Non-existent image (should show error)
{{img:this-does-not-exist.png}}

## Benefits of This System

1. **Single source of truth**: All image metadata lives in gallery.json
2. **Automatic alt text**: No more forgetting accessibility
3. **Rich captions**: Automatic captions from my curated descriptions
4. **Commentary**: Optional deeper context with toggle buttons
5. **Consistent styling**: All images follow the same design
6. **No broken paths**: No more `../../img/` path issues
7. **Easy maintenance**: Update alt text once, changes everywhere

This makes managing images across my blog so much easier!
