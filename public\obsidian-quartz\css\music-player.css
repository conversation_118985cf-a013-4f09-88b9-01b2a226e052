/* Integrated Music Player Styles */

/* Main Container */
.music-player-container {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
}

/* Playlist Header */
.playlist-header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.playlist-selector-wrapper {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.playlist-selector-wrapper label {
    color: var(--text-muted);
    font-size: 0.9rem;
    font-weight: 500;
    min-width: fit-content;
}

.playlist-dropdown {
    background-color: var(--background-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
}

.playlist-dropdown:hover {
    border-color: var(--primary-color);
    background-color: var(--card-background);
}

.playlist-dropdown:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

.playlist-info h1 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.playlist-info p {
    color: var(--text-muted);
    font-size: 1rem;
    margin: 0;
}

/* Player Main Section */
.player-main {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.now-playing-section {
    display: flex;
    gap: 1.5rem;
    align-items: center;
    padding: 1.5rem;
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-light);
}

.album-art-container {
    position: relative;
    flex-shrink: 0;
}

.album-art-container img {
    width: 120px;
    height: 120px;
    border-radius: var(--border-radius);
    object-fit: cover;
    transition: transform 0.3s ease;
}

.album-art-container.playing img {
    transform: scale(1.05);
}

.vinyl-record {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, #333 30%, #666 31%, #666 40%, #333 41%);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.album-art-container.playing .vinyl-record {
    opacity: 1;
    animation: spin 3s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.track-info {
    flex: 1;
    min-width: 0;
}

.track-info h2 {
    font-size: 1.4rem;
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.track-info h3 {
    font-size: 1.1rem;
    margin: 0 0 0.5rem 0;
    color: var(--primary-color);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.track-info p {
    margin: 0 0 0.5rem 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.track-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.85rem;
    color: var(--text-muted);
}

/* Player Controls */
.player-controls {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow-light);
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.progress-container span {
    font-size: 0.85rem;
    color: var(--text-muted);
    min-width: 40px;
    text-align: center;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background-color: var(--border-color);
    border-radius: 3px;
    cursor: pointer;
    overflow: hidden;
}

.progress {
    height: 100%;
    background-color: var(--primary-color);
    width: 0%;
    transition: width 0.1s ease;
}

.control-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.control-btn {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.control-btn:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
}

.play-pause {
    width: 55px;
    height: 55px;
    font-size: 1.2rem;
}

.volume-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.volume-slider {
    width: 100px;
    height: 4px;
    background-color: var(--border-color);
    border-radius: 2px;
    outline: none;
    cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    background-color: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
}

.volume-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background-color: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* Playlist Section */
.playlist-section {
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow-light);
}

.search-container {
    display: flex;
    margin-bottom: 1rem;
    gap: 0.5rem;
}

.search-container input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--card-background);
    color: var(--text-color);
    font-size: 0.9rem;
}

.search-container input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

.search-container button {
    padding: 0.75rem 1rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-container button:hover {
    background-color: var(--secondary-color);
}

.playlist-wrapper h3 {
    margin: 0 0 1rem 0;
    color: var(--text-color);
    font-size: 1.2rem;
}

.playlist-tracks {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 400px;
    overflow-y: auto;
}

.playlist-track {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 1rem;
}

.playlist-track:hover {
    background-color: var(--card-background);
}

.playlist-track.active {
    background-color: var(--primary-color);
    color: white;
}

.track-number {
    font-size: 0.85rem;
    color: var(--text-muted);
    min-width: 30px;
    text-align: center;
}

.playlist-track.active .track-number {
    color: white;
}

.track-details {
    flex: 1;
    min-width: 0;
}

.track-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.track-artist {
    font-size: 0.85rem;
    color: var(--text-muted);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.playlist-track.active .track-artist {
    color: rgba(255, 255, 255, 0.8);
}

.track-duration {
    font-size: 0.85rem;
    color: var(--text-muted);
    min-width: 50px;
    text-align: right;
}

.playlist-track.active .track-duration {
    color: white;
}

.no-results {
    text-align: center;
    padding: 2rem;
    color: var(--text-muted);
    font-style: italic;
}

/* Lyrics & Commentary Container */
.lyrics-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    backdrop-filter: blur(5px);
}

.lyrics-container.hidden {
    display: none;
}

.lyrics-content-wrapper {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 800px;
    max-height: 80vh;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.lyrics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--background-color);
}

.lyrics-header h2 {
    color: var(--text-color);
    font-size: 1.5rem;
    margin: 0;
}

#close-lyrics-button {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#close-lyrics-button:hover {
    background-color: var(--border-color);
    color: var(--text-color);
}

.lyrics-content,
.commentary-content {
    padding: 2rem;
    overflow-y: auto;
    flex: 1;
    line-height: 1.8;
    font-size: 1.1rem;
    color: var(--text-color);
}

.lyrics-content {
    background-color: var(--card-background);
    border-bottom: 1px solid var(--border-color);
}

.commentary-content {
    background-color: var(--background-color);
    font-style: italic;
    color: var(--text-muted);
}

.lyrics-content:empty,
.commentary-content:empty {
    display: none;
}

.lyrics-content p,
.commentary-content p {
    margin-bottom: 1rem;
}

.lyrics-content pre {
    white-space: pre-wrap;
    font-family: inherit;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .music-player-container {
        padding: 1rem;
    }

    .now-playing-section {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .album-art-container img {
        width: 100px;
        height: 100px;
    }

    .track-info h2 {
        font-size: 1.2rem;
    }

    .control-buttons {
        gap: 0.5rem;
    }

    .control-btn {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }

    .play-pause {
        width: 50px;
        height: 50px;
        font-size: 1.1rem;
    }

    .playlist-selector-wrapper {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .playlist-dropdown {
        width: 100%;
    }

    .lyrics-container {
        padding: 1rem;
    }

    .lyrics-content-wrapper {
        max-height: 90vh;
    }

    .lyrics-content,
    .commentary-content {
        padding: 1.5rem;
        font-size: 1rem;
    }
}
