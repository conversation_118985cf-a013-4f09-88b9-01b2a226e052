/**
 * Related Posts Styles
 * For A. A. Chips' Obsidian-Quartz Blog
 * 
 * Styles for the related posts section that appears after post content
 */

/* Related Posts Section */
.related-posts {
    margin: 3rem 0 2rem 0;
    padding: 2rem 0;
    border-top: 2px solid var(--border-color, #333);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%);
    border-radius: 8px;
    padding: 2rem;
}

.related-posts h2 {
    color: var(--accent-color, #00ff00);
    font-family: 'Virgil', cursive, sans-serif;
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    text-align: center;
    text-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
    position: relative;
}

.related-posts h2::after {
    content: '';
    display: block;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--accent-color, #00ff00), transparent);
    margin: 0.5rem auto;
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
}

/* Related Posts Grid */
.related-posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

/* Related Post Cards */
.related-post-card {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(20, 20, 20, 0.9) 100%);
    border: 1px solid var(--border-color, #333);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.related-post-card:hover {
    transform: translateY(-5px);
    border-color: var(--accent-color, #00ff00);
    box-shadow: 
        0 8px 16px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(0, 255, 0, 0.2);
}

.related-post-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--accent-color, #00ff00), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.related-post-card:hover::before {
    opacity: 1;
}

.related-post-link {
    display: block;
    text-decoration: none;
    color: inherit;
    height: 100%;
}

/* Related Post Thumbnails */
.related-post-thumbnail {
    width: 100%;
    height: 160px;
    overflow: hidden;
    position: relative;
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
}

.related-thumb-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    filter: brightness(0.8) contrast(1.1);
}

.related-post-card:hover .related-thumb-img {
    transform: scale(1.05);
    filter: brightness(1) contrast(1.2);
}

.related-thumb-img.placeholder {
    opacity: 0.6;
    filter: brightness(0.5) contrast(1.1) sepia(0.3) hue-rotate(90deg);
}

/* Related Post Content */
.related-post-content {
    padding: 1.2rem;
}

.related-post-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 0.8rem 0;
    color: var(--text-primary, #e0e0e0);
    line-height: 1.3;
    transition: color 0.3s ease;
}

.related-post-card:hover .related-post-title {
    color: var(--accent-color, #00ff00);
}

.related-post-excerpt {
    font-size: 0.9rem;
    color: var(--text-muted, #b0b0b0);
    line-height: 1.4;
    margin: 0 0 1rem 0;
}

.related-post-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-muted, #888);
}

.related-post-date {
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .related-posts {
        margin: 2rem 0 1rem 0;
        padding: 1.5rem 1rem;
    }
    
    .related-posts h2 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .related-posts-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .related-post-thumbnail {
        height: 140px;
    }
    
    .related-post-content {
        padding: 1rem;
    }
    
    .related-post-title {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .related-posts {
        padding: 1rem 0.5rem;
    }
    
    .related-post-thumbnail {
        height: 120px;
    }
    
    .related-post-content {
        padding: 0.8rem;
    }
    
    .related-post-title {
        font-size: 0.95rem;
        margin-bottom: 0.6rem;
    }
    
    .related-post-excerpt {
        font-size: 0.85rem;
        margin-bottom: 0.8rem;
    }
}

/* Dark theme enhancements */
@media (prefers-color-scheme: dark) {
    .related-posts {
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.1) 100%);
    }
    
    .related-post-card {
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(15, 15, 15, 0.95) 100%);
        border-color: #444;
    }
    
    .related-post-card:hover {
        border-color: var(--accent-color, #00ff00);
    }
}

/* Animation for loading */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.related-post-card {
    animation: fadeInUp 0.6s ease forwards;
}

.related-post-card:nth-child(2) {
    animation-delay: 0.1s;
}

.related-post-card:nth-child(3) {
    animation-delay: 0.2s;
}

.related-post-card:nth-child(4) {
    animation-delay: 0.3s;
}

.related-post-card:nth-child(5) {
    animation-delay: 0.4s;
}

.related-post-card:nth-child(6) {
    animation-delay: 0.5s;
}

/* Accessibility improvements */
.related-post-link:focus {
    outline: 2px solid var(--accent-color, #00ff00);
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    .related-post-card,
    .related-thumb-img,
    .related-post-title {
        transition: none;
        animation: none;
    }
    
    .related-post-card:hover {
        transform: none;
    }
    
    .related-post-card:hover .related-thumb-img {
        transform: none;
    }
}
