/* Store-specific CSS that integrates with the existing dark theme */

/* Store Container */
.store-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Store Navigation */
.store-nav {
    display: flex;
    gap: 15px;
    margin: 20px 0;
    flex-wrap: wrap;
    justify-content: center;
}

.store-nav button {
    background: var(--bg-secondary, #1a1a1a);
    border: 1px solid var(--accent-color, #00ff00);
    color: var(--accent-color, #00ff00);
    padding: 8px 16px;
    cursor: pointer;
    font-family: var(--font-mono, 'Courier New', monospace);
    font-size: 0.9em;
    transition: all 0.3s ease;
    border-radius: 3px;
    min-width: 80px;
}

.store-nav button:hover {
    background: var(--accent-color, #00ff00);
    color: var(--bg-primary, #000);
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
    transform: translateY(-1px);
}

.store-nav button.active {
    background: var(--accent-color, #00ff00);
    color: var(--bg-primary, #000);
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.2);
}

/* Items Grid */
.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

/* Item Cards */
.item-card {
    background: var(--bg-secondary, #1a1a1a);
    border: 1px solid var(--border-color, #333);
    padding: 20px;
    transition: all 0.3s ease;
    border-radius: 5px;
    position: relative;
    overflow: hidden;
}

.item-card:hover {
    border-color: var(--accent-color, #00ff00);
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.1);
    transform: translateY(-2px);
}

/* Item Status Styles */
.item-card.sold {
    opacity: 0.7;
    border-color: #666;
}

.item-card.sold .item-price {
    text-decoration: line-through;
    color: #888;
}

.item-card.sold::after {
    content: "☠️ SOLD";
    color: #ff4444;
    display: block;
    margin-top: 10px;
    font-size: 0.9em;
    font-weight: bold;
    text-align: center;
}

.item-card.wishlist {
    border-style: dashed;
    border-color: #ffaa00;
}

.item-card.wishlist::before {
    content: "🌟 WISHLIST";
    color: #ffaa00;
    display: block;
    font-size: 0.8em;
    margin-bottom: 10px;
    font-weight: bold;
    text-align: center;
}

.item-card.giveaway {
    border-color: #44ff44;
}

.item-card.giveaway::before {
    content: "🎁 FREE";
    color: #44ff44;
    display: block;
    font-size: 0.8em;
    margin-bottom: 10px;
    font-weight: bold;
    text-align: center;
}

/* Item Images */
.item-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: var(--bg-primary, #000);
    border: 1px solid var(--border-color, #333);
    border-radius: 3px;
    margin-bottom: 15px;
    transition: transform 0.3s ease;
}

.item-image:hover {
    transform: scale(1.02);
}

.item-image.placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted, #666);
    font-size: 1.1em;
    background: linear-gradient(45deg, #1a1a1a 25%, #222 25%, #222 50%, #1a1a1a 50%, #1a1a1a 75%, #222 75%);
    background-size: 20px 20px;
}

/* Item Content */
.item-title {
    margin: 0 0 10px 0;
    font-size: 1.1em;
    font-weight: bold;
}

.item-title a {
    color: var(--text-primary, #ccc);
    text-decoration: none;
    transition: all 0.3s ease;
}

.item-title a:hover {
    color: var(--accent-color, #00ff00);
    text-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
}

.item-description {
    margin: 10px 0;
    line-height: 1.5;
    color: var(--text-secondary, #aaa);
    font-size: 0.95em;
}

.item-price {
    color: var(--accent-color, #00ff00);
    font-weight: bold;
    font-size: 1.1em;
    margin: 10px 0;
    text-shadow: 0 0 3px rgba(0, 255, 0, 0.2);
}

.item-condition {
    font-size: 0.85em;
    color: var(--text-muted, #888);
    font-style: italic;
    margin: 5px 0;
}

/* Contact Button */
.contact-btn {
    display: inline-block;
    margin-top: 15px;
    background: var(--accent-color, #00ff00);
    color: var(--bg-primary, #000);
    padding: 8px 16px;
    text-decoration: none;
    font-family: var(--font-mono, 'Courier New', monospace);
    font-size: 0.9em;
    border-radius: 3px;
    transition: all 0.3s ease;
    font-weight: bold;
    text-align: center;
    min-width: 120px;
}

.contact-btn:hover {
    background: var(--text-primary, #fff);
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
    transform: translateY(-1px);
}

/* Item Detail Page Styles */
.item-detail-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.item-header {
    margin-bottom: 30px;
    text-align: center;
}

.item-status-badges {
    margin-bottom: 15px;
}

.status-badge {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8em;
    font-weight: bold;
    margin: 0 5px;
}

.status-badge.wishlist {
    background: rgba(255, 170, 0, 0.2);
    color: #ffaa00;
    border: 1px solid #ffaa00;
}

.status-badge.giveaway {
    background: rgba(68, 255, 68, 0.2);
    color: #44ff44;
    border: 1px solid #44ff44;
}

.status-badge.sold {
    background: rgba(255, 68, 68, 0.2);
    color: #ff4444;
    border: 1px solid #ff4444;
}

.item-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 30px;
}

.item-images {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.main-image {
    height: 300px;
    object-fit: cover;
}

.thumbnail {
    height: 80px;
    object-fit: cover;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.thumbnail:hover {
    opacity: 1;
}

.item-details {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.item-metadata {
    background: var(--bg-secondary, #1a1a1a);
    padding: 15px;
    border-radius: 5px;
    border: 1px solid var(--border-color, #333);
}

.metadata-item {
    margin-bottom: 8px;
    font-size: 0.9em;
}

.metadata-item:last-child {
    margin-bottom: 0;
}

.metadata-item strong {
    color: var(--text-primary, #ccc);
}

.acquisition-story {
    background: var(--bg-secondary, #1a1a1a);
    padding: 20px;
    border-radius: 5px;
    border-left: 3px solid var(--accent-color, #00ff00);
}

.acquisition-story h3 {
    margin: 0 0 10px 0;
    color: var(--accent-color, #00ff00);
    font-size: 1.1em;
}

.acquisition-story p {
    margin: 0;
    line-height: 1.6;
    color: var(--text-secondary, #aaa);
    font-style: italic;
}

.contact-section {
    text-align: center;
    padding: 20px;
    background: var(--bg-secondary, #1a1a1a);
    border-radius: 5px;
    border: 1px solid var(--accent-color, #00ff00);
}

.navigation-links {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid var(--border-color, #333);
}

.back-to-store {
    color: var(--accent-color, #00ff00);
    text-decoration: none;
    font-family: var(--font-mono, 'Courier New', monospace);
    transition: all 0.3s ease;
}

.back-to-store:hover {
    text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
    .items-grid {
        grid-template-columns: 1fr;
    }
    
    .store-nav {
        justify-content: center;
    }
    
    .store-nav button {
        flex: 1;
        min-width: 70px;
        font-size: 0.8em;
        padding: 6px 12px;
    }
    
    .item-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .main-image {
        height: 250px;
    }
    
    .item-detail-container {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .store-container {
        padding: 15px;
    }
    
    .item-card {
        padding: 15px;
    }
    
    .store-nav {
        gap: 8px;
    }
    
    .store-nav button {
        font-size: 0.75em;
        padding: 5px 8px;
    }
}
