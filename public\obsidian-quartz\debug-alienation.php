<?php
/**
 * Debug alienation directory scanning
 */

echo "<h1>Debug Alienation Directory Scanning</h1>";

$alienationDir = __DIR__ . '/content/alienation';
echo "<p>Scanning directory: " . htmlspecialchars($alienationDir) . "</p>";

if (!is_dir($alienationDir)) {
    echo "<p>❌ Directory does not exist!</p>";
    exit;
}

$posts = glob($alienationDir . '/*.php');
echo "<p>Found " . count($posts) . " PHP files total</p>";

echo "<h3>All PHP files found:</h3>";
echo "<ul>";
foreach ($posts as $postFile) {
    $filename = basename($postFile, '.php');
    echo "<li>" . htmlspecialchars($filename) . ".php";
    
    if ($filename === 'index') {
        echo " (excluded - index file)";
    } elseif ($filename === 'contents') {
        echo " (excluded - contents file)";
    } elseif ($filename === 'notes') {
        echo " (excluded - notes file)";
    } else {
        echo " ✅ (valid post)";
    }
    echo "</li>";
}
echo "</ul>";

// Test the filtering logic
$content_posts = [];
foreach ($posts as $postFile) {
    $filename = basename($postFile, '.php');
    if ($filename !== 'index' && $filename !== 'contents' && $filename !== 'notes') {
        $content_posts[] = [
            'title' => ucwords(str_replace('-', ' ', $filename)),
            'url' => $filename . '.php',
            'excerpt' => 'Alienation and recovery content.',
            'date' => date('Y-m-d'),
            'author' => 'A. A. Chips',
            'thumbnail' => null
        ];
    }
}

echo "<h3>Filtered posts:</h3>";
echo "<p>Found " . count($content_posts) . " valid posts</p>";

if (!empty($content_posts)) {
    echo "<ul>";
    foreach ($content_posts as $post) {
        echo "<li>" . htmlspecialchars($post['title']) . " (" . htmlspecialchars($post['url']) . ")</li>";
    }
    echo "</ul>";
    
    echo "<p>✅ Posts array is not empty - should display!</p>";
} else {
    echo "<p>❌ Posts array is empty - this is the problem!</p>";
}

// Test the condition
echo "<h3>Testing condition:</h3>";
echo "<p>!empty(\$content_posts): " . (!empty($content_posts) ? 'TRUE' : 'FALSE') . "</p>";
echo "<p>isset(\$content_posts): " . (isset($content_posts) ? 'TRUE' : 'FALSE') . "</p>";
echo "<p>is_array(\$content_posts): " . (is_array($content_posts) ? 'TRUE' : 'FALSE') . "</p>";
echo "<p>count(\$content_posts): " . count($content_posts) . "</p>";
?>
