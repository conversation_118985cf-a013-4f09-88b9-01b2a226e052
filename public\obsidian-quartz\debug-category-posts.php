<?php
/**
 * Debug script to check what's in the category posts data
 */

echo "<h1>Debug Category Posts Data</h1>";

// Load the humor index to see what data is available
echo "<h2>Humor Category Debug</h2>";

if (file_exists('content/humor/index.php')) {
    // Extract the all_posts_data from the humor index
    $content = file_get_contents('content/humor/index.php');
    
    // Find the all_posts_data array
    if (preg_match('/\$all_posts_data = array \((.*?)\);/s', $content, $matches)) {
        echo "<h3>Found all_posts_data array</h3>";
        
        // Look for humor-related URLs
        $humorUrls = [];
        if (preg_match_all("/'url' => '([^']*humor[^']*)',/", $content, $urlMatches)) {
            $humorUrls = $urlMatches[1];
            echo "<h4>URLs containing 'humor':</h4>";
            echo "<ul>";
            foreach ($humorUrls as $url) {
                echo "<li>" . htmlspecialchars($url) . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No URLs containing 'humor' found</p>";
        }
        
        // Look for any URLs to see the pattern
        if (preg_match_all("/'url' => '([^']*)',/", $content, $allUrlMatches)) {
            $allUrls = array_slice($allUrlMatches[1], 0, 10); // First 10 URLs
            echo "<h4>Sample URLs (first 10):</h4>";
            echo "<ul>";
            foreach ($allUrls as $url) {
                echo "<li>" . htmlspecialchars($url) . "</li>";
                // Check what our filter would match
                if (strpos($url, 'content\\humor\\') !== false) {
                    echo " ✅ Would match content\\humor\\";
                } elseif (strpos($url, 'content/humor/') !== false) {
                    echo " ✅ Would match content/humor/";
                } elseif (strpos($url, 'humor') !== false) {
                    echo " ⚠️ Contains humor but doesn't match filter";
                }
                echo "<br>";
            }
            echo "</ul>";
        }
        
        // Check categories
        if (preg_match_all("/'category' => '([^']*)',/", $content, $categoryMatches)) {
            $categories = array_unique($categoryMatches[1]);
            echo "<h4>Categories found:</h4>";
            echo "<ul>";
            foreach ($categories as $category) {
                echo "<li>" . htmlspecialchars($category) . "</li>";
            }
            echo "</ul>";
        }
        
    } else {
        echo "<p>Could not find all_posts_data array in humor index</p>";
    }
} else {
    echo "<p>Humor index file not found</p>";
}

// Test the filtering logic directly
echo "<h2>Test Filtering Logic</h2>";

$testUrls = [
    'content\\humor\\test.php',
    'content/humor/test.php', 
    'content\\\\humor\\\\test.php',
    'humor/test.php',
    'test.php'
];

echo "<h3>Testing different URL patterns:</h3>";
foreach ($testUrls as $testUrl) {
    echo "<p>URL: " . htmlspecialchars($testUrl) . "</p>";
    echo "<ul>";
    echo "<li>strpos(\$url, 'content\\\\humor\\\\') !== false: " . (strpos($testUrl, 'content\\humor\\') !== false ? 'TRUE' : 'FALSE') . "</li>";
    echo "<li>strpos(\$url, 'content/humor/') !== false: " . (strpos($testUrl, 'content/humor/') !== false ? 'TRUE' : 'FALSE') . "</li>";
    echo "<li>strpos(\$url, 'humor') !== false: " . (strpos($testUrl, 'humor') !== false ? 'TRUE' : 'FALSE') . "</li>";
    echo "</ul>";
}
?>
