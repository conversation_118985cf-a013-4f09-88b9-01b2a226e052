<?php
/**
 * Fix category URL issues across all category index pages
 */

echo "<h1>Fix Category URL Issues</h1>";

$categories = [
    'humor', 'climate', 'inspiration', 'journal', 'judaism', 'kitchen', 'ptsd-myth', 'street', 'writings'
];

foreach ($categories as $category) {
    echo "<h2>Fixing $category category URLs</h2>";
    
    $indexFile = "content/$category/index.php";
    if (!file_exists($indexFile)) {
        echo "<p>❌ $category index file not found</p>";
        continue;
    }
    
    $content = file_get_contents($indexFile);
    
    // Find and replace the URL generation line
    $oldPattern = '/href="\<\?php echo htmlspecialchars\(\$post_item\[\'url\'\] \?\? \'#\'\); \?\>"/';
    $newReplacement = 'href="<?php 
            // Fix URL path - convert from \'content\\' . $category . '\\file.php\' to \'../file.php\'
            $url = $post_item[\'url\'] ?? \'#\';
            if (strpos($url, \'content\\' . $category . '\\\\\') === 0) {
                $url = \'../\' . basename($url);
            }
            echo htmlspecialchars($url);
            ?>"';
    
    // Replace the pattern
    $newContent = preg_replace($oldPattern, $newReplacement, $content);
    
    if ($newContent !== $content) {
        file_put_contents($indexFile, $newContent);
        echo "<p>✅ Fixed URLs in $category index</p>";
    } else {
        echo "<p>⚠️ No URL pattern found to fix in $category index</p>";
    }
}

echo "<h2>Summary</h2>";
echo "<p>Fixed URL generation to convert paths like 'content\\category\\file.php' to '../file.php'</p>";
echo "<p>This prevents the double path issue that was causing 404 errors.</p>";
?>
