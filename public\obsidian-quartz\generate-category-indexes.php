<?php
/**
 * Category Index Generator
 * Scans category directories and generates index.php files with post listings
 */

require_once 'path-helper.php';
$config = include 'config.php';
$paths = initPaths($config, __FILE__);

class CategoryIndexGenerator {
    private $config;
    private $contentDir;
    
    public function __construct($config) {
        $this->config = $config;
        $this->contentDir = __DIR__ . '/content/';
    }
    
    /**
     * Generate index files for all categories
     */
    public function generateAllIndexes() {
        echo "Generating category index files...\n\n";
        
        // Get all category directories from config
        $categories = $this->config['categories'];
        
        foreach ($categories as $categoryName => $categoryPath) {
            // Extract category slug from path (e.g., 'content/humor/index.php' -> 'humor')
            preg_match('/content\/([^\/]+)\/index\.php/', $categoryPath, $matches);
            if (!isset($matches[1])) {
                continue;
            }
            
            $categorySlug = $matches[1];
            $categoryDir = $this->contentDir . $categorySlug . '/';
            
            if (!is_dir($categoryDir)) {
                echo "⚠️  Category directory not found: $categorySlug\n";
                continue;
            }
            
            // Check if there's already an index.md file - if so, skip this category
            // as it should be handled by build.php instead
            $indexMdPath = $categoryDir . 'index.md';
            if (file_exists($indexMdPath)) {
                echo "  ℹ️  Skipping $categorySlug - has index.md file (handled by build.php)\n";
                continue;
            }

            echo "Processing category: $categoryName ($categorySlug)\n";
            $this->generateCategoryIndex($categorySlug, $categoryName, $categoryDir);
        }
        
        echo "\n✅ Category index generation complete!\n";
    }
    
    /**
     * Generate index.php for a specific category
     */
    private function generateCategoryIndex($categorySlug, $categoryName, $categoryDir) {
        // Scan for all .md files in the category (except index.md)
        $posts = $this->scanCategoryPosts($categoryDir);
        
        if (empty($posts)) {
            echo "  ℹ️  No posts found in $categorySlug\n";
            return;
        }
        
        echo "  Found " . count($posts) . " posts\n";
        
        // Generate the PHP file content
        $phpContent = $this->generateIndexPhp($categorySlug, $categoryName, $posts);
        
        // Write to index.php
        $indexPath = $categoryDir . 'index.php';
        if (file_put_contents($indexPath, $phpContent) !== false) {
            echo "  ✅ Generated: $indexPath\n";
        } else {
            echo "  ❌ Failed to write: $indexPath\n";
        }
    }
    
    /**
     * Scan category directory for posts
     */
    private function scanCategoryPosts($categoryDir) {
        $posts = [];
        $files = glob($categoryDir . '*.md');
        
        foreach ($files as $file) {
            $filename = basename($file, '.md');
            
            // Skip index.md
            if ($filename === 'index') {
                continue;
            }
            
            // Parse the markdown file to get metadata
            $content = file_get_contents($file);
            $parsed = $this->parseMarkdown($content);
            
            // Extract title with fallback logic
            $title = $parsed['frontmatter']['title'] ?? null;
            if (!$title || empty($title)) {
                $title = $this->extractTitleFromContent($parsed['raw_content']);
            }

            $posts[] = [
                'title' => $title,
                'author' => $parsed['frontmatter']['author'] ?? null,
                'date' => $parsed['frontmatter']['date'] ?? null,
                'excerpt' => $parsed['frontmatter']['excerpt'] ?? $this->generateExcerpt($parsed['raw_content']),
                'url' => $filename . '.php',
                'tags' => $parsed['frontmatter']['tags'] ?? [],
                'filename' => $filename,
                'thumbnail' => $parsed['frontmatter']['thumbnail'] ?? null,
            ];
        }
        
        // Sort by date (newest first)
        usort($posts, function($a, $b) {
            $dateA = $a['date'] ? strtotime($a['date']) : 0;
            $dateB = $b['date'] ? strtotime($b['date']) : 0;
            return $dateB - $dateA;
        });
        
        return $posts;
    }
    
    /**
     * Parse markdown file
     */
    private function parseMarkdown($content) {
        $frontmatter = [];
        $markdownContent = $content;
        
        // Check for frontmatter
        if (preg_match('/^---\s*\n(.*?)\n---\s*\n(.*)$/s', $content, $matches)) {
            $frontmatterText = $matches[1];
            $markdownContent = $matches[2];
            
            // Parse YAML-style frontmatter
            $lines = explode("\n", $frontmatterText);
            $currentKey = null;
            
            foreach ($lines as $line) {
                if (preg_match('/^(\w+):\s*(.*)$/', $line, $match)) {
                    $key = $match[1];
                    $value = trim($match[2]);
                    
                    if ($value === '') {
                        $frontmatter[$key] = [];
                        $currentKey = $key;
                    } else {
                        $frontmatter[$key] = $value;
                        $currentKey = null;
                    }
                } elseif ($currentKey && preg_match('/^\s+-\s+(.+)$/', $line, $match)) {
                    $frontmatter[$currentKey][] = trim($match[1]);
                }
            }
        }
        
        return [
            'frontmatter' => $frontmatter,
            'content' => $markdownContent,
            'raw_content' => $markdownContent
        ];
    }
    
    /**
     * Extract title from markdown content when frontmatter is missing
     */
    private function extractTitleFromContent($content) {
        // Try to find the first heading
        if (preg_match('/^#+\s*(.+)$/m', $content, $matches)) {
            return trim($matches[1]);
        }

        // Try to find the first line that looks like a title (not empty, not tags)
        $lines = explode("\n", $content);
        foreach ($lines as $line) {
            $line = trim($line);
            // Skip empty lines, tag lines, and lines that start with special characters
            if (empty($line) ||
                strpos($line, '#') === 0 ||
                strpos($line, '!') === 0 ||
                strpos($line, '[') === 0 ||
                strpos($line, '---') === 0) {
                continue;
            }

            // If we find a reasonable line, use it as title
            if (strlen($line) > 3 && strlen($line) < 100) {
                return $line;
            }
        }

        return 'Untitled';
    }

    /**
     * Generate excerpt from content
     */
    private function generateExcerpt($content, $length = 150) {
        $text = strip_tags($content);
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);

        if (strlen($text) <= $length) {
            return $text;
        }

        return substr($text, 0, $length) . '...';
    }
    
    /**
     * Generate the PHP file content for category index
     */
    private function generateIndexPhp($categorySlug, $categoryName, $posts) {
        $php = "<?php\n";
        $php .= "// Auto-generated category index\n";
        $php .= "// Category: $categorySlug\n\n";
        
        // Path helper and config loading
        $php .= "// Load path helper and configuration with fallback\n";
        $php .= "\$pathPrefix = '../../';\n";
        $php .= "if (file_exists(__DIR__ . '/' . \$pathPrefix . 'path-helper.php')) {\n";
        $php .= "    require_once __DIR__ . '/' . \$pathPrefix . 'path-helper.php';\n";
        $php .= "    \$config = include __DIR__ . '/' . \$pathPrefix . 'config.php';\n";
        $php .= "} elseif (file_exists(__DIR__ . '/' . \$pathPrefix . '../path-helper.php')) {\n";
        $php .= "    require_once __DIR__ . '/' . \$pathPrefix . '../path-helper.php';\n";
        $php .= "    \$config = include __DIR__ . '/' . \$pathPrefix . '../config.php';\n";
        $php .= "} else {\n";
        $php .= "    die('Could not find path-helper.php');\n";
        $php .= "}\n";
        $php .= "\$paths = initPaths(\$config, __FILE__);\n\n";
        
        // Page variables
        $php .= "// Page variables\n";
        $php .= "\$page_title = " . var_export($categoryName, true) . ";\n";
        $php .= "\$meta_description = 'Browse all posts in the $categoryName category';\n";
        $php .= "\$meta_keywords = '" . strtolower($categorySlug) . ", posts, A. A. Chips, blog';\n";
        $php .= "\$css_path = \$paths['css_path'];\n";
        $php .= "\$js_path = \$paths['js_path'];\n";
        $php .= "\$base_url = \$paths['base_path'];\n";
        $php .= "\$related_posts = [];\n\n";
        
        // Category posts data
        $php .= "// Category posts data\n";
        $php .= "\$category_posts = " . var_export($posts, true) . ";\n\n";
        
        // Generate content
        $php .= $this->getCategoryTemplate();
        
        return $php;
    }
    
    /**
     * Get the category index template
     */
    private function getCategoryTemplate() {
        return <<<'PHP'
// Generate content
ob_start();
?>
<div class="category-index">
    <header class="category-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
        <p class="category-description">All posts in the <?php echo htmlspecialchars($page_title); ?> category</p>
    </header>

    <div class="post-grid">
        <?php foreach ($category_posts as $post): ?>
            <div class="post-card">
                <a href="<?php
                    // Convert full path to relative path for category index pages
                    $url = $post['url'];
                    if (strpos($url, 'content\\') === 0) {
                        // Extract just the filename from the full path
                        $url = basename($url);
                    }
                    echo htmlspecialchars($url);
                ?>" class="post-card-link">
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars(is_string($post['excerpt']) ? $post['excerpt'] : ''); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author'] && is_string($post['author'])): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo is_string($post['date']) ? htmlspecialchars($post['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>
PHP;
    }
}

// Run the generator
$generator = new CategoryIndexGenerator($config);
$generator->generateAllIndexes();

