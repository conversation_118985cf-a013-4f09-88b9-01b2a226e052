<?php
/**
 * Store Page Generator
 * This script generates individual item pages from the store-items.json file
 * Run this script whenever you add new items to the JSON file
 */

// Load store items
$store_items_file = 'data/store-items.json';
if (!file_exists($store_items_file)) {
    die("Store items file not found: $store_items_file\n");
}

$json_content = file_get_contents($store_items_file);
$store_data = json_decode($json_content, true);

if (!$store_data || !isset($store_data['items'])) {
    die("Invalid JSON data in store items file\n");
}

$items = $store_data['items'];
$template_file = 'store/item-template.php';

if (!file_exists($template_file)) {
    die("Template file not found: $template_file\n");
}

$generated_count = 0;
$updated_count = 0;

foreach ($items as $item) {
    $slug = $item['slug'];
    $item_file = "store/{$slug}.php";

    // Check if file already exists
    $file_existed = file_exists($item_file);

    // Copy template to create/update item page
    if (copy($template_file, $item_file)) {
        if ($file_existed) {
            echo "Updated item page: {$item_file}\n";
            $updated_count++;
        } else {
            echo "Generated new item page: {$item_file}\n";
            $generated_count++;
        }
    } else {
        echo "Failed to generate: {$item_file}\n";
    }
}

echo "\nSummary:\n";
echo "- Generated: {$generated_count} new pages\n";
echo "- Existing: {$updated_count} pages\n";
echo "- Total items: " . count($items) . "\n";

// Also generate a simple README for the store directory
$readme_content = "# Store Item Pages

This directory contains individual pages for each store item.

## Files:
- `item-template.php` - Template file for generating new item pages
";

foreach ($items as $item) {
    $readme_content .= "- `{$item['slug']}.php` - {$item['title']}\n";
}

$readme_content .= "
## Adding New Items:
1. Add the item to `../data/store-items.json`
2. Run `php ../generate-store-pages.php` to create the page
3. Add thumbnail images to `../img/store/`

## Image Naming:
Images should be named according to the filenames in the JSON `images` array.
For example, if an item has `\"images\": [\"axolotl-1.jpg\", \"axolotl-2.jpg\"]`,
place those files in `../img/store/axolotl-1.jpg` and `../img/store/axolotl-2.jpg`.
";

file_put_contents('store/README.md', $readme_content);
echo "Generated store/README.md\n";

echo "\nDone! 🎉\n";
?>
