<footer>
    <div class="container">
        <!-- Visitor Counter -->
        <?php
        // Include visitor counter display with multiple path attempts
        $visitorCounterPaths = [
            __DIR__ . '/../visitor-counter/visitor-display.php',  // From includes directory
            dirname(__DIR__) . '/visitor-counter/visitor-display.php',  // Alternative approach
            ($paths['base_path'] ?? '') . 'visitor-counter/visitor-display.php'  // Original fallback
        ];

        $visitorCounterLoaded = false;
        foreach ($visitorCounterPaths as $visitorCounterPath) {
            if (file_exists($visitorCounterPath)) {
                require_once $visitorCounterPath;
                $visitorCounterLoaded = true;
                break;
            }
        }

        if ($visitorCounterLoaded) {

            // Get page slug for tracking
            $currentPageSlug = null;
            if (isset($_SERVER['REQUEST_URI'])) {
                $uri = $_SERVER['REQUEST_URI'];
                $path = parse_url($uri, PHP_URL_PATH);
                $currentPageSlug = basename($path, '.php');
                $currentPageSlug = basename($currentPageSlug, '.html');

                // Handle index pages
                if ($currentPageSlug === 'index' || empty($currentPageSlug)) {
                    $dir = dirname($path);
                    $dirName = basename($dir);
                    if ($dirName && $dirName !== '.' && $dirName !== 'content') {
                        $currentPageSlug = $dirName . '-index';
                    } else {
                        $currentPageSlug = 'home';
                    }
                }

                // Clean the slug
                $currentPageSlug = preg_replace('/[^a-z0-9-]/', '-', strtolower($currentPageSlug));
                $currentPageSlug = preg_replace('/-+/', '-', $currentPageSlug);
                $currentPageSlug = trim($currentPageSlug, '-');
            }

            // Display visitor counter with retro style
            echo displayVisitorCounter($currentPageSlug, $page_title ?? null, [
                'style' => 'retro',
                'show_page_count' => true,
                'show_site_count' => true,
                'show_today_count' => false,
                'format' => 'full'
            ]);
        } else {
            // Fallback if visitor counter cannot be loaded
            echo '<div class="visitor-counter-error" style="color: #666; font-size: 0.8em;">Visitor counter temporarily unavailable</div>';
        }
        ?>

        <div class="bottom">
            <p>© 2025 A. A. Chips. All rights reserved.</p>
        </div>
    </div>
</footer>
