<?php
/**
 * Image Helper Functions
 * Provides server-side image resolution from gallery.json
 */

class ImageHelper {
    private static $galleryData = null;
    private static $imageIndex = null;
    private static $basePath = '';

    /**
     * Initialize the image helper with gallery data
     */
    public static function init($basePath = '') {
        self::$basePath = $basePath;
        self::loadGalleryData();
        self::buildImageIndex();
    }

    /**
     * Load gallery data from JSON file
     */
    private static function loadGalleryData() {
        $galleryFile = self::$basePath . 'data/gallery.json';
        
        if (!file_exists($galleryFile)) {
            error_log("Gallery file not found: $galleryFile");
            return;
        }

        $jsonContent = file_get_contents($galleryFile);
        if ($jsonContent === false) {
            error_log("Failed to read gallery file: $galleryFile");
            return;
        }

        self::$galleryData = json_decode($jsonContent, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Failed to parse gallery JSON: " . json_last_error_msg());
            self::$galleryData = null;
        }
    }

    /**
     * Build an index of all images for quick lookup
     */
    private static function buildImageIndex() {
        self::$imageIndex = [];
        
        if (!self::$galleryData) return;

        foreach (self::$galleryData as $category => $images) {
            foreach ($images as $imageData) {
                $filename = $imageData['filename'];
                
                // Index by full filename
                self::$imageIndex[$filename] = array_merge($imageData, ['category' => $category]);
                
                // Also index by basename for easier lookup
                $basename = basename($filename);
                if (!isset(self::$imageIndex[$basename])) {
                    self::$imageIndex[$basename] = array_merge($imageData, ['category' => $category]);
                }
            }
        }
    }

    /**
     * Find image data by filename
     */
    public static function findImageData($filename) {
        if (!self::$imageIndex) {
            self::init();
        }

        // Try exact match first
        if (isset(self::$imageIndex[$filename])) {
            return self::$imageIndex[$filename];
        }

        // Try with different extensions
        $baseName = pathinfo($filename, PATHINFO_FILENAME);
        $extensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp'];
        
        foreach ($extensions as $ext) {
            $testName = $baseName . $ext;
            if (isset(self::$imageIndex[$testName])) {
                return self::$imageIndex[$testName];
            }
        }

        // Try partial matches
        foreach (self::$imageIndex as $key => $value) {
            if (strpos($key, $filename) !== false || strpos($filename, basename($key)) !== false) {
                return $value;
            }
        }

        return null;
    }

    /**
     * Process text content and replace image references
     */
    public static function processImageReferences($content, $imagePath = 'img/') {
        if (!self::$galleryData) {
            self::init();
        }

        // Prevent infinite recursion by checking if we're already processing
        static $processing = false;
        if ($processing) {
            return $content;
        }

        $processing = true;
        $pattern = '/\{\{img:([^}]+)\}\}/';

        $result = preg_replace_callback($pattern, function($matches) use ($imagePath) {
            $filename = trim($matches[1]);
            return self::createImageHTML($filename, $imagePath);
        }, $content);

        $processing = false;
        return $result;
    }

    /**
     * Create HTML for an image reference
     */
    public static function createImageHTML($filename, $imagePath = 'img/', $options = []) {
        $imageData = self::findImageData($filename);
        
        if (!$imageData) {
            error_log("Image not found in gallery: $filename");
            return "<span class='missing-image'>[Image not found: $filename]</span>";
        }

        $src = rtrim($imagePath, '/') . '/' . ltrim($imageData['filename'], '/');
        $alt = htmlspecialchars($imageData['alt'] ?? '');
        $caption = htmlspecialchars($imageData['caption'] ?? '');
        $commentary = htmlspecialchars($imageData['commentary'] ?? '');
        
        $html = '<div class="dynamic-image-container">';
        
        // Image element
        $html .= '<img src="' . htmlspecialchars($src) . '"';
        $html .= ' alt="' . $alt . '"';
        $html .= ' loading="lazy"';
        
        if (isset($imageData['width']) && $imageData['width']) {
            $html .= ' width="' . intval($imageData['width']) . '"';
        }
        if (isset($imageData['height']) && $imageData['height']) {
            $html .= ' height="' . intval($imageData['height']) . '"';
        }
        
        $html .= '>';

        // Caption
        if ($caption && (!isset($options['hide_caption']) || !$options['hide_caption'])) {
            $html .= '<div class="image-caption">' . $caption . '</div>';
        }

        // Commentary (optional)
        if ($commentary && (!isset($options['hide_commentary']) || !$options['hide_commentary'])) {
            $commentaryId = 'commentary-' . md5($filename . $commentary);
            $html .= '<button class="toggle-commentary" onclick="toggleCommentary(\'' . $commentaryId . '\')">Show Commentary</button>';
            $html .= '<div id="' . $commentaryId . '" class="image-commentary" style="display: none;">';
            $html .= '<strong>Commentary:</strong> ' . $commentary;
            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Get all images in a specific category
     */
    public static function getImagesByCategory($category) {
        if (!self::$galleryData) {
            self::init();
        }

        return self::$galleryData[$category] ?? [];
    }

    /**
     * Get all categories
     */
    public static function getCategories() {
        if (!self::$galleryData) {
            self::init();
        }

        return array_keys(self::$galleryData ?? []);
    }

    /**
     * Search for images by alt text or caption
     */
    public static function searchImages($query) {
        if (!self::$galleryData) {
            self::init();
        }

        $results = [];
        $query = strtolower($query);

        foreach (self::$galleryData as $category => $images) {
            foreach ($images as $imageData) {
                $searchText = strtolower(
                    ($imageData['alt'] ?? '') . ' ' . 
                    ($imageData['caption'] ?? '') . ' ' . 
                    ($imageData['commentary'] ?? '')
                );
                
                if (strpos($searchText, $query) !== false) {
                    $results[] = array_merge($imageData, ['category' => $category]);
                }
            }
        }

        return $results;
    }

    /**
     * Get a random image from a category or all images
     */
    public static function getRandomImage($category = null) {
        if (!self::$galleryData) {
            self::init();
        }

        if ($category && isset(self::$galleryData[$category])) {
            $images = self::$galleryData[$category];
        } else {
            $images = [];
            foreach (self::$galleryData as $cat => $catImages) {
                foreach ($catImages as $img) {
                    $images[] = array_merge($img, ['category' => $cat]);
                }
            }
        }

        if (empty($images)) {
            return null;
        }

        return $images[array_rand($images)];
    }

    /**
     * Validate that an image file exists
     */
    public static function imageExists($filename, $imagePath = 'img/') {
        $imageData = self::findImageData($filename);
        if (!$imageData) {
            return false;
        }

        $fullPath = rtrim($imagePath, '/') . '/' . ltrim($imageData['filename'], '/');
        return file_exists(self::$basePath . $fullPath);
    }
}

// JavaScript function for commentary toggle (to be included in pages)
function getCommentaryToggleScript() {
    return '
<script>
function toggleCommentary(id) {
    const element = document.getElementById(id);
    const button = element.previousElementSibling;
    
    if (element.style.display === "none") {
        element.style.display = "block";
        button.textContent = "Hide Commentary";
    } else {
        element.style.display = "none";
        button.textContent = "Show Commentary";
    }
}
</script>';
}
?>
