<?php
/**
 * Related Posts Generator
 * For A. A. Chips' Obsidian-Quartz Blog
 * 
 * Generates a semi-randomized grid of related posts based on shared tags or categories
 */

/**
 * Generate related posts for a given post
 * 
 * @param array $currentPost Current post data with tags and category
 * @param array $allPosts Array of all available posts
 * @param int $maxPosts Maximum number of related posts to return
 * @return array Array of related posts
 */
function generateRelatedPosts($currentPost, $allPosts, $maxPosts = 6) {
    $relatedPosts = [];
    $currentUrl = $currentPost['url'] ?? '';
    $currentTags = $currentPost['tags'] ?? [];
    $currentCategory = $currentPost['category'] ?? '';
    
    // Ensure tags is an array
    if (!is_array($currentTags)) {
        $currentTags = $currentTags ? [$currentTags] : [];
    }
    
    // Score posts based on relevance
    $scoredPosts = [];
    
    foreach ($allPosts as $post) {
        // Skip the current post
        if (isset($post['url']) && $post['url'] === $currentUrl) {
            continue;
        }
        
        $score = 0;
        $postTags = $post['tags'] ?? [];
        $postCategory = $post['category'] ?? '';
        
        // Ensure post tags is an array
        if (!is_array($postTags)) {
            $postTags = $postTags ? [$postTags] : [];
        }
        
        // Score based on shared tags (higher weight)
        $sharedTags = array_intersect($currentTags, $postTags);
        $score += count($sharedTags) * 3;
        
        // Score based on same category (medium weight)
        if ($currentCategory && $postCategory === $currentCategory) {
            $score += 2;
        }
        
        // Score based on URL path similarity (lower weight)
        if (isset($post['url']) && $currentUrl) {
            $currentPath = dirname($currentUrl);
            $postPath = dirname($post['url']);
            if ($currentPath === $postPath) {
                $score += 1;
            }
        }
        
        // Add some randomness to prevent always showing the same posts
        $score += mt_rand(0, 1);
        
        if ($score > 0) {
            $scoredPosts[] = [
                'post' => $post,
                'score' => $score
            ];
        }
    }
    
    // Sort by score (highest first)
    usort($scoredPosts, function($a, $b) {
        return $b['score'] <=> $a['score'];
    });
    
    // Take the top posts
    $relatedPosts = array_slice($scoredPosts, 0, $maxPosts);
    
    // If we don't have enough related posts, fill with random posts
    if (count($relatedPosts) < $maxPosts) {
        $remainingSlots = $maxPosts - count($relatedPosts);
        $usedUrls = array_column(array_column($relatedPosts, 'post'), 'url');
        $usedUrls[] = $currentUrl; // Don't include current post
        
        $randomPosts = [];
        foreach ($allPosts as $post) {
            if (!in_array($post['url'] ?? '', $usedUrls)) {
                $randomPosts[] = ['post' => $post, 'score' => 0];
            }
        }
        
        // Shuffle and take what we need
        shuffle($randomPosts);
        $relatedPosts = array_merge($relatedPosts, array_slice($randomPosts, 0, $remainingSlots));
    }
    
    // Extract just the post data
    return array_column($relatedPosts, 'post');
}

/**
 * Render related posts HTML
 * 
 * @param array $relatedPosts Array of related posts
 * @param array $paths Path configuration
 * @return string HTML for related posts section
 */
function renderRelatedPosts($relatedPosts, $paths) {
    if (empty($relatedPosts)) {
        return '';
    }
    
    ob_start();
    ?>
    <section class="related-posts">
        <h2>Related Posts</h2>
        <div class="related-posts-grid">
            <?php foreach ($relatedPosts as $post): ?>
                <div class="related-post-card">
                    <a href="<?php echo htmlspecialchars($post['url'] ?? '#'); ?>" class="related-post-link">
                        <div class="related-post-thumbnail">
                            <?php if (isset($post['thumbnail']) && $post['thumbnail']): ?>
                                <?php if (preg_match('/^https?:\/\//', $post['thumbnail'])): ?>
                                    <img src="<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                         alt="<?php echo htmlspecialchars($post['title'] ?? ''); ?>" class="related-thumb-img">
                                <?php else: ?>
                                    <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                         alt="<?php echo htmlspecialchars($post['title'] ?? ''); ?>" class="related-thumb-img">
                                <?php endif; ?>
                            <?php else: ?>
                                <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                                <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                                <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>" 
                                     alt="<?php echo htmlspecialchars($post['title'] ?? ''); ?>" class="related-thumb-img placeholder">
                            <?php endif; ?>
                        </div>
                        <div class="related-post-content">
                            <h3 class="related-post-title"><?php echo htmlspecialchars($post['title'] ?? 'Untitled'); ?></h3>
                            <p class="related-post-excerpt"><?php echo htmlspecialchars(substr($post['excerpt'] ?? '', 0, 100)); ?><?php echo strlen($post['excerpt'] ?? '') > 100 ? '...' : ''; ?></p>
                            <div class="related-post-meta">
                                <?php if (isset($post['date']) && $post['date']): ?>
                                    <span class="related-post-date"><?php echo htmlspecialchars($post['date']); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endforeach; ?>
        </div>
    </section>
    <?php
    return ob_get_clean();
}

/**
 * Get all posts data for related posts generation
 * This is a simplified version that scans the content directory
 * 
 * @param string $contentDir Path to content directory
 * @return array Array of all posts
 */
function getAllPostsData($contentDir) {
    $allPosts = [];
    
    // Scan content directory for PHP files
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($contentDir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->getExtension() === 'php' && $file->getFilename() !== 'index.php') {
            $relativePath = str_replace($contentDir, '', $file->getPathname());
            $relativePath = ltrim(str_replace('\\', '/', $relativePath), '/');
            
            // Extract basic info from filename and path
            $filename = $file->getBasename('.php');
            $category = dirname($relativePath);
            if ($category === '.') $category = '';
            
            $allPosts[] = [
                'title' => ucwords(str_replace('-', ' ', $filename)),
                'url' => $relativePath,
                'category' => $category,
                'tags' => [], // Would need to parse file to get actual tags
                'excerpt' => 'Content from ' . $category . ' category.',
                'date' => date('Y-m-d', $file->getMTime()),
                'author' => 'A. A. Chips'
            ];
        }
    }
    
    return $allPosts;
}

/**
 * Simple function to include related posts in a post template
 * 
 * @param array $currentPost Current post data
 * @param array $paths Path configuration
 * @param int $maxPosts Maximum number of related posts
 * @return string HTML for related posts
 */
function includeRelatedPosts($currentPost, $paths, $maxPosts = 6) {
    $contentDir = dirname($paths['template_path']) . '/content/';
    $allPosts = getAllPostsData($contentDir);
    $relatedPosts = generateRelatedPosts($currentPost, $allPosts, $maxPosts);
    return renderRelatedPosts($relatedPosts, $paths);
}
?>
