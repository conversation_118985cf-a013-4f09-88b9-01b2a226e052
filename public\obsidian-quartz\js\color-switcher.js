/**
 * Color Switcher Component
 * Handles theme switching between light, dark, and gradient themes
 */

class ColorSwitcher {
    constructor() {
        this.themes = ['light', 'dark', 'gradient'];
        this.currentTheme = this.getStoredTheme() || 'dark';
        this.init();
    }

    init() {
        this.createSwitcher();
        this.applyTheme(this.currentTheme);
        this.bindEvents();
    }

    createSwitcher() {
        // Find the navigation element
        const nav = document.querySelector('nav ul');
        if (!nav) return;

        // Create color switcher container
        const switcherContainer = document.createElement('li');
        switcherContainer.className = 'color-switcher';

        // Create the switcher toggle
        const switcherToggle = document.createElement('div');
        switcherToggle.className = 'color-switcher-toggle';
        switcherToggle.setAttribute('role', 'button');
        switcherToggle.setAttribute('aria-label', 'Switch color theme');

        // Create theme options
        this.themes.forEach(theme => {
            const option = document.createElement('div');
            option.className = `theme-option theme-${theme}`;
            option.setAttribute('data-theme', theme);
            option.setAttribute('role', 'button');
            option.setAttribute('aria-label', `Switch to ${theme} theme`);
            
            const icon = document.createElement('span');
            icon.className = 'theme-icon';
            option.appendChild(icon);

            if (theme === this.currentTheme) {
                option.classList.add('active');
            }

            switcherToggle.appendChild(option);
        });

        switcherContainer.appendChild(switcherToggle);
        nav.appendChild(switcherContainer);
    }

    bindEvents() {
        // Add click events to theme options
        document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const theme = option.getAttribute('data-theme');
                this.switchTheme(theme);
            });
        });

        // Add keyboard support
        document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    const theme = option.getAttribute('data-theme');
                    this.switchTheme(theme);
                }
            });
        });

        // Listen for system theme changes
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', () => {
                if (!this.getStoredTheme()) {
                    this.applySystemTheme();
                }
            });
        }
    }

    switchTheme(theme) {
        if (this.themes.includes(theme) && theme !== this.currentTheme) {
            this.currentTheme = theme;
            this.applyTheme(theme);
            this.storeTheme(theme);
            this.updateActiveOption(theme);
            
            // Dispatch custom event for other components
            window.dispatchEvent(new CustomEvent('themeChanged', {
                detail: { theme: theme }
            }));
        }
    }

    applyTheme(theme) {
        // Remove existing theme classes
        document.documentElement.removeAttribute('data-theme');

        // Apply new theme - now all themes use data-theme attribute
        document.documentElement.setAttribute('data-theme', theme);

        // Add smooth transition class temporarily
        document.body.classList.add('theme-transitioning');
        setTimeout(() => {
            document.body.classList.remove('theme-transitioning');
        }, 300);

        // Update meta theme-color for mobile browsers
        this.updateMetaThemeColor(theme);
    }

    updateMetaThemeColor(theme) {
        let themeColor;
        switch (theme) {
            case 'light':
                themeColor = '#6b5b95';
                break;
            case 'dark':
                themeColor = '#444';
                break;
            case 'gradient':
                themeColor = '#8b5cf6';
                break;
            default:
                themeColor = '#6b5b95';
        }

        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        metaThemeColor.content = themeColor;
    }

    updateActiveOption(theme) {
        document.querySelectorAll('.theme-option').forEach(option => {
            option.classList.remove('active');
            if (option.getAttribute('data-theme') === theme) {
                option.classList.add('active');
            }
        });
    }

    storeTheme(theme) {
        try {
            localStorage.setItem('obsidian-quartz-theme', theme);
        } catch (e) {
            console.warn('Could not save theme preference:', e);
        }
    }

    getStoredTheme() {
        try {
            return localStorage.getItem('obsidian-quartz-theme');
        } catch (e) {
            console.warn('Could not retrieve theme preference:', e);
            return null;
        }
    }

    applySystemTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            this.switchTheme('dark');
        } else {
            this.switchTheme('light');
        }
    }

    // Public method to get current theme
    getCurrentTheme() {
        return this.currentTheme;
    }

    // Public method to cycle through themes
    cycleTheme() {
        const currentIndex = this.themes.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % this.themes.length;
        this.switchTheme(this.themes[nextIndex]);
    }
}

// Add smooth transitions for theme switching
const themeTransitionCSS = `
.theme-transitioning,
.theme-transitioning *,
.theme-transitioning *:before,
.theme-transitioning *:after {
    transition: background-color 0.3s ease, 
                color 0.3s ease, 
                border-color 0.3s ease, 
                box-shadow 0.3s ease !important;
}
`;

// Inject transition CSS
const style = document.createElement('style');
style.textContent = themeTransitionCSS;
document.head.appendChild(style);

// Initialize color switcher when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.colorSwitcher = new ColorSwitcher();
    });
} else {
    window.colorSwitcher = new ColorSwitcher();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ColorSwitcher;
}
