/**
 * Donation Modal System
 * Shows silly donation prompts every 3-5 page visits
 * Self-contained and easily removable by commenting out the script tag
 */

class DonationModal {
    constructor() {
        this.storageKey = 'aachips_page_visits';
        this.modalShownKey = 'aachips_modal_shown';
        this.visitThreshold = this.getRandomThreshold(); // 3-5 pages
        this.modalId = 'donation-modal';
        
        // Silly donation messages
        this.messages = [
            {
                title: "🍎 Apple Emergency! 🍎",
                message: "My apple supply is running dangerously low! Without your support, I might have to resort to eating... *shudder*... oranges. Please, think of the apples!",
                buttonText: "Save the Apples!"
            },
            {
                title: "🎭 Plot Twist! 🎭",
                message: "Surprise! This isn't actually a pop-up ad for crypto or extended car warranties. It's just me, asking if you'd like to toss a coin to your witcher... I mean, blogger.",
                buttonText: "Toss a Coin!"
            },
            {
                title: "🔮 Fortune Cookie Says... 🔮",
                message: "\"You will find great fortune... by clicking the donate button below.\" Okay, I may have written that fortune myself, but it's still technically a prediction!",
                buttonText: "Fulfill the Prophecy!"
            },
            {
                title: "🚨 Breaking News! 🚨",
                message: "Local blogger discovered to be powered entirely by Ko-Fi donations and good vibes. Scientists are baffled. You can help solve this mystery!",
                buttonText: "Fund the Research!"
            },
            {
                title: "🎪 Step Right Up! 🎪",
                message: "See the amazing disappearing money trick! You put money in Ko-Fi, and POOF! It vanishes into my grocery budget. It's magic! (The good kind of magic.)",
                buttonText: "Witness the Magic!"
            },
            {
                title: "🦸‍♀️ Hero Needed! 🦸‍♂️",
                message: "The city of Content Creation is under attack by the evil Dr. Empty-Wallet! Only you can save the day with the power of... small monthly donations!",
                buttonText: "Be the Hero!"
            },
            {
                title: "🎵 Music to Your Wallet! 🎵",
                message: "Listen to the soothing sounds of... wait, what? You're not into music? Well, then, how about the sound of your money making a difference in someone's life?",
                buttonText: "Make the Sound!"
            },
            {
                title: "Vote with your dollar! Don't buy toilet paper.",
                message: "Voting with your dollar often means for people that you can choose between which brand of toilet paper to buy. Or you can choose not to buy toilet paper at all and give that money to me instead.",
                buttonText: "Go Naturale!"
            }
        ];
        
        this.init();
    }
    
    init() {
        this.trackPageVisit();
        this.createModalHTML();
        this.setupEventListeners();
    }
    
    getRandomThreshold() {
        return Math.floor(Math.random() * 3) + 3; // Random number between 3-5
    }
    
    trackPageVisit() {
        const visits = this.getPageVisits();
        const newVisits = visits + 1;
        localStorage.setItem(this.storageKey, newVisits.toString());
        
        // Check if we should show the modal
        if (newVisits >= this.visitThreshold && !this.hasModalBeenShownRecently()) {
            setTimeout(() => this.showModal(), 2000); // Show after 2 seconds
        }
    }
    
    getPageVisits() {
        const visits = localStorage.getItem(this.storageKey);
        return visits ? parseInt(visits, 10) : 0;
    }
    
    hasModalBeenShownRecently() {
        const lastShown = localStorage.getItem(this.modalShownKey);
        if (!lastShown) return false;
        
        const lastShownTime = parseInt(lastShown, 10);
        const now = Date.now();
        const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds
        
        return (now - lastShownTime) < oneHour;
    }
    
    createModalHTML() {
        const modalHTML = `
            <div id="${this.modalId}" class="donation-modal" style="display: none;">
                <div class="donation-modal-overlay"></div>
                <div class="donation-modal-content">
                    <button class="donation-modal-close" aria-label="Close modal">&times;</button>
                    <div class="donation-modal-body">
                        <h3 class="donation-modal-title"></h3>
                        <p class="donation-modal-message"></p>
                        <div class="donation-modal-buttons">
                            <button class="donation-modal-kofi-btn" id="modal-kofi-btn">
                                <span class="kofi-icon">☕</span>
                                <span class="kofi-text">Support on Ko-Fi</span>
                            </button>
                            <button class="donation-modal-widget-btn" id="modal-widget-btn">
                                Check Widget Below ↙️
                            </button>
                            <button class="donation-modal-later-btn" id="modal-later-btn">
                                Maybe Later
                            </button>
                        </div>
                        <p class="donation-modal-footer">
                            <small>This message appears every few pages. You can always find the Ko-Fi widget in the bottom left corner!</small>
                        </p>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }
    
    setupEventListeners() {
        const modal = document.getElementById(this.modalId);
        const closeBtn = modal.querySelector('.donation-modal-close');
        const overlay = modal.querySelector('.donation-modal-overlay');
        const kofiBtn = modal.querySelector('#modal-kofi-btn');
        const widgetBtn = modal.querySelector('#modal-widget-btn');
        const laterBtn = modal.querySelector('#modal-later-btn');
        
        // Close modal events
        closeBtn.addEventListener('click', () => this.hideModal());
        overlay.addEventListener('click', () => this.hideModal());
        laterBtn.addEventListener('click', () => this.hideModal());
        
        // Ko-Fi button
        kofiBtn.addEventListener('click', () => {
            window.open('https://www.ko-fi.com/aachips', '_blank');
            this.hideModal();
        });
        
        // Widget button - scroll to Ko-Fi widget
        widgetBtn.addEventListener('click', () => {
            this.hideModal();
            // Try to trigger the Ko-Fi widget if it exists
            if (window.kofiWidgetOverlay) {
                // Small delay to let modal close
                setTimeout(() => {
                    // Look for Ko-Fi widget elements and try to trigger them
                    const kofiWidget = document.querySelector('[data-kofi-button]') || 
                                     document.querySelector('.floatingchat-container') ||
                                     document.querySelector('[id*="kofi"]');
                    if (kofiWidget) {
                        kofiWidget.click();
                    }
                }, 300);
            }
        });
        
        // Keyboard events
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.style.display !== 'none') {
                this.hideModal();
            }
        });
    }
    
    showModal() {
        const modal = document.getElementById(this.modalId);
        const randomMessage = this.messages[Math.floor(Math.random() * this.messages.length)];
        
        // Update modal content
        modal.querySelector('.donation-modal-title').textContent = randomMessage.title;
        modal.querySelector('.donation-modal-message').textContent = randomMessage.message;
        modal.querySelector('#modal-kofi-btn .kofi-text').textContent = randomMessage.buttonText;
        
        // Show modal
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
        
        // Add show class for animation
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        
        // Mark as shown and reset visit counter
        localStorage.setItem(this.modalShownKey, Date.now().toString());
        localStorage.setItem(this.storageKey, '0');
        this.visitThreshold = this.getRandomThreshold(); // Set new random threshold
        
        // Auto-close after 30 seconds if user doesn't interact
        setTimeout(() => {
            if (modal.style.display !== 'none') {
                this.hideModal();
            }
        }, 30000);
    }
    
    hideModal() {
        const modal = document.getElementById(this.modalId);
        modal.classList.remove('show');
        
        setTimeout(() => {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        }, 300);
    }
}

// Initialize the donation modal system when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new DonationModal();
});

// Also initialize if the script loads after DOMContentLoaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new DonationModal();
    });
} else {
    new DonationModal();
}
