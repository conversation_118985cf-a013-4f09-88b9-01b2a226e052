/**
 * Dynamic Image Resolver
 * Resolves image references from gallery.json and replaces placeholders in content
 */

class ImageResolver {
    constructor() {
        this.galleryData = null;
        this.imageIndex = null;
        this.basePath = '';
        this.init();
    }

    async init() {
        try {
            await this.loadGalleryData();
            this.buildImageIndex();
            this.resolveImageReferences();
        } catch (error) {
            console.error('Failed to initialize ImageResolver:', error);
        }
    }

    async loadGalleryData() {
        try {
            const response = await fetch('data/gallery.json');
            if (!response.ok) {
                throw new Error(`Failed to load gallery data: ${response.status}`);
            }
            this.galleryData = await response.json();
            
            // Determine base path for images
            this.basePath = this.getBasePath();
        } catch (error) {
            console.error('Error loading gallery data:', error);
            throw error;
        }
    }

    getBasePath() {
        // Try to determine the correct base path for images
        const scripts = document.getElementsByTagName('script');
        for (let script of scripts) {
            if (script.src && script.src.includes('image-resolver.js')) {
                const scriptPath = script.src;
                const baseUrl = scriptPath.substring(0, scriptPath.lastIndexOf('/js/'));
                return baseUrl + '/img/';
            }
        }
        // Fallback
        return 'img/';
    }

    buildImageIndex() {
        this.imageIndex = new Map();
        
        if (!this.galleryData) return;

        // Index all images by filename for quick lookup
        Object.entries(this.galleryData).forEach(([category, images]) => {
            images.forEach(imageData => {
                const filename = imageData.filename;
                // Index by full filename and also by just the basename
                this.imageIndex.set(filename, { ...imageData, category });
                
                const basename = filename.split('/').pop();
                if (!this.imageIndex.has(basename)) {
                    this.imageIndex.set(basename, { ...imageData, category });
                }
            });
        });
    }

    resolveImageReferences() {
        // Find all text nodes and elements that might contain image references
        this.processTextNodes(document.body);
        
        // Also process any elements that might have been added dynamically
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        this.processTextNodes(node);
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    processTextNodes(element) {
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            if (this.containsImageReference(node.textContent)) {
                textNodes.push(node);
            }
        }

        // Process text nodes in reverse order to avoid issues with DOM changes
        textNodes.reverse().forEach(textNode => {
            this.replaceImageReferences(textNode);
        });
    }

    containsImageReference(text) {
        return /\{\{img:[^}]+\}\}/g.test(text);
    }

    replaceImageReferences(textNode) {
        const text = textNode.textContent;
        const regex = /\{\{img:([^}]+)\}\}/g;
        
        let match;
        let lastIndex = 0;
        const fragments = [];

        while ((match = regex.exec(text)) !== null) {
            // Add text before the match
            if (match.index > lastIndex) {
                fragments.push(document.createTextNode(text.slice(lastIndex, match.index)));
            }

            // Create image element
            const filename = match[1].trim();
            const imageElement = this.createImageElement(filename);
            fragments.push(imageElement);

            lastIndex = regex.lastIndex;
        }

        // Add remaining text
        if (lastIndex < text.length) {
            fragments.push(document.createTextNode(text.slice(lastIndex)));
        }

        // Replace the text node with fragments
        if (fragments.length > 0) {
            const parent = textNode.parentNode;
            fragments.forEach(fragment => {
                parent.insertBefore(fragment, textNode);
            });
            parent.removeChild(textNode);
        }
    }

    createImageElement(filename) {
        const imageData = this.findImageData(filename);
        
        if (!imageData) {
            console.warn(`Image not found in gallery: ${filename}`);
            return document.createTextNode(`[Image not found: ${filename}]`);
        }

        const container = document.createElement('div');
        container.className = 'dynamic-image-container';

        const img = document.createElement('img');
        img.src = this.basePath + imageData.filename;
        img.alt = imageData.alt || '';
        img.loading = 'lazy';
        
        if (imageData.width) img.width = imageData.width;
        if (imageData.height) img.height = imageData.height;

        container.appendChild(img);

        // Add caption if available
        if (imageData.caption && imageData.caption.trim()) {
            const caption = document.createElement('div');
            caption.className = 'image-caption';
            caption.textContent = imageData.caption;
            container.appendChild(caption);
        }

        // Add commentary if available (optional, can be toggled)
        if (imageData.commentary && imageData.commentary.trim()) {
            const commentary = document.createElement('div');
            commentary.className = 'image-commentary';
            commentary.style.display = 'none'; // Hidden by default
            commentary.innerHTML = `<strong>Commentary:</strong> ${imageData.commentary}`;
            
            const toggleButton = document.createElement('button');
            toggleButton.className = 'toggle-commentary';
            toggleButton.textContent = 'Show Commentary';
            toggleButton.onclick = () => {
                const isHidden = commentary.style.display === 'none';
                commentary.style.display = isHidden ? 'block' : 'none';
                toggleButton.textContent = isHidden ? 'Hide Commentary' : 'Show Commentary';
            };
            
            container.appendChild(toggleButton);
            container.appendChild(commentary);
        }

        return container;
    }

    findImageData(filename) {
        // Try exact match first
        if (this.imageIndex.has(filename)) {
            return this.imageIndex.get(filename);
        }

        // Try with different extensions
        const baseName = filename.replace(/\.[^/.]+$/, "");
        const extensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp'];
        
        for (const ext of extensions) {
            const testName = baseName + ext;
            if (this.imageIndex.has(testName)) {
                return this.imageIndex.get(testName);
            }
        }

        // Try partial matches (in case of path differences)
        for (const [key, value] of this.imageIndex.entries()) {
            if (key.includes(filename) || filename.includes(key.split('/').pop())) {
                return value;
            }
        }

        return null;
    }

    // Public method to manually resolve a specific image reference
    resolveImage(filename) {
        return this.findImageData(filename);
    }

    // Public method to get all images in a category
    getImagesByCategory(category) {
        return this.galleryData ? this.galleryData[category] || [] : [];
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.imageResolver = new ImageResolver();
    });
} else {
    window.imageResolver = new ImageResolver();
}
