<?php
/**
 * Image Migration Script
 * Helps convert old <img> tags to new {{img:filename}} syntax
 */

require_once 'includes/image-helper.php';

echo "Image Migration Script\n";
echo "======================\n\n";

// Initialize image helper
ImageHelper::init(__DIR__ . '/');

// Find all markdown files
function findMarkdownFiles($dir) {
    $files = [];
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );

    foreach ($iterator as $file) {
        if ($file->getExtension() === 'md') {
            $files[] = $file->getPathname();
        }
    }
    return $files;
}

// Extract filename from img src
function extractFilename($src) {
    // Remove path prefixes like ../../img/ or img/
    $src = preg_replace('#^(\.\./)*img/#', '', $src);php 
    return $src;
}

// Process a single file
function processFile($filePath, $dryRun = false) {
    $content = file_get_contents($filePath);
    $originalContent = $content;
    $changes = 0;
    
    // Pattern to match <img> tags
    $pattern = '/<img\s+[^>]*src=["\']([^"\']+)["\'][^>]*>/i';
    
    $content = preg_replace_callback($pattern, function($matches) use (&$changes) {
        $fullTag = $matches[0];
        $src = $matches[1];
        
        // Extract just the filename
        $filename = extractFilename($src);
        
        // Check if this image exists in our gallery
        $imageData = ImageHelper::findImageData($filename);
        
        if ($imageData) {
            $changes++;
            echo "    ✅ Converting: $src → {{img:$filename}}\n";
            return "{{img:$filename}}";
        } else {
            echo "    ⚠️  Skipping: $src (not found in gallery)\n";
            return $fullTag; // Keep original
        }
    }, $content);
    
    if ($changes > 0 && !$dryRun) {
        file_put_contents($filePath, $content);
        echo "    💾 Saved $changes changes to file\n";
    }
    
    return $changes;
}

// Main execution
$contentDir = __DIR__ . '/content/';
$files = findMarkdownFiles($contentDir);

echo "Found " . count($files) . " markdown files to check.\n\n";

$dryRun = true; // Set to false to actually make changes

if ($dryRun) {
    echo "🔍 DRY RUN MODE - No files will be modified\n";
    echo "Set \$dryRun = false in the script to make actual changes\n\n";
}

$totalChanges = 0;

foreach ($files as $file) {
    $relativePath = str_replace($contentDir, '', $file);
    echo "Processing: $relativePath\n";
    
    $changes = processFile($file, $dryRun);
    $totalChanges += $changes;
    
    if ($changes === 0) {
        echo "    ✨ No changes needed\n";
    }
    echo "\n";
}

echo "Migration Summary:\n";
echo "==================\n";
echo "Files processed: " . count($files) . "\n";
echo "Total changes: $totalChanges\n";

if ($dryRun && $totalChanges > 0) {
    echo "\n💡 To apply these changes, edit this script and set \$dryRun = false\n";
}

echo "\nMigration complete!\n";
?>
