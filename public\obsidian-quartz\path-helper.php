<?php
/**
 * Path Helper Utility
 * Provides centralized path resolution for the obsidian-quartz blog system
 * Resolves all path-related issues including relative paths, image paths, and content loops
 */

class PathHelper {
    private static $config = null;
    private static $currentDepth = 0;
    private static $basePath = '';
    
    /**
     * Initialize the path helper with configuration
     */
    public static function init($config, $currentFilePath = '') {
        self::$config = $config;
        self::calculateDepth($currentFilePath);
        self::calculateBasePath();
    }
    
    /**
     * Calculate the depth of the current file relative to the site root
     */
    private static function calculateDepth($currentFilePath) {
        if (empty($currentFilePath)) {
            // Try to determine from current script
            $currentFilePath = $_SERVER['SCRIPT_FILENAME'] ?? '';
        }
        
        // Get the site root directory
        $siteRoot = dirname(__FILE__);
        
        // Calculate relative path from site root
        $relativePath = str_replace($siteRoot, '', $currentFilePath);
        $relativePath = str_replace('\\', '/', $relativePath);
        $relativePath = ltrim($relativePath, '/');
        
        // Count directory levels
        if (empty($relativePath) || $relativePath === basename($currentFilePath)) {
            self::$currentDepth = 0; // File is in root
        } else {
            self::$currentDepth = substr_count(dirname($relativePath), '/') + 1;
        }
    }
    
    /**
     * Calculate the base path (number of ../ needed to reach root)
     */
    private static function calculateBasePath() {
        if (self::$currentDepth === 0) {
            self::$basePath = '';
        } else {
            self::$basePath = str_repeat('../', self::$currentDepth);
        }
    }
    
    /**
     * Get the base path to reach the site root
     */
    public static function getBasePath() {
        return self::$basePath;
    }
    
    /**
     * Get path to CSS directory
     */
    public static function getCssPath() {
        return self::$basePath . (self::$config['paths']['css'] ?? 'css/');
    }
    
    /**
     * Get path to JavaScript directory
     */
    public static function getJsPath() {
        return self::$basePath . (self::$config['paths']['js'] ?? 'js/');
    }
    
    /**
     * Get path to images directory
     */
    public static function getImagesPath() {
        return self::$basePath . (self::$config['paths']['images'] ?? 'img/');
    }
    
    /**
     * Get path to includes directory
     */
    public static function getIncludesPath() {
        return self::$basePath . (self::$config['paths']['includes'] ?? 'includes/');
    }
    
    /**
     * Get path to content directory
     */
    public static function getContentPath() {
        return self::$basePath . (self::$config['paths']['content'] ?? 'content/');
    }
    
    /**
     * Get path to data directory
     */
    public static function getDataPath() {
        return self::$basePath . (self::$config['paths']['data'] ?? 'data/');
    }
    
    /**
     * Get path to a specific image file
     */
    public static function getImageUrl($imageName) {
        return self::getImagesPath() . ltrim($imageName, '/');
    }
    
    /**
     * Get path to a specific CSS file
     */
    public static function getCssUrl($cssFile) {
        return self::getCssPath() . ltrim($cssFile, '/');
    }
    
    /**
     * Get path to a specific JS file
     */
    public static function getJsUrl($jsFile) {
        return self::getJsPath() . ltrim($jsFile, '/');
    }
    
    /**
     * Get path to the main template file
     */
    public static function getTemplatePath() {
        return self::$basePath . 'page template.htm';
    }
    
    /**
     * Get path to config file
     */
    public static function getConfigPath() {
        return self::$basePath . 'config.php';
    }
    
    /**
     * Resolve internal link paths (for wiki-style links)
     */
    public static function resolveInternalLink($pageName, $currentDirectory = '') {
        // Handle paths like "category/page-name"
        if (strpos($pageName, '/') !== false) {
            $parts = explode('/', $pageName);
            $category = $parts[0];
            $page = end($parts);
            $pageSlug = self::slugify($page);
            
            // If we're already in the target category, just link to the page
            if (basename($currentDirectory) === $category) {
                return $pageSlug . '.php';
            }
            
            // Otherwise, build path to the category
            if (self::$currentDepth > 0) {
                // We're in content directory, use relative path
                return '../' . $category . '/' . $pageSlug . '.php';
            } else {
                // We're in site root, use full content path
                return self::$basePath . 'content/' . $category . '/' . $pageSlug . '.php';
            }
        }
        
        // For simple page names, assume they're in the content root or current directory
        $pageSlug = self::slugify($pageName);
        
        // If we're in a content subdirectory, try to link to a page in the same directory first
        if (self::$currentDepth > 0 && !empty($currentDirectory)) {
            return $pageSlug . '.php';
        }
        
        // Otherwise, link to content root
        return self::getContentPath() . $pageSlug . '.php';
    }
    
    /**
     * Convert text to URL-friendly slug
     */
    public static function slugify($text) {
        $slug = strtolower(trim($text));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        return trim($slug, '-');
    }
    
    /**
     * Get the current depth level
     */
    public static function getCurrentDepth() {
        return self::$currentDepth;
    }
    
    /**
     * Check if we're in the content directory
     */
    public static function isInContentDirectory() {
        return self::$currentDepth > 0;
    }
    
    /**
     * Get category index path
     */
    public static function getCategoryIndexPath($category) {
        // If we're already in the content directory, use relative path
        if (self::$currentDepth > 0) {
            // For files in content subdirectories, go up one level then to category
            if (self::$currentDepth > 1) {
                return '../' . $category . '/index.php';
            }
            // For files in content root, just go to category
            return $category . '/index.php';
        }
        // For files in site root, use full content path
        return self::getContentPath() . $category . '/index.php';
    }
    
    /**
     * Get home page path
     */
    public static function getHomePath() {
        return self::$basePath . 'index.html';
    }
    
    /**
     * Generate all path constants for use in templates
     */
    public static function getPathConstants() {
        return [
            'base_path' => self::getBasePath(),
            'css_path' => self::getCssPath(),
            'js_path' => self::getJsPath(),
            'images_path' => self::getImagesPath(),
            'includes_path' => self::getIncludesPath(),
            'content_path' => self::getContentPath(),
            'data_path' => self::getDataPath(),
            'template_path' => self::getTemplatePath(),
            'config_path' => self::getConfigPath(),
            'home_path' => self::getHomePath(),
            'current_depth' => self::getCurrentDepth(),
            'is_in_content' => self::isInContentDirectory()
        ];
    }
    
    /**
     * Set the current depth manually (useful for build scripts)
     */
    public static function setCurrentDepth($depth) {
        self::$currentDepth = $depth;
        self::calculateBasePath();
    }
    
    /**
     * Calculate depth from a file path string
     */
    public static function calculateDepthFromPath($filePath) {
        // Remove the content directory prefix if present
        $contentDir = self::$config['paths']['content'] ?? 'content/';
        if (strpos($filePath, $contentDir) === 0) {
            $filePath = substr($filePath, strlen($contentDir));
        }
        
        // Count directory separators
        $filePath = str_replace('\\', '/', $filePath);
        $filePath = trim($filePath, '/');
        
        if (empty($filePath) || strpos($filePath, '/') === false) {
            return 1; // File is in content root
        }
        
        return substr_count(dirname($filePath), '/') + 2; // +1 for content dir, +1 for subdirectory
    }

    /**
     * Fix content path loops
     * Add this function to your path-helper.php file
     */
    public static function fixContentPath($path) {
        // Remove duplicate content/ prefixes
        $pattern = '~^(content/)+~';
        $fixed = preg_replace($pattern, 'content/', $path);
        return $fixed;
    }
}

/**
 * Convenience function to initialize path helper
 */
function initPaths($config, $currentFile = '') {
    PathHelper::init($config, $currentFile);
    return PathHelper::getPathConstants();
}

/**
 * Quick access functions for common paths
 */
function cssPath($file = '') {
    return PathHelper::getCssPath() . $file;
}

function jsPath($file = '') {
    return PathHelper::getJsPath() . $file;
}

function imgPath($file = '') {
    return PathHelper::getImagesPath() . $file;
}

function contentPath($file = '') {
    return PathHelper::getContentPath() . $file;
}

function basePath() {
    return PathHelper::getBasePath();
}

