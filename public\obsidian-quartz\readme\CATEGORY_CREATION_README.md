# Category Creation Guide

This guide explains how to manually create new category sections on the obsidian-quartz site.

## Overview

Categories are organized sections that group related content together. Each category needs:
1. A directory in the `content/` folder
2. An `index.php` file that serves as the category landing page
3. Individual post files (`.md` and `.php` pairs)
4. Registration in the main site index

## Step-by-Step Process

### 1. Create the Category Directory

Create a new directory under `content/` with your category name:
```
content/your-category-name/
```

### 2. Create the Category Index File

Create an `index.php` file in your category directory. Use this template:

```php
<?php
// Auto-generated category index
// Category: your-category-name

// Load path helper and configuration with fallback
$pathPrefix = '../../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Your Category Name Index';
$meta_description = 'Description of your category';
$meta_keywords = 'category, keywords, A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$related_posts = [];

// Category posts data
$category_posts = array (
  0 => 
  array (
    'title' => 'First Post Title',
    'url' => 'first-post.php',
    'date' => '2024-01-01',
    'excerpt' => 'Brief description of the first post.',
    'thumbnail' => null,
  ),
  // Add more posts here...
);

// Include the template
include $paths['template_path'] . 'template.htm';

function renderContent() {
    global $paths, $category_posts;
    
    echo <<<HTML
    <div class="category-header">
        <h1>Your Category Name</h1>
        <p class="category-description">Description of what this category contains.</p>
        
        <div class="category-intro">
            <p>Detailed introduction to the category content.</p>
        </div>
    </div>

    <div class="post-grid">
        <?php foreach ($category_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post['thumbnail']) && $post['thumbnail']): ?>
                        <?php if (preg_match('/^https?:\/\//', $post['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php else: ?>
                            <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="post-thumb-placeholder">
                            <i class="icon-document"></i>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <?php if (isset($post['excerpt']) && $post['excerpt']): ?>
                        <p class="post-card-excerpt"><?php echo htmlspecialchars($post['excerpt']); ?></p>
                    <?php endif; ?>
                    <?php if (isset($post['date']) && $post['date']): ?>
                        <div class="post-card-meta">
                            <span class="post-card-date"><?php echo date('M j, Y', strtotime($post['date'])); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>

HTML;
}
?>
```

### 3. Add Category to Main Index

Edit `index.html` to include your new category in two places:

#### A. Categories Grid Section
Find the "Browse Categories" section and add:
```html
<a href="content/your-category-name/index.php" class="category-card">
    <h3>Your Category Name</h3>
    <p>Brief description of the category.</p>
</a>
```

#### B. Quick Links Section
Find the quick links list and add:
```html
<li>
    <a href="content/your-category-name/index.php" class="internal-link">Your Category Name</a>
</li>
```

### 4. Create Individual Posts

For each post in your category:

1. Create a `.md` file with your content
2. Create a corresponding `.php` file that follows the site's post template structure
3. Update the `$category_posts` array in your `index.php` with the new post information

### 5. Path Helper Requirements

**Important**:
- **Category index files** (`index.php`) must use `$pathPrefix = '../../';` to properly locate the `path-helper.php` file from the category subdirectory
- **Individual post files** within the category should use `$pathPrefix = '';` and rely on the fallback mechanism

The path structure is:
- Root: `path-helper.php`
- Category: `content/category-name/index.php` (needs `../../` to reach root)
- Posts: `content/category-name/post.php` (fallback mechanism handles path resolution)

## Common Issues and Solutions

### "Could not find path-helper.php" Error

This error occurs when the path prefix is incorrect. Make sure:
- Category index files use `$pathPrefix = '../../';`
- Individual post files use `$pathPrefix = '';`
- The `path-helper.php` file exists in the root directory
- File paths are relative to the current file location

**Important**: The fallback mechanism in the path helper code will try:
1. The specified path first (e.g., `../../path-helper.php` for category index)
2. One level up as a fallback (e.g., `../../../path-helper.php`)

This should handle most path resolution issues automatically. If you still get this error:
1. Verify the `path-helper.php` file exists in the site root
2. Check file permissions
3. Ensure the category directory structure is correct (`content/category-name/index.php`)
4. Test the path resolution using a simple PHP script in the category directory

### Missing Template Files

Ensure the following files exist:
- `path-helper.php` (in root directory)
- `config.php` (in root directory)  
- `template.htm` (in templates directory)

### Category Not Appearing

If your category doesn't show up:
1. Check that `index.php` exists in the category directory
2. Verify the category is added to `index.html`
3. Ensure proper file permissions

## File Structure Example

```
content/
├── your-category-name/
│   ├── index.php (category landing page)
│   ├── post-1.md
│   ├── post-1.php
│   ├── post-2.md
│   └── post-2.php
├── other-category/
│   └── ...
└── index.php
```

## Testing Your Category

1. Navigate to `your-site.com/content/your-category-name/index.php`
2. Verify the page loads without errors
3. Check that all posts are listed correctly
4. Test navigation to individual posts
5. Confirm the category appears in the main site navigation

## Maintenance

When adding new posts to an existing category:
1. Create the `.md` and `.php` files
2. Update the `$category_posts` array in the category's `index.php`
3. Test the category page to ensure the new post appears

This system allows for organized, scalable content management while maintaining the site's structure and navigation consistency.
