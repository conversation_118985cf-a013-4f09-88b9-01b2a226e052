# Quick Cheat Sheet - <PERSON><PERSON> <PERSON><PERSON> Chips Website

## 🚀 Essential Commands

### Blog Posts
```bash
# Convert all Markdown to PHP
php build.php

# Or via browser
http://localhost/your-path/build.php?build=1
```

### Store Management
```bash
# Generate/update all store product pages
php generate-store-pages.php
```

### Image Migration
```bash
# Convert old <img> tags to {{img:filename}}
php migrate-images.php
```

## 📝 Quick Workflows

### Adding a Blog Post
1. Create `content/category/my-post.md`
2. Add frontmatter:
   ```markdown
   ---
   title: My Post Title
   author: A. A. Chips
   date: 2025-01-15
   tags: tag1, tag2
   ---
   ```
3. Write content in Markdown
4. Run `php build.php`
5. Visit `http://localhost/your-path/content/category/my-post.php`

### Adding a Store Item
1. Edit `data/store-items.json`:
   ```json
   {
     "id": "item-1",
     "slug": "my-product",
     "title": "Product Name",
     "description": "Description",
     "price": 25.00,
     "status": "Available",
     "type": "sale",
     "images": ["product.jpg"],
     "condition": "New",
     "date_listed": "2025-01-15",
     "acquisition_story": "Story here"
   }
   ```
   **Note**:
   - `images` should be just the filename (e.g., `"product.jpg"`)
   - Do NOT include `img/store/` in the JSON
   - Optional fields: `weight_lbs`, `condition`, `acquisition_story`

2. Add image to `img/store/product.jpg`
3. Run `php generate-store-pages.php`
4. Visit `http://localhost/your-path/store/my-product.php`

### Using Images in Posts
1. Add to `data/gallery.json`:
   ```json
   {
     "filename": "self/my-photo.jpg",
     "alt": "Description",
     "caption": "Caption text",
     "commentary": "Optional commentary"
   }
   ```
2. In your Markdown: `{{img:my-photo.jpg}}`
3. Run `php build.php`

## 📁 Key File Locations

| What | Where |
|------|-------|
| Blog posts (source) | `content/**/*.md` |
| Blog posts (generated) | `content/**/*.php` |
| Store items data | `data/store-items.json` |
| Store pages | `store/*.php` |
| Store images | `img/store/*.jpg` |
| Gallery images | `img/**/*.jpg` |
| Gallery metadata | `data/gallery.json` |
| Site config | `config.php` |
| Main template | `page template.htm` |

## 🎨 Common Edits

### Change Site Title
Edit `config.php`:
```php
'site' => [
    'title' => 'Your Site Name',
    'description' => 'Your description',
    'author' => 'Your Name'
]
```

### Add a Category
1. Create `content/new-category/` directory
2. Create `content/new-category/index.php` (see `CATEGORY_CREATION_README.md`)
3. Add to `config.php` categories array
4. Add link in `index.html`

### Change Colors
Edit `css/style.css`:
```css
:root {
    --bg-primary: #000;
    --bg-secondary: #1a1a1a;
    --text-primary: #00ff00;
    --accent-color: #00ff00;
}
```

## 🔍 Debugging

### Check if XAMPP is running
- Open XAMPP Control Panel
- Ensure Apache is running (green)
- Ensure MySQL is running (green) if using comments

### Test PHP scripts
```bash
# From command line in project directory
php -v  # Check PHP version
php build.php  # Run build script
```

### Validate JSON files
- Use online JSON validator: https://jsonlint.com/
- Check `data/store-items.json`
- Check `data/gallery.json`

### Check file permissions
- Ensure web server can read all files
- Ensure PHP can write to directories (for generated files)

## 🆘 "I Forgot How To..."

### ...add a new blog post?
Create `.md` file → Add frontmatter → Run `php build.php`

### ...add a store item?
Edit `store-items.json` → Add image to `img/store/` → Run `php generate-store-pages.php`

### ...use images in posts?
Add to `gallery.json` → Use `{{img:filename}}` → Run `php build.php`

### ...update an existing post?
Edit the `.md` file → Run `php build.php` (overwrites `.php` file)

### ...remove a store item?
Remove from `store-items.json` → Run `php generate-store-pages.php` → Manually delete old `.php` file from `store/`

### ...change the site theme?
Edit `css/style.css` (changes apply immediately, no build needed)

## 📚 More Help

- Full guide: `README.md`
- Category creation: `CATEGORY_CREATION_README.md`
- Image system: `IMAGE_REFERENCE_GUIDE.md`
- Store details: `STORE_README.md`

## 🎯 Development Workflow

```
1. Start XAMPP (Apache + MySQL)
2. Edit content (.md files or JSON)
3. Run appropriate build command
4. Test in browser (http://localhost/...)
5. Repeat as needed
6. Deploy to production when ready
```

## 🌐 Local URLs

- Main site: `http://localhost/your-path/index.php`
- Store: `http://localhost/your-path/store.php`
- Gallery: `http://localhost/your-path/gallery.php`
- Build script: `http://localhost/your-path/build.php?build=1`

## 💡 Pro Tips

- Always run build scripts after editing source files
- Keep `gallery.json` updated for better accessibility
- Use descriptive slugs for SEO
- Test locally before deploying
- Back up your database regularly
- Store credentials outside `public_html` in production
- Use version control (git) to track changes

---

**Quick Start After Break:**
1. Start XAMPP
2. Navigate to project
3. Run `php build.php` and `php generate-store-pages.php`
4. Test site in browser
5. You're back! 🎉

