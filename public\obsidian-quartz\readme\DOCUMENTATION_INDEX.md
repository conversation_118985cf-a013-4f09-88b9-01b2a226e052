# 📚 Documentation Index

Welcome to the A. A. Chips website documentation! This index will help you find exactly what you need.

## 🎯 Start Here Based on Your Situation

### 👋 "I'm returning after a few months off"
**Read**: `GETTING_STARTED_AFTER_BREAK.md`
- 5-minute quick start
- Essential commands refresher
- Common tasks checklist
- Troubleshooting guide

### ⚡ "I just need a quick command"
**Read**: `CHEATSHEET.md`
- All essential commands
- Quick workflows
- Common edits
- Debugging tips

### 📖 "I want to understand the whole system"
**Read**: `README.md`
- Complete guide to everything
- How the blog works
- How the store works
- How images work
- Customization guide

### 🗂️ "I'm confused about what files do what"
**Read**: `FILE_STRUCTURE_GUIDE.md`
- Visual directory structure
- What each file does
- What to edit vs. what not to edit
- File relationships and workflows

## 📑 All Documentation Files

### Core Documentation

#### 1. README.md
**The Complete Guide**
- Overview of the entire system
- Blog system explanation
- Store system explanation
- Image system explanation
- Configuration guide
- Deployment instructions
- Troubleshooting

**Read this when**: You want comprehensive understanding

#### 2. GETTING_STARTED_AFTER_BREAK.md
**Quick Start Guide**
- 5-minute setup
- Essential commands
- Common tasks
- First 10 minutes back
- Quick troubleshooting

**Read this when**: You're returning to the project after time away

#### 3. CHEATSHEET.md
**Quick Reference**
- Essential commands
- Quick workflows
- Key file locations
- Common edits
- Debugging tips
- Pro tips

**Read this when**: You need a quick reminder of a command

#### 4. FILE_STRUCTURE_GUIDE.md
**File Organization**
- Directory structure
- What each file does
- File relationships
- What to edit vs. not edit
- Regeneration rules

**Read this when**: You're confused about file organization

### Specialized Guides

#### 5. CATEGORY_CREATION_README.md
**Creating Blog Categories**
- Step-by-step category creation
- Template code
- Path helper requirements
- Common issues
- Testing guide

**Read this when**: Adding a new blog category

#### 6. IMAGE_REFERENCE_GUIDE.md
**Image System**
- How to use `{{img:filename}}`
- Gallery.json structure
- Image lookup logic
- Benefits and features
- Migration from old system
- Troubleshooting

**Read this when**: Working with images in posts

#### 7. STORE_README.md
**Store System**
- Store overview
- Data structure
- Adding items
- Features
- Styling
- Maintenance
- Troubleshooting

**Read this when**: Managing the store

#### 8. store/README.md
**Store Directory**
- Store item pages
- Adding new items
- Image naming conventions

**Read this when**: Working directly with store files

#### 9. img/store/README.md
**Store Images**
- Image requirements
- Image guidelines
- Adding images
- Placeholder images

**Read this when**: Adding product photos

## 🔍 Find Information By Topic

### Commands & Scripts

| Topic | Document | Section |
|-------|----------|---------|
| Build blog posts | CHEATSHEET.md | Essential Commands |
| Generate store pages | CHEATSHEET.md | Essential Commands |
| Migrate images | CHEATSHEET.md | Essential Commands |
| All commands | GETTING_STARTED_AFTER_BREAK.md | The Two Essential Commands |

### Blog System

| Topic | Document | Section |
|-------|----------|---------|
| How blog works | README.md | How the Blog Works |
| Writing posts | README.md | Writing a New Post |
| Markdown features | README.md | Markdown Features Supported |
| Category structure | README.md | Category Structure |
| Creating categories | CATEGORY_CREATION_README.md | Entire document |

### Store System

| Topic | Document | Section |
|-------|----------|---------|
| How store works | README.md | How the Store Works |
| Adding items | README.md | Adding a New Item |
| Store features | STORE_README.md | Features |
| Data structure | STORE_README.md | Data Structure |
| Image requirements | img/store/README.md | Entire document |

### Image System

| Topic | Document | Section |
|-------|----------|---------|
| Using images | README.md | How the Image System Works |
| Image syntax | IMAGE_REFERENCE_GUIDE.md | Basic Syntax |
| Gallery.json | IMAGE_REFERENCE_GUIDE.md | What Gets Generated |
| Troubleshooting | IMAGE_REFERENCE_GUIDE.md | Troubleshooting |

### File Organization

| Topic | Document | Section |
|-------|----------|---------|
| Directory structure | FILE_STRUCTURE_GUIDE.md | Directory Overview |
| Key files | FILE_STRUCTURE_GUIDE.md | Key Files Explained |
| What to edit | FILE_STRUCTURE_GUIDE.md | What to Edit vs. What Not to Edit |
| File relationships | FILE_STRUCTURE_GUIDE.md | File Relationships |

### Configuration

| Topic | Document | Section |
|-------|----------|---------|
| Site configuration | README.md | Site Configuration |
| Customization | README.md | Customization |
| Colors | CHEATSHEET.md | Change Colors |
| Deployment | README.md | Deployment |

### Troubleshooting

| Topic | Document | Section |
|-------|----------|---------|
| Quick fixes | GETTING_STARTED_AFTER_BREAK.md | Troubleshooting |
| Common issues | README.md | Troubleshooting |
| After break issues | GETTING_STARTED_AFTER_BREAK.md | Common Tasks Checklist |
| File issues | FILE_STRUCTURE_GUIDE.md | Common Mistakes |

## 🎓 Learning Path

### Beginner (Just Starting)
1. Read `GETTING_STARTED_AFTER_BREAK.md` - Get oriented
2. Skim `CHEATSHEET.md` - Know what's possible
3. Try adding a blog post - Practice the workflow
4. Try adding a store item - Practice the workflow

### Intermediate (Regular Use)
1. Keep `CHEATSHEET.md` handy - Quick reference
2. Read `README.md` sections as needed - Deep dives
3. Explore `FILE_STRUCTURE_GUIDE.md` - Understand organization
4. Customize the site - Make it your own

### Advanced (Customization)
1. Study `FILE_STRUCTURE_GUIDE.md` - Understand architecture
2. Read all specialized guides - Master each system
3. Modify build scripts - Extend functionality
4. Create new features - Build on the foundation

## 🔄 Common Workflows

### "I want to write a blog post"
1. Read: `CHEATSHEET.md` → "Adding a Blog Post"
2. Create `.md` file
3. Run `php build.php`
4. Done!

### "I want to add a product to the store"
1. Read: `CHEATSHEET.md` → "Adding a Store Item"
2. Edit `store-items.json`
3. Add image to `img/store/`
4. Run `php generate-store-pages.php`
5. Done!

### "I want to use images in my posts"
1. Read: `IMAGE_REFERENCE_GUIDE.md` → "Basic Syntax"
2. Add image to `gallery.json`
3. Use `{{img:filename}}` in Markdown
4. Run `php build.php`
5. Done!

### "I want to create a new category"
1. Read: `CATEGORY_CREATION_README.md` → Entire guide
2. Follow step-by-step instructions
3. Test the category
4. Done!

### "I want to customize the look"
1. Read: `README.md` → "Customization"
2. Edit `css/style.css`
3. Refresh browser
4. Done!

## 📞 Quick Help

### "I forgot the command to..."
→ `CHEATSHEET.md`

### "I don't understand how..."
→ `README.md`

### "I can't find the file for..."
→ `FILE_STRUCTURE_GUIDE.md`

### "I'm getting an error..."
→ `GETTING_STARTED_AFTER_BREAK.md` → Troubleshooting

### "I want to add a..."
→ `CHEATSHEET.md` → Quick Workflows

## 🎯 Documentation Summary

```
GETTING_STARTED_AFTER_BREAK.md  ← Start here if returning
         ↓
    CHEATSHEET.md               ← Quick commands
         ↓
      README.md                 ← Full guide
         ↓
  ┌──────┴──────┐
  ↓             ↓
Specialized   FILE_STRUCTURE_GUIDE.md
Guides        (understand files)
  ↓
- CATEGORY_CREATION_README.md
- IMAGE_REFERENCE_GUIDE.md
- STORE_README.md
```

## 💡 Pro Tips

1. **Bookmark this index** for quick navigation
2. **Start with the quick guides** before diving deep
3. **Use Ctrl+F** to search within documents
4. **Keep CHEATSHEET.md open** while working
5. **Refer to README.md** for detailed explanations

## 🎉 You're All Set!

You now know where to find everything. Pick the document that matches your current need and dive in!

**Most common starting points**:
- Just returning? → `GETTING_STARTED_AFTER_BREAK.md`
- Need a command? → `CHEATSHEET.md`
- Want full details? → `README.md`

Happy creating! 🚀

