# File Structure Guide

This document explains what each file and directory does in the A. A. Chips website.

## 📂 Directory Overview

```
public/obsidian-quartz/
│
├── 📄 README.md                    ← YOU ARE HERE (main documentation)
├── 📄 CHEATSHEET.md                ← Quick reference guide
├── 📄 CATEGORY_CREATION_README.md  ← How to create new categories
├── 📄 IMAGE_REFERENCE_GUIDE.md     ← Image system documentation
├── 📄 STORE_README.md              ← Store system documentation
│
├── 🔧 build.php                    ← Converts Markdown to PHP
├── 🔧 generate-store-pages.php     ← Creates store product pages
├── 🔧 migrate-images.php           ← Migrates old image tags
│
├── ⚙️ config.php                   ← Site configuration
├── ⚙️ path-helper.php              ← Path resolution helper
├── ⚙️ secure_config.php            ← Database credentials (move outside public_html)
│
├── 🎨 page template.htm            ← Main page template
├── 🏠 index.php                    ← Homepage
├── 🏠 index.html                   ← Static homepage alternative
├── 🛒 store.php                    ← Store main page
├── 🖼️ gallery.php                  ← Image gallery page
│
├── 📁 content/                     ← Blog posts and content
│   ├── writings/                   ← Writing category
│   │   ├── post1.md               ← Source Markdown
│   │   ├── post1.php              ← Generated PHP (don't edit directly)
│   │   └── index.php              ← Category landing page
│   ├── kitchen/                    ← Recipe category
│   ├── judaism/                    ← Judaism category
│   └── [other categories]/
│
├── 📁 store/                       ← Store product pages
│   ├── item-template.php          ← Template for generating pages
│   ├── product-slug.php           ← Individual product pages (generated)
│   └── README.md                  ← Store directory documentation
│
├── 📁 data/                        ← JSON data files
│   ├── gallery.json               ← Image metadata (alt text, captions)
│   ├── gallery-backup.json        ← Backup of gallery data
│   └── store-items.json           ← Store inventory data
│
├── 📁 img/                         ← Images
│   ├── store/                     ← Store product images
│   │   ├── product1.jpg
│   │   └── README.md              ← Image requirements
│   ├── self/                      ← Personal photos
│   ├── art/                       ← Art images
│   ├── thumbs/                    ← Thumbnail images
│   └── [other folders]/
│
├── 📁 css/                         ← Stylesheets
│   ├── style.css                  ← Main stylesheet
│   ├── store.css                  ← Store-specific styles
│   ├── comments.css               ← Comment system styles
│   ├── music-player.css           ← Music player styles
│   └── visitor-counter.css        ← Visitor counter styles
│
├── 📁 js/                          ← JavaScript files
│   ├── script.js                  ← Main JavaScript
│   ├── image-resolver.js          ← Client-side image processing
│   ├── gallery.js                 ← Gallery functionality
│   ├── comments.js                ← Comment system
│   └── [other scripts]/
│
├── 📁 includes/                    ← PHP includes
│   ├── header.php                 ← Site header
│   ├── footer.php                 ← Site footer
│   ├── sidebar.php                ← Sidebar
│   └── image-helper.php           ← Server-side image processing
│
├── 📁 comments/                    ← Comment system
│   ├── database.php               ← Database setup
│   ├── comment-handler.php        ← Comment processing
│   ├── comments-display.php       ← Comment display
│   ├── google-auth.php            ← Google authentication
│   └── config.php                 ← Comment system config
│
└── 📁 visitor-counter/             ← Visitor tracking
    ├── VisitorCounter.php
    └── visitor-display.php
```

## 🔑 Key Files Explained

### Configuration Files

**config.php**
- Site title, description, author
- Category definitions
- Build settings
- Path configurations
- **Edit this to**: Change site-wide settings

**path-helper.php**
- Calculates relative paths based on file location
- Handles navigation between directories
- **Don't edit unless**: You understand the path resolution system

**secure_config.php**
- Database credentials
- **Security note**: Move outside `public_html` in production

### Build Scripts

**build.php**
- Reads all `.md` files in `content/`
- Parses frontmatter (title, date, tags, etc.)
- Converts Markdown to HTML
- Processes `{{img:filename}}` references
- Generates `.php` files
- **Run when**: You edit any `.md` file

**generate-store-pages.php**
- Reads `data/store-items.json`
- Creates individual product pages in `store/`
- Generates `store/README.md`
- **Run when**: You add/edit store items

**migrate-images.php**
- Finds old `<img>` tags in Markdown files
- Converts to `{{img:filename}}` syntax
- **Run once**: When migrating to new image system

### Data Files

**data/gallery.json**
```json
{
  "images": [
    {
      "filename": "self/photo.jpg",
      "alt": "Accessibility description",
      "caption": "Display caption",
      "commentary": "Optional longer text",
      "width": 800,
      "height": 600
    }
  ]
}
```
- Central image metadata
- Used by `{{img:filename}}` system
- **Edit when**: Adding images to posts

**data/store-items.json**
```json
{
  "items": [
    {
      "id": "unique-id",
      "slug": "url-name",
      "title": "Product Name",
      "description": "Description",
      "price": 25.00,
      "status": "Available",
      "type": "sale",
      "images": ["product.jpg"],
      "condition": "New",
      "date_listed": "2025-01-15",
      "acquisition_story": "Story"
    }
  ]
}
```
- Store inventory
- **Edit when**: Adding/updating products

### Template Files

**page template.htm**
- Main HTML structure
- Includes header, footer, sidebar
- Handles metadata, CSS, JavaScript
- **Edit when**: Changing site-wide layout

**store/item-template.php**
- Template for individual product pages
- Copied by `generate-store-pages.php`
- **Edit when**: Changing product page layout

## 📝 File Relationships

### Blog Post Flow
```
1. You create: content/writings/my-post.md
2. build.php reads it
3. build.php generates: content/writings/my-post.php
4. User visits: /content/writings/my-post.php
5. PHP includes: page template.htm
6. Template includes: header.php, footer.php, sidebar.php
7. Page renders with: css/style.css, js/script.js
```

### Store Item Flow
```
1. You edit: data/store-items.json
2. You add: img/store/product.jpg
3. generate-store-pages.php reads JSON
4. Script generates: store/product-slug.php
5. User visits: /store/product-slug.php
6. PHP includes: page template.htm
7. Page displays: img/store/product.jpg
8. Styled with: css/store.css
```

### Image Reference Flow
```
1. You add to: data/gallery.json
2. You write in MD: {{img:photo.jpg}}
3. build.php processes reference
4. Looks up in: data/gallery.json
5. Generates HTML with: alt text, caption, commentary
6. Image loaded from: img/self/photo.jpg
7. Styled with: css/style.css (.dynamic-image-container)
```

## 🎯 What to Edit vs. What Not to Edit

### ✅ Safe to Edit (Source Files)
- `content/**/*.md` - Blog post source
- `data/store-items.json` - Store inventory
- `data/gallery.json` - Image metadata
- `config.php` - Site configuration
- `css/*.css` - Stylesheets
- `page template.htm` - Main template
- `includes/*.php` - Header, footer, sidebar

### ⚠️ Edit with Caution
- `build.php` - Build script logic
- `generate-store-pages.php` - Store generator logic
- `path-helper.php` - Path resolution
- `includes/image-helper.php` - Image processing

### ❌ Don't Edit Directly (Generated Files)
- `content/**/*.php` - Generated from `.md` files
- `store/*.php` (except `item-template.php`) - Generated from JSON
- These will be overwritten when you run build scripts!

## 🔄 Regeneration Rules

| You Edit | Run This | Regenerates |
|----------|----------|-------------|
| `content/**/*.md` | `php build.php` | `content/**/*.php` |
| `data/store-items.json` | `php generate-store-pages.php` | `store/*.php` |
| `data/gallery.json` | `php build.php` | All posts with `{{img:}}` |
| `css/*.css` | Nothing | Changes apply immediately |
| `config.php` | Nothing | Changes apply immediately |
| `page template.htm` | Nothing | Changes apply immediately |

## 💡 Pro Tips

1. **Always edit `.md` files, not `.php` files** for blog posts
2. **Run build scripts after editing source files**
3. **Keep backups** of `data/gallery.json` and `data/store-items.json`
4. **Use version control** (git) to track changes
5. **Test locally** before deploying to production

## 🆘 Common Mistakes

### "I edited a .php file but changes disappeared"
- You edited a generated file
- Edit the `.md` source instead
- Run `php build.php` to regenerate

### "I added a store item but no page exists"
- You forgot to run `php generate-store-pages.php`
- Run it now to create the page

### "Images aren't showing"
- Check filename in JSON matches actual file
- Check file is in correct directory (`img/store/` for store)
- Run build script to regenerate pages

### "Changes to CSS aren't showing"
- Clear browser cache (Ctrl+F5)
- Check that CSS file is being loaded (view page source)
- Verify no syntax errors in CSS

---

**Remember**: Source files → Build scripts → Generated files → Website

Edit source files, run build scripts, test the website!

