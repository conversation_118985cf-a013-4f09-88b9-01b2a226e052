# Getting Started After a Break 🎯

Welcome back! Here's everything you need to get back up to speed quickly.

## 🚀 5-Minute Quick Start

### 1. Start Your Environment
```bash
# Start XAMPP
- Open XAMPP Control Panel
- Click "Start" for Apache
- Click "Start" for MySQL (if using comments)
```

### 2. Navigate to Your Project
```bash
# In browser
http://localhost/webtech/coursework/Aprils Apple Chips HTML/aachips/public/obsidian-quartz/

# Or your custom path
http://localhost/your-path/
```

### 3. Refresh Everything
```bash
# In terminal, navigate to project directory
cd "c:\xampp2\htdocs\webtech\coursework\Aprils Apple Chips HTML\aachips\public\obsidian-quartz"

# Rebuild all blog posts
php build.php

# Regenerate all store pages
php generate-store-pages.php
```

### 4. Test the Site
- Visit homepage: `http://localhost/your-path/index.php`
- Check a blog post: `http://localhost/your-path/content/writings/`
- Check store: `http://localhost/your-path/store.php`

## 📋 Common Tasks Checklist

### Adding a New Blog Post
- [ ] Create `.md` file in appropriate `content/category/` folder
- [ ] Add frontmatter (title, date, author, tags)
- [ ] Write content in Markdown
- [ ] Run `php build.php`
- [ ] Test in browser

### Adding a Store Item
- [ ] Edit `data/store-items.json`
- [ ] Add product image to `img/store/`
- [ ] Run `php generate-store-pages.php`
- [ ] Test product page in browser

### Using Images in Posts
- [ ] Add image metadata to `data/gallery.json`
- [ ] Place image file in `img/` directory
- [ ] Use `{{img:filename}}` in Markdown
- [ ] Run `php build.php`
- [ ] Test image display in browser

## 🎓 The Two Essential Commands

### Command #1: `php build.php`
**When to use**: After editing any `.md` file

**What it does**:
- Reads all Markdown files in `content/`
- Converts Markdown to HTML
- Processes `{{img:filename}}` references
- Generates `.php` files
- Preserves frontmatter metadata

**Example**:
```bash
# You edited: content/writings/my-post.md
# Run this:
php build.php
# Result: content/writings/my-post.php is created/updated
```

### Command #2: `php generate-store-pages.php`
**When to use**: After editing `data/store-items.json`

**What it does**:
- Reads `data/store-items.json`
- Creates individual product pages
- Updates `store/README.md`
- Shows summary of changes

**Example**:
```bash
# You edited: data/store-items.json
# Run this:
php generate-store-pages.php
# Result: store/product-slug.php pages created/updated
```

## 🗺️ Site Map

```
Your Site
├── Homepage (index.php)
├── Blog Categories
│   ├── Writings (content/writings/index.php)
│   ├── Kitchen (content/kitchen/index.php)
│   ├── Judaism (content/judaism/index.php)
│   └── [Other Categories]
├── Store (store.php)
│   └── Individual Products (store/product-slug.php)
├── Gallery (gallery.php)
└── Individual Posts (content/category/post.php)
```

## 📁 Key Files Quick Reference

| File | Purpose | Edit? |
|------|---------|-------|
| `content/**/*.md` | Blog post source | ✅ YES |
| `content/**/*.php` | Generated blog pages | ❌ NO (auto-generated) |
| `data/store-items.json` | Store inventory | ✅ YES |
| `data/gallery.json` | Image metadata | ✅ YES |
| `img/store/*.jpg` | Product images | ✅ YES (add files) |
| `config.php` | Site settings | ✅ YES |
| `css/style.css` | Main styles | ✅ YES |
| `build.php` | Build script | ⚠️ CAUTION |
| `generate-store-pages.php` | Store generator | ⚠️ CAUTION |

## 🔄 Typical Workflow

### Scenario 1: Writing a New Blog Post
```
1. Create file: content/writings/my-new-post.md
2. Add frontmatter and content
3. Run: php build.php
4. Visit: http://localhost/your-path/content/writings/my-new-post.php
5. Done! ✅
```

### Scenario 2: Adding a Product
```
1. Edit: data/store-items.json (add new item)
2. Add image: img/store/product-photo.jpg
3. Run: php generate-store-pages.php
4. Visit: http://localhost/your-path/store/product-slug.php
5. Done! ✅
```

### Scenario 3: Updating Existing Post
```
1. Edit: content/writings/existing-post.md
2. Run: php build.php (overwrites .php file)
3. Refresh browser
4. Done! ✅
```

## 🐛 Troubleshooting

### "I can't remember the command to generate store pages"
```bash
php generate-store-pages.php
```

### "I edited a .md file but don't see changes"
```bash
# You need to rebuild
php build.php
```

### "I added a store item but there's no page"
```bash
# You need to generate pages
php generate-store-pages.php
```

### "Images aren't showing on store pages"
1. Check `data/store-items.json` - is the filename correct?
2. **IMPORTANT**: Image paths should be just the filename (e.g., `"product.jpg"`)
   - ✅ Correct: `"images": ["product.jpg"]`
   - ❌ Wrong: `"images": ["img/store/product.jpg"]`
3. Check `img/store/` - does the file exist?
4. Check filename matches exactly (case-sensitive)
5. Empty strings in images array are OK: `"images": [""]` will show placeholder

### "I get an error about missing weight_lbs or other fields"
- All fields except `id`, `slug`, `title`, `description`, `price`, `status`, `type`, `images`, and `date_listed` are **optional**
- You can omit: `weight_lbs`, `condition`, `acquisition_story`
- The template now safely handles missing optional fields

### "XAMPP won't start"
1. Check if another program is using port 80 (Skype, IIS, etc.)
2. Try changing Apache port in XAMPP config
3. Restart computer and try again

### "I get 'Could not find path-helper.php' error"
1. Make sure you're in the right directory
2. Check that `path-helper.php` exists in root
3. Verify file permissions

## 📚 Documentation Quick Links

- **Full Guide**: `README.md` - Complete documentation
- **Cheat Sheet**: `CHEATSHEET.md` - Quick command reference
- **File Structure**: `FILE_STRUCTURE_GUIDE.md` - What each file does
- **Categories**: `CATEGORY_CREATION_README.md` - How to add categories
- **Images**: `IMAGE_REFERENCE_GUIDE.md` - Image system details
- **Store**: `STORE_README.md` - Store system details

## 💡 Pro Tips

1. **Keep a terminal open** in your project directory for quick commands
2. **Bookmark your local URL** for easy access
3. **Use Ctrl+F5** to hard refresh and clear cache
4. **Check the terminal output** when running build scripts for errors
5. **Keep backups** of your JSON files before major edits

## 🎯 Your First 10 Minutes Back

1. ✅ Start XAMPP (Apache + MySQL)
2. ✅ Open terminal in project directory
3. ✅ Run `php build.php`
4. ✅ Run `php generate-store-pages.php`
5. ✅ Open browser to `http://localhost/your-path/`
6. ✅ Click around to verify everything works
7. ✅ Open this guide in your editor for reference
8. ✅ Review `CHEATSHEET.md` for quick commands
9. ✅ Check what you were working on last time
10. ✅ Start creating! 🎉

## 🔑 Remember These Three Things

1. **Source files** (`.md`, `.json`) are what you edit
2. **Build scripts** (`build.php`, `generate-store-pages.php`) convert source to web pages
3. **Generated files** (`.php` in content and store) are created automatically

```
Edit Source → Run Build Script → Test in Browser → Repeat
```

## 🎨 Quick Customization

### Change Site Title
Edit `config.php`:
```php
'site' => [
    'title' => 'Your New Title'
]
```

### Change Colors
Edit `css/style.css`:
```css
:root {
    --accent-color: #00ff00; /* Change this */
}
```

### Add a Category
See `CATEGORY_CREATION_README.md`

## 🚀 You're Ready!

You now have everything you need to get back to work. Remember:

- **Blog posts**: Edit `.md` → Run `php build.php`
- **Store items**: Edit JSON → Run `php generate-store-pages.php`
- **Images**: Add to `gallery.json` → Use `{{img:filename}}`

**Happy creating! 🎉**

---

**Still confused?** Check `CHEATSHEET.md` for quick commands or `README.md` for the full guide.

