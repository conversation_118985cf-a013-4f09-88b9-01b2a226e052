╔══════════════════════════════════════════════════════════════════════════════╗
║                    A. A. CHIPS WEBSITE - QUICK START                         ║
║                         (Print or Keep Open)                                 ║
╚══════════════════════════════════════════════════════════════════════════════╝

┌──────────────────────────────────────────────────────────────────────────────┐
│ 🚀 THE TWO COMMANDS YOU NEED TO REMEMBER                                     │
└──────────────────────────────────────────────────────────────────────────────┘

  1. php build.php                    → Converts blog posts (.md to .php)
  2. php generate-store-pages.php     → Creates store product pages

┌──────────────────────────────────────────────────────────────────────────────┐
│ 📝 BLOG POST WORKFLOW                                                        │
└──────────────────────────────────────────────────────────────────────────────┘

  Step 1: Create file
          content/writings/my-post.md

  Step 2: Add frontmatter
          ---
          title: My Post
          date: 2025-01-15
          author: A. A. Chips
          tags: tag1, tag2
          ---

  Step 3: Write content in Markdown

  Step 4: Run command
          php build.php

  Step 5: Visit in browser
          http://localhost/your-path/content/writings/my-post.php

┌──────────────────────────────────────────────────────────────────────────────┐
│ 🛒 STORE ITEM WORKFLOW                                                       │
└──────────────────────────────────────────────────────────────────────────────┘

  Step 1: Edit JSON
          data/store-items.json
          Add new item with:
          - id, slug, title, description
          - price, status, type
          - images: ["product.jpg"]

  Step 2: Add image
          img/store/product.jpg

  Step 3: Run command
          php generate-store-pages.php

  Step 4: Visit in browser
          http://localhost/your-path/store/product-slug.php

┌──────────────────────────────────────────────────────────────────────────────┐
│ 🖼️ IMAGE WORKFLOW                                                            │
└──────────────────────────────────────────────────────────────────────────────┘

  Step 1: Add to gallery.json
          data/gallery.json
          {
            "filename": "self/photo.jpg",
            "alt": "Description",
            "caption": "Caption"
          }

  Step 2: Place image file
          img/self/photo.jpg

  Step 3: Use in Markdown
          {{img:photo.jpg}}

  Step 4: Run command
          php build.php

┌──────────────────────────────────────────────────────────────────────────────┐
│ 🔧 ENVIRONMENT SETUP                                                         │
└──────────────────────────────────────────────────────────────────────────────┘

  1. Start XAMPP
     - Open XAMPP Control Panel
     - Start Apache
     - Start MySQL (if using comments)

  2. Navigate to project
     cd "c:\xampp2\htdocs\webtech\coursework\Aprils Apple Chips HTML\aachips\public\obsidian-quartz"

  3. Test in browser
     http://localhost/webtech/coursework/Aprils Apple Chips HTML/aachips/public/obsidian-quartz/

┌──────────────────────────────────────────────────────────────────────────────┐
│ 📁 KEY FILES                                                                 │
└──────────────────────────────────────────────────────────────────────────────┘

  EDIT THESE:
  ✅ content/**/*.md              Blog post source files
  ✅ data/store-items.json        Store inventory
  ✅ data/gallery.json            Image metadata
  ✅ config.php                   Site configuration
  ✅ css/style.css                Styles

  DON'T EDIT THESE (auto-generated):
  ❌ content/**/*.php             Generated from .md files
  ❌ store/*.php                  Generated from JSON

┌──────────────────────────────────────────────────────────────────────────────┐
│ 🐛 TROUBLESHOOTING                                                           │
└──────────────────────────────────────────────────────────────────────────────┘

  Problem: Edited .md file but no changes
  Solution: Run php build.php

  Problem: Added store item but no page
  Solution: Run php generate-store-pages.php

  Problem: Images not showing
  Solution: Check filename in JSON matches file in img/

  Problem: XAMPP won't start
  Solution: Check if port 80 is in use, restart computer

┌──────────────────────────────────────────────────────────────────────────────┐
│ 📚 DOCUMENTATION                                                             │
└──────────────────────────────────────────────────────────────────────────────┘

  Just returning?           → GETTING_STARTED_AFTER_BREAK.md
  Need a command?           → CHEATSHEET.md
  Want full guide?          → README.md
  Confused about files?     → FILE_STRUCTURE_GUIDE.md
  All documentation?        → DOCUMENTATION_INDEX.md

┌──────────────────────────────────────────────────────────────────────────────┐
│ ⚡ QUICK REFERENCE                                                           │
└──────────────────────────────────────────────────────────────────────────────┘

  Local URL:
  http://localhost/your-path/

  Terminal Commands:
  php build.php                    # Rebuild blog
  php generate-store-pages.php     # Rebuild store
  php migrate-images.php           # Migrate images

  File Locations:
  Blog posts:     content/**/*.md
  Store data:     data/store-items.json
  Store images:   img/store/
  Gallery data:   data/gallery.json
  Config:         config.php
  Styles:         css/style.css

┌──────────────────────────────────────────────────────────────────────────────┐
│ 🎯 REMEMBER                                                                  │
└──────────────────────────────────────────────────────────────────────────────┘

  1. Edit source files (.md, .json)
  2. Run build scripts (build.php, generate-store-pages.php)
  3. Test in browser
  4. Repeat!

  Source Files → Build Scripts → Generated Files → Website

┌──────────────────────────────────────────────────────────────────────────────┐
│ 💡 PRO TIPS                                                                  │
└──────────────────────────────────────────────────────────────────────────────┘

  • Keep terminal open in project directory
  • Bookmark your local URL
  • Use Ctrl+F5 to hard refresh browser
  • Check terminal output for errors
  • Keep backups of JSON files
  • Always run build scripts after editing source files

╔══════════════════════════════════════════════════════════════════════════════╗
║                           YOU'RE READY! 🎉                                   ║
║                                                                              ║
║  Edit → Build → Test → Repeat                                               ║
║                                                                              ║
║  For more help, see DOCUMENTATION_INDEX.md                                   ║
╚══════════════════════════════════════════════════════════════════════════════╝

