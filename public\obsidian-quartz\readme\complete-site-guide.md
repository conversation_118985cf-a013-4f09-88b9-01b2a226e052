# A. A. Chips Personal Website - Complete Guide

Welcome to the A. A. Chips website! This is a personal blog and store built with PHP, featuring a unique "digital garden" approach to content. This guide will help you understand how everything works and how to recreate it with your own content.

## ⚡ Quick Reference - Essential Commands

After taking a break from the project, here are the key commands you'll need:

### Blog Commands
```bash
# Convert all Markdown files to PHP (run after editing any .md files)
php build.php
```

### Store Commands
```bash
# Generate/update store product pages from store-items.json
php generate-store-pages.php
```

### Image Commands
```bash
# Migrate old <img> tags to new {{img:filename}} syntax
php migrate-images.php
```

### Workflow Summary
1. **Edit blog content**: Modify `.md` files in `content/` folders
2. **Run build**: `php build.php` to convert Markdown to PHP
3. **Edit store items**: Update `data/store-items.json`
4. **Generate store pages**: `php generate-store-pages.php`
5. **Add images**: Place files in `img/store/` matching JSON filenames
6. **Test**: Visit your local site to verify changes

### Visual Workflow

```
BLOG WORKFLOW:
content/writings/my-post.md  →  [php build.php]  →  content/writings/my-post.php  →  Website

STORE WORKFLOW:
data/store-items.json  →  [php generate-store-pages.php]  →  store/product-slug.php  →  Website
         +
img/store/product.jpg  →  (referenced by JSON)  →  Displayed on product page

IMAGE WORKFLOW:
data/gallery.json  →  [php build.php]  →  {{img:filename}} converted to HTML  →  Website
```

## 🌟 What This Site Does

This is a personal website with:
- **Blog System**: Write posts in Markdown, automatically converted to PHP pages
- **Store System**: List items for sale, giveaway, or wishlist with JSON-based management
- **Image Gallery**: Dynamic image referencing with centralized metadata
- **Comments System**: Google-authenticated commenting on posts
- **Categories**: Organized content sections (Writings, Kitchen, Judaism, etc.)
- **Dark Theme**: Eye-friendly dark background with terminal-style aesthetics

## 📁 Project Structure

```
public/obsidian-quartz/
├── content/              # Blog posts organized by category
│   ├── writings/         # Writing category
│   ├── kitchen/          # Recipe category
│   ├── judaism/          # Judaism category
│   └── [other categories]/
├── store/                # Individual store item pages
├── data/                 # JSON data files
│   ├── gallery.json      # Image metadata
│   └── store-items.json  # Store inventory
├── img/                  # Images
│   ├── store/            # Store product images
│   └── [other folders]/  # Gallery images
├── css/                  # Stylesheets
├── js/                   # JavaScript files
├── includes/             # PHP includes (header, footer, helpers)
├── comments/             # Comment system files
├── build.php             # Markdown to PHP converter
├── generate-store-pages.php  # Store page generator
├── config.php            # Site configuration
└── page template.htm     # Main page template
```

## 🚀 Quick Start

### Prerequisites
- PHP 7.4 or higher
- Web server (Apache/XAMPP recommended)
- MySQL database (for comments)

### Initial Setup

1. **Clone/Download** this project to your web server directory
2. **Configure Database** (for comments):
   - Import the database schema from `comments/database.php`
   - Update credentials in `secure_config.php` (outside public_html for security)
3. **Test the Site**: Navigate to `http://localhost/your-path/index.php`

## ✍️ How the Blog Works

### Writing a New Post

1. **Create a Markdown file** in the appropriate category folder:
   ```
   content/writings/my-new-post.md
   ```

2. **Add frontmatter** at the top of your file:
   ```markdown
   ---
   title: My Awesome Post
   author: A. A. Chips
   date: 2025-01-15
   tags: writing, personal, thoughts
   excerpt: A brief description of what this post is about
   ---
   
   # My Awesome Post
   
   Your content here...
   ```

3. **Run the build script** to convert Markdown to PHP:
   ```bash
   php build.php
   ```
   
   Or visit in browser: `http://localhost/your-path/build.php?build=1`

4. **The script will**:
   - Parse your Markdown file
   - Extract frontmatter metadata
   - Convert Markdown to HTML
   - Generate a PHP file with the same name
   - Process image references (see Image System below)

### Markdown Features Supported

- **Headers**: `#`, `##`, `###`
- **Bold**: `**text**`
- **Italic**: `*text*`
- **Links**: `[text](url)`
- **Lists**: `- item` or `1. item`
- **Blockquotes**: `> quote`
- **Code blocks**: ` ```code``` `
- **Images**: `{{img:filename}}` (see Image System)

### Category Structure

Each category has:
- **Directory**: `content/category-name/`
- **Index file**: `content/category-name/index.php` (landing page)
- **Posts**: Individual `.md` and `.php` files

To create a new category, see `CATEGORY_CREATION_README.md`.

## 🖼️ How the Image System Works

### The Smart Way: Dynamic Image References

Instead of hardcoding image paths, use the dynamic reference system:

1. **Add image to gallery.json**:
   ```json
   {
     "images": [
       {
         "filename": "self/my-photo.jpg",
         "alt": "Description for accessibility",
         "caption": "Optional caption text",
         "commentary": "Optional longer commentary",
         "width": 800,
         "height": 600
       }
     ]
   }
   ```

2. **Reference in your Markdown**:
   ```markdown
   {{img:my-photo.jpg}}
   ```

3. **The system automatically**:
   - Finds the image in gallery.json
   - Generates proper HTML with correct path
   - Includes alt text for accessibility
   - Adds caption and commentary (if provided)
   - Applies responsive styling

### Benefits
- **Single source of truth**: Update alt text once, changes everywhere
- **No broken links**: System warns if image not found
- **Better accessibility**: Proper alt text from curated descriptions
- **Consistent styling**: All images follow your design

For more details, see `IMAGE_REFERENCE_GUIDE.md`.

## 🛒 How the Store Works

### Adding a New Item

1. **Edit `data/store-items.json`**:
   ```json
   {
     "items": [
       {
         "id": "unique-id-1",
         "slug": "url-friendly-name",
         "title": "Item Title",
         "description": "Item description",
         "price": 15.00,
         "status": "Available",
         "type": "sale",
         "images": ["image1.jpg", "image2.jpg"],
         "condition": "Like new",
         "weight_lbs": 2.5,
         "date_listed": "2024-05-20",
         "acquisition_story": "How you got this item"
       }
     ]
   }
   ```

2. **Run the store page generator**:
   ```bash
   php generate-store-pages.php
   ```

   This command:
   - Reads all items from `data/store-items.json`
   - Creates/updates individual PHP pages in `store/` directory
   - Each page is named `[slug].php` (e.g., `url-friendly-name.php`)
   - Generates a README in the store directory listing all items
   - Shows summary of generated/updated pages

3. **Add product images** to `img/store/` with filenames matching the JSON
   - Image filenames in JSON should be just the filename (e.g., `"image1.jpg"`)
   - Place actual files in `img/store/image1.jpg`
   - The system automatically constructs the full path
   - Multiple images per item are supported (first image is the main image)
   - If image is missing, a placeholder "📦 Image Coming Soon" appears

### Store Item Types
- **sale**: Items for sale
- **giveaway**: Free items
- **wishlist**: Items you're looking for

### Store Features
- Filterable by type (All, For Sale, Giveaway, Wishlist)
- Individual pages for each item
- Contact buttons with pre-filled emails
- Responsive image galleries
- No checkout system (manual order handling)

For more details, see `STORE_README.md`.

## 🎨 Customization

### Site Configuration

Edit `config.php` to customize:
- Site title and description
- Author information
- Categories
- Build settings
- Path configurations

### Styling

Main stylesheet: `css/style.css`
- Dark theme with terminal aesthetics
- Custom fonts (includes Virgil.woff2)
- Responsive design
- Store-specific styles in `css/store.css`

### Colors
The site uses CSS custom properties:
- `--bg-primary`: Main background
- `--bg-secondary`: Secondary background
- `--text-primary`: Primary text
- `--accent-color`: Accent color (green)
- `--border-color`: Border color

## 💬 Comments System

The site includes a Google-authenticated comment system:
- Users sign in with Google
- Comments stored in MySQL database
- Appears on individual posts (not category pages)
- Positioned after post content, before category browsing

Configuration in `comments/config.php`.

## 🔧 Common Commands

### Build all blog posts from Markdown:
```bash
php build.php
```

### Generate store pages:
```bash
php generate-store-pages.php
```

### Migrate old image tags to new system:
```bash
php migrate-images.php
```

## 📝 Best Practices

1. **Always run build.php** after editing Markdown files
2. **Use the image reference system** (`{{img:filename}}`) instead of hardcoded paths
3. **Keep gallery.json updated** with proper alt text for accessibility
4. **Test locally** before deploying to production
5. **Store database credentials** outside public_html for security
6. **Use descriptive slugs** for store items and blog posts

## 🚨 Troubleshooting

### Coming Back After a Break?

If you're returning to the project after time away:

1. **Check your environment**:
   - Is XAMPP running? (Apache + MySQL)
   - Navigate to `http://localhost/your-path/` to test

2. **Refresh your memory**:
   - Blog posts: Edit `.md` files, run `php build.php`
   - Store items: Edit `data/store-items.json`, run `php generate-store-pages.php`
   - Images: Add to `img/store/`, reference in JSON

3. **Common "I forgot" scenarios**:
   - **"I edited a .md file but changes don't show"** → Run `php build.php`
   - **"I added a store item but no page exists"** → Run `php generate-store-pages.php`
   - **"Store images aren't showing"** → Check filenames in JSON match files in `img/store/`
   - **"I want to add a new category"** → See `CATEGORY_CREATION_README.md`

### "Could not find path-helper.php" Error
- Ensure `path-helper.php` exists in the root directory
- Check that category index files use `$pathPrefix = '../../';`
- Verify file permissions

### Images Not Showing
- Check filename spelling in JSON matches actual files
- Ensure images are in correct directory (`img/` or `img/store/`)
- Run `build.php` to regenerate pages
- For store: Images should be in `img/store/`, not `img/`

### Store Pages Not Working
- Run `php generate-store-pages.php` to regenerate
- Check that `store/` directory exists
- Verify JSON syntax is valid (use a JSON validator)
- Ensure each item has a unique `slug` field

### Build Script Errors
- Check Markdown syntax in your files
- Ensure frontmatter is properly formatted (YAML between `---` markers)
- Verify `gallery.json` is valid JSON
- Look for unclosed quotes or missing commas in JSON files

## 🌐 Deployment

### Local Development (XAMPP)
1. Place in `htdocs/` directory
2. Access via `http://localhost/your-folder/`
3. Use local MySQL for comments

### Production Webhost
1. Upload all files to `public_html/`
2. Move `secure_config.php` outside `public_html/`
3. Update database credentials
4. Test all functionality

## 📚 Documentation Guide

### 🎯 Where to Start

**Just returning after a break?**
→ Start with `GETTING_STARTED_AFTER_BREAK.md` - Quick 5-minute guide to get back up to speed

**Need a quick command?**
→ Check `CHEATSHEET.md` - All essential commands in one place

**Want to understand the whole system?**
→ You're reading it! This `README.md` is the complete guide

**Confused about files?**
→ See `FILE_STRUCTURE_GUIDE.md` - Visual guide to what each file does

### 📖 Specialized Guides

- `CATEGORY_CREATION_README.md` - How to create new blog categories
- `IMAGE_REFERENCE_GUIDE.md` - Detailed image system guide
- `STORE_README.md` - Complete store system documentation
- `store/README.md` - Store directory documentation
- `img/store/README.md` - Store image requirements

### 📋 Quick Reference

| I want to... | Read this |
|--------------|-----------|
| Get started quickly | `GETTING_STARTED_AFTER_BREAK.md` |
| Find a command | `CHEATSHEET.md` |
| Understand everything | `README.md` (this file) |
| Know what files do | `FILE_STRUCTURE_GUIDE.md` |
| Add a blog category | `CATEGORY_CREATION_README.md` |
| Use images in posts | `IMAGE_REFERENCE_GUIDE.md` |
| Manage the store | `STORE_README.md` |

## 🎯 Making It Your Own

To recreate this with your own content:

1. **Update `config.php`** with your site info
2. **Replace content** in `content/` folders with your Markdown files
3. **Update `gallery.json`** with your images
4. **Customize `store-items.json`** with your items (or remove store)
5. **Modify CSS** in `css/style.css` for your aesthetic
6. **Run `build.php`** to generate your pages
7. **Test everything** locally before deploying

## 🤝 Support

This is a personal project, but feel free to adapt it for your own use. The code is designed to be readable and modifiable.

## 📄 License

Personal project - adapt as needed for your own use.

---

**Happy blogging! 🎉**

