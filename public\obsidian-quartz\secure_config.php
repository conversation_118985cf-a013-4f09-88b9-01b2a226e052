<?php
/**
 * SECURE CONFIGURATION TEMPLATE FOR PRODUCTION
 *
 * INSTRUCTIONS:
 * 1. Copy this file to: ../secure_config/obq_comments.php (outside public_html)
 * 2. Update all the placeholder values with your actual webhost credentials
 * 3. Set file permissions to 600 (read/write for owner only)
 * 4. Delete this template file from your public directory
 */

// Security check - this prevents direct web access
if (!defined('OBQ_CONFIG_ACCESS')) {
    exit('Direct access not allowed');
}

return [
    'database' => [
        'host' => 'az1-ss110.a2hosting.com',                    // Usually 'localhost' for shared hosting
        'dbname' => 'aachipsc_aachipscomments',    // Replace with your actual database name
        'username' => 'aachipsc_commentwriter',        // Replace with your database username
        'password' => '$gdIjGDkyFLX',    // Replace with your database password
        'charset' => 'utf8mb4',
        'table_prefix' => 'aachipsc_blog_',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    ],

    'auth' => [
        'session_timeout' => 86400 * 7,          // 7 days - how long to remember user info
        'require_email_verification' => false,   // Set to true if you want email verification
    ],

    'admin' => [
        'admin_google_ids' => [],                 // Add your Google IDs if using Google auth
        'notification_email' => '<EMAIL>', // Replace with your notification email
    ],

    'spam' => [
        'honeypot_field' => 'website',           // Hidden field name for spam detection
        'spam_threshold' => 5.0,                 // Spam score threshold (0.0 to 10.0)
        'rate_limit_comments' => 5,              // Max comments per hour per IP
        'rate_limit_window' => 3600,             // Rate limit window in seconds (1 hour)
    ],

    'security' => [
        'csrf_token_name' => 'csrf_token',
        'session_name' => 'obsidian_quartz_comments',
        'cookie_lifetime' => 86400 * 30,         // 30 days
        'secure_cookies' => true,                // Set to true for HTTPS (RECOMMENDED)
        'same_site_cookies' => 'Lax',
    ],

    'display' => [
        'date_format' => 'F j, Y \a\t g:i A',   // Comment date format
        'timezone' => 'America/New_York',        // Change to your timezone
        'avatar_size' => 40,                     // Avatar size in pixels
        'show_user_email' => false,              // Never show user emails publicly
        'show_comment_count' => true,            // Show comment counts on posts
        'default_sort' => 'newest',              // 'newest', 'oldest', 'most_liked'
    ],

    'cache' => [
        'enable_cache' => false,                 // Enable caching for better performance
        'cache_duration' => 300,                 // Cache duration in seconds (5 minutes)
        'cache_directory' => __DIR__ . '/cache/', // Cache directory path
    ]
];

/*
IMPORTANT NOTES FOR PRODUCTION:

1. DATABASE CREDENTIALS:
   - Get these from your webhost control panel
   - Database name is usually: your_username_databasename
   - Username might be: your_username_dbuser
   - Password: Use a strong, unique password

2. SECURITY SETTINGS:
   - Set 'secure_cookies' to true if you have HTTPS (recommended)
   - Consider enabling email verification for comments
   - Adjust spam threshold based on your needs

3. FILE PERMISSIONS:
   - This file should be set to 600 (read/write for owner only)
   - The secure_config directory should be 755
   - Make sure this file is outside your public_html directory

4. TESTING:
   - After uploading, test database connection
   - Try posting a comment to verify functionality
   - Check error logs if something doesn't work

5. CLEANUP:
   - Delete this template file from your public directory
   - Remove any test files from production
   - Keep development files only on localhost
*/