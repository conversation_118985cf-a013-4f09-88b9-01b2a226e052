<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AAchips.co/obsidian | Weird Items Store</title>
  <style>
    /* YOUR EXISTING STYLES (mocked to match your site) */
    body {
      background-color: #0a0a0a;
      color: #ccc;
      font-family: 'Courier New', monospace;
      line-height: 1.6;
      max-width: 900px;
      margin: 0 auto;
      padding: 20px;
    }
    a {
      color: #0f0;
      text-decoration: none;
    }
    a:hover {
      text-shadow: 0 0 5px #0f0;
    }
    h1, h2 {
      color: #0f0;
      border-bottom: 1px dashed #333;
      padding-bottom: 10px;
    }
    /* STORE-SPECIFIC STYLES */
    .store-nav {
      display: flex;
      gap: 15px;
      margin: 20px 0;
    }
    .store-nav button {
      background: #111;
      border: 1px solid #0f0;
      color: #0f0;
      padding: 5px 10px;
      cursor: pointer;
      font-family: monospace;
    }
    .store-nav button.active {
      background: #0f0;
      color: #000;
    }
    .items-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
    }
    .item-card {
      background: #111;
      border: 1px solid #333;
      padding: 15px;
      transition: all 0.3s;
    }
    .item-card:hover {
      border-color: #0f0;
      box-shadow: 0 0 10px rgba(0, 255, 0, 0.1);
    }
    .item-card.sold {
      opacity: 0.6;
      border-color: #555;
    }
    .item-card.sold .price {
      text-decoration: line-through;
    }
    .item-card.sold::after {
      content: "☠️ SOLD";
      color: #f00;
      display: block;
      margin-top: 10px;
      font-size: 0.8em;
    }
    .item-card.wishlist {
      border-style: dashed;
    }
    .item-card.wishlist::before {
      content: "🌟 WISHLIST";
      color: #ff0;
      display: block;
      font-size: 0.8em;
      margin-bottom: 5px;
    }
    .item-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
      background: #000;
      border: 1px solid #333;
    }
    .price {
      color: #0f0;
      font-weight: bold;
    }
    .contact-btn {
      display: inline-block;
      margin-top: 10px;
      background: #0f0;
      color: #000;
      padding: 5px 10px;
      font-family: monospace;
    }
  </style>
</head>
<body>
  <h1>~/obsidian/store</h1>
  <p>A collection of weird items I'm selling, giving away, or wishing for. No checkout—just message me if interested.</p>
  
  <div class="store-nav">
    <button class="filter-btn active" data-filter="all">All Items</button>
    <button class="filter-btn" data-filter="sale">For Sale</button>
    <button class="filter-btn" data-filter="wishlist">Wishlist</button>
  </div>
  
  <div class="items-grid" id="items-grid">
    <!-- ITEMS WILL BE POPULATED BY JS FROM JSON -->
  </div>

  <script>
    // SAMPLE DATA (would be your items.json in reality)
    const items = [
      {
        id: "axolotl-123",
        slug: "giant-stuffed-axolotl",
        title: "Giant Stuffed Axolotl (Slightly Singed)",
        description: "Rescued from a yard sale after a failed 'release it into the wild' experiment.",
        price: 15.00,
        status: "Available",
        type: "sale",
        image: "https://placehold.co/400x300/111/0f0?text=Singed+Axolotl",
        condition: "Pre-loved (smells faintly of campfire)",
        weight_lbs: 2.5
      },
      {
        id: "boardgame-456",
        slug: "vintage-clue-game",
        title: "Vintage Clue Game (1987)",
        description: "Missing the revolver token. Probably used as evidence.",
        price: 8.00,
        status: "Sold",
        type: "sale",
        image: "https://placehold.co/400x300/111/0f0?text=Vintage+Clue",
        condition: "Well-loved (bloodstains not included)"
      },
      {
        id: "wishlist-789",
        slug: "uranium-glass-bowl",
        title: "Uranium Glass Bowl",
        description: "I want this to glow under blacklight while I eat cereal.",
        price: "Trade or $40",
        status: "Wishlist",
        type: "wishlist",
        image: "https://placehold.co/400x300/111/0f0?text=Glowy+Bowl",
        condition: "Must glow!"
      }
    ];

    // RENDER ITEMS
    const grid = document.getElementById('items-grid');
    
    function renderItems(filter = "all") {
      grid.innerHTML = '';
      const filteredItems = filter === "all" ? items : items.filter(item => item.type === filter);
      
      filteredItems.forEach(item => {
        const itemCard = document.createElement('div');
        itemCard.className = `item-card ${item.status === "Sold" ? "sold" : ""} ${item.type === "wishlist" ? "wishlist" : ""}`;
        itemCard.innerHTML = `
          <img class="item-image" src="${item.image}" alt="${item.title}">
          <h3><a href="/obsidian/store/${item.slug}">${item.title}</a></h3>
          <p>${item.description}</p>
          ${item.price ? `<p class="price">${typeof item.price === 'number' ? '$' + item.price : item.price}</p>` : ''}
          ${item.condition ? `<p><small>Condition: ${item.condition}</small></p>` : ''}
          ${item.status === "Available" ? 
            `<a class="contact-btn" href="mailto:<EMAIL>?subject=Interest in ${encodeURIComponent(item.title)}">Message me</a>` : ''}
        `;
        grid.appendChild(itemCard);
      });
    }

    // FILTER BUTTONS
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        renderItems(btn.dataset.filter);
      });
    });

    // INIT
    renderItems();
  </script>
</body>
</html>