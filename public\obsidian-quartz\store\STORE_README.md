# Weird Items Store Implementation

This document describes the store functionality that has been added to your A. A. Chips website.

## Overview

The store is a fun, low-maintenance system to list odd items for sale, giveaway, or as wishlist items. It features:

- **Manual order handling** (no cart/payment processing)
- **Unique URLs** for each item
- **Photo galleries** with multiple images per item
- **JSON-based data management** for easy updates
- **Responsive design** that matches your existing dark theme
- **Filterable categories** (All, For Sale, Giveaway, Wishlist)

## Files Added/Modified

### Core Files
- `store.php` - Main store page with item grid and filtering
- `data/store-items.json` - JSON database of all store items
- `css/store.css` - Store-specific styling
- `generate-store-pages.php` - Script to auto-generate individual item pages

### Store Directory
- `store/` - Directory containing individual item pages
- `store/item-template.php` - Template for generating item pages
- `store/[item-slug].php` - Individual pages for each item
- `store/README.md` - Documentation for the store directory

### Images
- `img/store/` - Directory for item thumbnail images
- `img/store/README.md` - Documentation for image requirements

### Modified Files
- `includes/header.php` - Added "Store" link to navigation

## Data Structure

Items are stored in `data/store-items.json` with this structure:

```json
{
  "items": [
    {
      "id": "unique-id",
      "slug": "url-friendly-name",
      "title": "Item Title",
      "description": "Item description",
      "price": 15.00, // or "Free" or "Trade or $40"
      "status": "Available", // or "Sold" or "Wishlist"
      "type": "sale", // or "giveaway" or "wishlist"
      "images": ["image1.jpg", "image2.jpg"],
      "condition": "Description of condition",
      "weight_lbs": 2.5,
      "date_listed": "2024-05-20",
      "acquisition_story": "Funny story about how you got this item"
    }
  ]
}
```

## Adding New Items

1. **Edit the JSON file**: Add your new item to `data/store-items.json`
2. **Generate the page**: Run `php generate-store-pages.php`
3. **Add images**: Place item photos in `img/store/` with filenames matching the JSON
4. **Test**: Visit your store page to see the new item

### Example: Adding a New Item

```json
{
  "id": "weird-lamp-404",
  "slug": "haunted-desk-lamp",
  "title": "Possibly Haunted Desk Lamp",
  "description": "Flickers mysteriously. Great for mood lighting or séances.",
  "price": 25.00,
  "status": "Available",
  "type": "sale",
  "images": ["lamp-1.jpg", "lamp-2.jpg"],
  "condition": "Spiritually active",
  "weight_lbs": 3.2,
  "date_listed": "2024-06-15",
  "acquisition_story": "Found at an estate sale. Previous owner claimed it helped them write their novel. The novel was never finished..."
}
```

## Image Guidelines

- **Location**: `img/store/`
- **Naming**: Must match filenames in JSON `images` array
- **Size**: Recommended 400x300px minimum
- **Format**: JPG or PNG
- **Optimization**: Keep file sizes reasonable for web use

## URL Structure

- Main store: `yoursite.com/store.php`
- Individual items: `yoursite.com/store/[item-slug].php`
- Example: `yoursite.com/store/giant-stuffed-axolotl.php`

## Features

### Filtering
- **All Items**: Shows everything
- **For Sale**: Items with `"type": "sale"`
- **Giveaway**: Items with `"type": "giveaway"`
- **Wishlist**: Items with `"type": "wishlist"`

### Item Status
- **Available**: Shows contact button
- **Sold**: Grayed out with "SOLD" badge
- **Wishlist**: Shows "Help me find this!" button

### Contact Integration
- Contact buttons generate pre-filled emails
- Email subject includes item name
- Different messages for sale vs wishlist items

## Styling

The store uses CSS custom properties to integrate with your existing theme:

- `--bg-primary`: Main background color
- `--bg-secondary`: Secondary background color
- `--text-primary`: Primary text color
- `--text-secondary`: Secondary text color
- `--accent-color`: Accent color (green)
- `--border-color`: Border color

## Maintenance

### Updating Items
1. Edit `data/store-items.json`
2. Changes appear immediately (no regeneration needed)

### Adding Images
1. Add image files to `img/store/`
2. Update the `images` array in JSON
3. Images appear immediately

### Marking Items as Sold
1. Change `"status": "Available"` to `"status": "Sold"` in JSON
2. Item will automatically show as sold

## Technical Notes

- **PHP Integration**: Uses your existing template system
- **Responsive Design**: Works on mobile and desktop
- **SEO Friendly**: Each item has its own URL and metadata
- **Performance**: Lightweight JSON-based system
- **Accessibility**: Proper semantic HTML and ARIA labels

## Customization

### Email Address
Update the email address in:
- `store.php` (line with `mailto:<EMAIL>`)
- `store/item-template.php` (same line)

### Styling
Modify `css/store.css` to change colors, fonts, or layout.

### Item Types
Add new types by:
1. Using new `type` values in JSON
2. Adding corresponding filter buttons
3. Adding CSS styles for new types

## Troubleshooting

### Images Not Showing
- Check filename spelling in JSON matches actual files
- Ensure images are in `img/store/` directory
- Check file permissions

### Item Pages Not Working
- Run `php generate-store-pages.php` to regenerate
- Check that `store/` directory exists
- Verify JSON syntax is valid

### Styling Issues
- Clear browser cache
- Check that `css/store.css` is loading
- Verify CSS custom properties are defined in main stylesheet

## Future Enhancements

Possible additions you might consider:
- RSS feed for new items
- Search functionality
- Categories/tags beyond the basic types
- Image zoom/lightbox functionality
- Admin interface for easier item management
- Integration with payment systems (if desired)

---

Enjoy your weird items store! 🎉
