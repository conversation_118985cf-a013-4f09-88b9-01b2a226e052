# Store System Troubleshooting Guide

## 🎯 Quick Fixes for Common Store Issues

### Issue 1: Images Not Showing

**Problem**: Product images don't display on store pages

**Solution**: Check your image path format in `data/store-items.json`

✅ **CORRECT Format**:
```json
{
  "images": ["product.jpg"]
}
```

❌ **WRONG Format**:
```json
{
  "images": ["img/store/product.jpg"]
}
```

**Why**: The template automatically adds `img/store/` to the path. If you include it in the JSON, you'll get `img/store/img/store/product.jpg` which doesn't exist!

**Steps to Fix**:
1. Open `data/store-items.json`
2. Find the `images` array for each item
3. Remove any `img/store/` prefix from filenames
4. Keep only the filename: `"product.jpg"`
5. Run `php generate-store-pages.php`
6. Test in browser

### Issue 2: Error About Missing weight_lbs Field

**Problem**: PHP error when a product doesn't have `weight_lbs` field

**Solution**: The template has been fixed to handle optional fields

**Optional Fields** (you can omit these):
- `weight_lbs` - Product weight
- `condition` - Item condition
- `acquisition_story` - Story about the item

**Required Fields** (must include these):
- `id` - Unique identifier
- `slug` - URL-friendly name
- `title` - Product name
- `description` - Product description
- `price` - Price (number or string like "Free")
- `status` - "Available", "Sold", or "Wishlist"
- `type` - "sale", "giveaway", or "wishlist"
- `images` - Array of image filenames
- `date_listed` - Date in YYYY-MM-DD format

**Example with Optional Fields Omitted**:
```json
{
  "id": "simple-item",
  "slug": "simple-product",
  "title": "Simple Product",
  "description": "A basic product listing",
  "price": 10.00,
  "status": "Available",
  "type": "sale",
  "images": ["simple.jpg"],
  "date_listed": "2025-01-15"
}
```

### Issue 3: Empty Image Strings

**Problem**: Some products have `"images": [""]` with empty strings

**Solution**: This is now handled gracefully! Empty image strings will be skipped and a placeholder will show.

**What happens**:
- `"images": [""]` → Shows "📦 Image Coming Soon" placeholder
- `"images": []` → Shows "📦 Image Coming Soon" placeholder
- `"images": ["product.jpg"]` → Shows the image
- `"images": ["img1.jpg", "", "img2.jpg"]` → Shows img1 and img2, skips empty string

## 🔧 Store Data Structure Reference

### Complete Example with All Fields

```json
{
  "id": "complete-example",
  "slug": "complete-product",
  "title": "Complete Product Example",
  "description": "This shows all possible fields",
  "price": 25.00,
  "status": "Available",
  "type": "sale",
  "images": ["product1.jpg", "product2.jpg"],
  "condition": "Like new",
  "weight_lbs": 2.5,
  "date_listed": "2025-01-15",
  "acquisition_story": "Found this at a yard sale and knew it was special."
}
```

### Minimal Example (Required Fields Only)

```json
{
  "id": "minimal-example",
  "slug": "minimal-product",
  "title": "Minimal Product",
  "description": "Just the basics",
  "price": 10.00,
  "status": "Available",
  "type": "sale",
  "images": ["minimal.jpg"],
  "date_listed": "2025-01-15"
}
```

### Price Variations

```json
// Numeric price
"price": 25.00

// Free item
"price": "Free"

// Trade or price
"price": "Trade or $15"

// Under a certain amount
"price": "Under $25"

// Make an offer
"price": "Make an offer"
```

### Status Options

```json
"status": "Available"   // Item is available
"status": "Sold"        // Item has been sold
"status": "Wishlist"    // You're looking for this item
```

### Type Options

```json
"type": "sale"      // Item for sale
"type": "giveaway"  // Free item
"type": "wishlist"  // Item you want
```

## 🐛 Debugging Steps

### Step 1: Validate Your JSON

1. Copy your `store-items.json` content
2. Go to https://jsonlint.com/
3. Paste and validate
4. Fix any syntax errors (missing commas, quotes, etc.)

### Step 2: Check Image Files

1. Open `img/store/` directory
2. Verify image files exist
3. Check filenames match exactly (case-sensitive!)
4. Ensure files are valid images (JPG, PNG, etc.)

### Step 3: Regenerate Store Pages

```bash
php generate-store-pages.php
```

This will:
- Read `data/store-items.json`
- Create/update all product pages in `store/`
- Show summary of what was generated

### Step 4: Test in Browser

1. Navigate to `http://localhost/your-path/store.php`
2. Check that all items appear
3. Click on an item to view its detail page
4. Verify images load correctly
5. Check that all metadata displays properly

## 📝 Common Mistakes

### Mistake 1: Including Path in Images
```json
❌ "images": ["img/store/product.jpg"]
✅ "images": ["product.jpg"]
```

### Mistake 2: Forgetting to Regenerate
```
Edit JSON → ❌ Test immediately
Edit JSON → ✅ Run php generate-store-pages.php → Test
```

### Mistake 3: Case-Sensitive Filenames
```json
JSON: "images": ["Product.jpg"]
File: img/store/product.jpg
Result: ❌ Image won't load (case mismatch)

JSON: "images": ["product.jpg"]
File: img/store/product.jpg
Result: ✅ Image loads correctly
```

### Mistake 4: Missing Commas in JSON
```json
❌ Wrong:
{
  "title": "Product"
  "price": 10.00
}

✅ Correct:
{
  "title": "Product",
  "price": 10.00
}
```

## 🎯 Quick Checklist

Before running `php generate-store-pages.php`:

- [ ] JSON is valid (use jsonlint.com)
- [ ] Image paths are just filenames (no `img/store/`)
- [ ] All required fields are present
- [ ] Image files exist in `img/store/`
- [ ] Filenames match exactly (case-sensitive)
- [ ] No trailing commas in JSON
- [ ] Dates are in YYYY-MM-DD format

After running `php generate-store-pages.php`:

- [ ] Check terminal output for errors
- [ ] Verify files created in `store/` directory
- [ ] Test main store page in browser
- [ ] Test individual product pages
- [ ] Verify images load correctly
- [ ] Check that optional fields display when present

## 🆘 Still Having Issues?

### Check the Template

The template file is: `store/item-template.php`

This file is copied for each product. If you need to customize how products display, edit this file and regenerate.

### Check the Main Store Page

The main store listing is: `store.php`

This displays all products in a grid with filtering.

### Check Your Paths

Make sure you're running commands from the correct directory:
```bash
cd "c:\xampp2\htdocs\webtech\coursework\Aprils Apple Chips HTML\aachips\public\obsidian-quartz"
php generate-store-pages.php
```

### Enable Error Reporting

Add to the top of `generate-store-pages.php`:
```php
<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

This will show any PHP errors that occur during generation.

## 💡 Pro Tips

1. **Keep a backup** of `store-items.json` before making major changes
2. **Use a JSON editor** with syntax highlighting to avoid errors
3. **Test with one item first** before adding many products
4. **Use descriptive slugs** for better URLs (e.g., `vintage-typewriter` not `item1`)
5. **Optimize images** before uploading (resize to reasonable dimensions)
6. **Use consistent naming** for image files (e.g., `product-name-1.jpg`, `product-name-2.jpg`)

## 📚 Related Documentation

- Full store guide: `STORE_README.md`
- Quick commands: `CHEATSHEET.md`
- Getting started: `GETTING_STARTED_AFTER_BREAK.md`
- All documentation: `DOCUMENTATION_INDEX.md`

---

**Remember**: 
1. Edit `data/store-items.json`
2. Run `php generate-store-pages.php`
3. Test in browser
4. Repeat as needed!

