<?php
// Individual item page template
// This file should be copied and renamed for each item

// Load configuration
$config = require_once '../config.php';
require_once '../path-helper.php';

// Initialize paths
$paths = initPaths($config, __FILE__);

// Get item slug from filename
$current_file = basename($_SERVER['PHP_SELF'], '.php');
$item_slug = $current_file;

// Load store items
$store_items_file = $paths['data_path'] . 'store-items.json';
$item_data = null;

if (file_exists($store_items_file)) {
    $json_content = file_get_contents($store_items_file);
    $store_data = json_decode($json_content, true);
    $items = $store_data['items'] ?? [];
    
    // Find the item by slug
    foreach ($items as $item) {
        if ($item['slug'] === $item_slug) {
            $item_data = $item;
            break;
        }
    }
}

// If item not found, redirect to store
if (!$item_data) {
    header('Location: ../store.php');
    exit;
}

// Page variables
$page_title = $item_data['title'] . ' - Weird Items Store - A. A. Chips';
$meta_description = $item_data['description'];
$meta_keywords = 'A. A. Chips, store, ' . $item_data['title'] . ', ' . $item_data['type'];
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];

// Post metadata for template compatibility
$post_data = array(
    'title' => $item_data['title'],
    'author' => 'A. A. Chips',
    'date' => $item_data['date_listed'],
    'excerpt' => $item_data['description'],
    'tags' => array('store', $item_data['type']),
    'source_file' => 'store/' . $item_slug . '.php',
);

// Build breadcrumb
$breadcrumb = [
    ['title' => 'Home', 'url' => $base_url . 'index.php'],
    ['title' => 'Store', 'url' => $base_url . 'store.php'],
    ['title' => $item_data['title']]
];

// Build content
ob_start();
?>

<div class="item-detail-container">
    <div class="item-header">
        <div class="item-status-badges">
            <?php if ($item_data['type'] === 'wishlist'): ?>
                <span class="status-badge wishlist">🌟 WISHLIST</span>
            <?php elseif ($item_data['type'] === 'giveaway'): ?>
                <span class="status-badge giveaway">🎁 FREE</span>
            <?php endif; ?>
            
            <?php if ($item_data['status'] === 'Sold'): ?>
                <span class="status-badge sold">☠️ SOLD</span>
            <?php endif; ?>
        </div>
        
        <h1><?php echo htmlspecialchars($item_data['title']); ?></h1>
    </div>
    
    <div class="item-content">
        <div class="item-images">
            <?php if (!empty($item_data['images'])): ?>
                <?php foreach ($item_data['images'] as $index => $image): ?>
                    <?php if (!empty($image)): // Skip empty image strings ?>
                        <img src="<?php echo $base_url . 'img/store/' . $image; ?>"
                             alt="<?php echo htmlspecialchars($item_data['title']); ?>"
                             class="item-image <?php echo $index === 0 ? 'main-image' : 'thumbnail'; ?>"
                             onerror="this.style.display='none';">
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="item-image placeholder main-image">📦 Image Coming Soon</div>
            <?php endif; ?>
        </div>
        
        <div class="item-details">
            <div class="item-description">
                <p><?php echo nl2br(htmlspecialchars($item_data['description'])); ?></p>
            </div>
            
            <?php if (isset($item_data['price']) && $item_data['price'] !== null && $item_data['price'] !== ''): ?>
                <div class="item-price <?php echo ($item_data['status'] ?? '') === 'Sold' ? 'sold' : ''; ?>">
                    <?php
                    if (is_numeric($item_data['price'])) {
                        echo '$' . number_format($item_data['price'], 2);
                    } else {
                        echo htmlspecialchars($item_data['price']);
                    }
                    ?>
                </div>
            <?php endif; ?>
            
            <div class="item-metadata">
                <?php if (!empty($item_data['condition'])): ?>
                    <div class="metadata-item">
                        <strong>Condition:</strong> <?php echo htmlspecialchars($item_data['condition']); ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($item_data['weight_lbs']) && $item_data['weight_lbs'] !== null): ?>
                    <div class="metadata-item">
                        <strong>Weight:</strong> <?php echo $item_data['weight_lbs']; ?> lbs
                    </div>
                <?php endif; ?>

                <?php if (!empty($item_data['date_listed'])): ?>
                    <div class="metadata-item">
                        <strong>Listed:</strong> <?php echo date('F j, Y', strtotime($item_data['date_listed'])); ?>
                    </div>
                <?php endif; ?>
            </div>

            <?php if (!empty($item_data['acquisition_story'])): ?>
                <div class="acquisition-story">
                    <h3>The Story</h3>
                    <p><?php echo nl2br(htmlspecialchars($item_data['acquisition_story'])); ?></p>
                </div>
            <?php endif; ?>
            
            <?php if ($item_data['status'] === 'Available'): ?>
                <div class="contact-section">
                    <a href="mailto:<EMAIL>?subject=Interest in <?php echo urlencode($item_data['title']); ?>&body=Hi! I'm interested in the <?php echo urlencode($item_data['title']); ?>. <?php echo $item_data['type'] === 'wishlist' ? 'Can you help me find this item?' : 'Is it still available?'; ?>" 
                       class="contact-btn">
                        <?php echo $item_data['type'] === 'wishlist' ? 'Help me find this!' : 'Message me about this item'; ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="navigation-links">
        <a href="<?php echo $base_url; ?>store.php" class="back-to-store">← Back to Store</a>
    </div>
</div>



<script>
// Image gallery functionality
document.addEventListener('DOMContentLoaded', function() {
    const mainImage = document.querySelector('.main-image');
    const thumbnails = document.querySelectorAll('.thumbnail');
    
    thumbnails.forEach(thumb => {
        thumb.addEventListener('click', function() {
            if (mainImage && this.src) {
                const tempSrc = mainImage.src;
                mainImage.src = this.src;
                this.src = tempSrc;
            }
        });
    });
});
</script>

<?php
$content = ob_get_clean();

// Add store-specific CSS
$additional_css = '<link rel="stylesheet" href="' . ($css_path ?? '../css/') . 'store.css?v=' . time() . '">';

// Include the template
include '../page template.htm';
?>
