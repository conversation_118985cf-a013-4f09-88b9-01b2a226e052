Weird Items Store/Wishlist - Technical Brief
Goal: A fun, low-maintenance system to list odd items (for sale/giveaway) and wishlist items, with manual order handling.

1. Core Features
Item Listings (Sale/Giveaway/Wishlist):

Unique URL per item (e.g., aachips.co/obsidian/store/giant-stuffed-axolotl)

Photo gallery (1+ images)

Title, description, price (or "Free" / "Make Offer"), status ("Available", "Wishlist", "Sold")

Metadata: Condition, weight (for shipping estimates), acquisition story (optional humor)

Manual Order Flow:

"Interested? Message me" button → Pre-filled email/contact form link (e.g., mailto:<EMAIL>?subject=Interest in [ITEM]).

No cart/payment processing (handled via Venmo/PayPal offsite).

Wishlist Mode:

Tag items as "Wishlist" with desired price/free.

Optional "Help me find this!" call-to-action.

2. Technical Implementation
A. Data Structure (JSON)
json
{
  "items": [
    {
      "id": "axolotl-123",
      "slug": "giant-stuffed-axolotl",
      "title": "Giant Stuffed Axolotl (<PERSON><PERSON>ly Singed)",
      "description": "Rescued from a yard sale after a failed 'release it into the wild' experiment.",
      "price": 15.00,
      "status": "Available", // or "Wishlist", "Sold"
      "type": "For Sale", // or "Giveaway", "Wishlist"
      "images": ["axolotl-1.jpg", "axolotl-2.jpg"],
      "condition": "Pre-loved (smells faintly of campfire)",
      "weight_lbs": 2.5,
      "date_listed": "2024-05-20"
    }
  ]
}
Storage: Single JSON file in your Obsidian vault (synced via Git/GitHub).

Manual Updates: Edit JSON directly or use a simple CMS (e.g., Obsidian Frontmatter).

B. Static Site Integration
Generation:

Use Eleventy (11ty) or a simple JavaScript script to:

Read items.json.

Generate individual HTML pages for each item (for unique URLs).

Create a filterable store index (e.g., filter by "For Sale" vs. "Wishlist").

Styling: Match your existing site’s dark theme with playful accents (e.g., sold items crossed out with a "☠️" icon).

C. Hosting
Keep existing GitHub Pages setup. JSON + generated HTML files deploy automatically.

3. User Flow Example
Browse: User visits aachips.co/obsidian/store → sees grid of weird items.

Click Item: Views details page with story/photos.

Purchase:

Clicks "Message me" → opens email with pre-filled item name.

You reply with shipping cost + payment link (Venmo/PayPal).

Update: Mark item as "Sold" in JSON → rebuilds site.

4. Optional Enhancements
RSS Feed: For "New Items" alerts.

Sorting: By price, weirdness, or acquisition date.

Hidden Easter Eggs: For items with funny backstories (e.g., hover effects revealing "This item may be haunted").