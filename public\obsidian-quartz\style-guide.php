<a href="your-page.php" class="adventure-link">Your Text</a>
<a href="your-page.php" class="story-path">Your Text</a>
<a href="your-page.php" class="mystical-link">Your Text</a>

For Quest Cards:
<a href="your-page.php" class="quest-card">
  <div class="quest-title">Your Title</div>
  <div class="quest-description">Your description</div>
</a> 
    

<figure class="polaroid-frame">
<!-- vintage-frame, minimal-frame, sketch-frame -->
  <img src="your-image.jpg" alt="Description" data-caption="Modal caption">
  <figcaption>Your caption text</figcaption>
</figure>

          

            <?php
            $category = 'blog';
            $posts = get_posts_in_category($category);
            foreach ($posts as $post) {
            ?>
            <div class="post-card">
                <a href="<?php echo $post->permalink; ?>" class="post-card-link">
                    <div class="post-card-thumbnail">
                        <img src="<?php echo get_the_post_thumbnail_url($post->ID); ?>" alt="<?php echo $post->post_title; ?>" class="post-thumb-img">
                    </div>
                    <div class="post-card-content">
                        <h3 class="post-card-title"><?php echo $post->post_title; ?></h3>
                        <p class="post-card-excerpt"><?php echo wp_trim_words($post->post_content, 20); ?></p>
                        <div class="post-card-meta">
                            <span class="post-author">By <?php echo get_the_author_meta('display_name', $post->post_author); ?></span>
                        </div>
                        <div class="post-card-tags">
                            <?php
                            $post_tags = wp_get_post_tags($post->ID);
                            foreach ($post_tags as $tag) {
                                echo '<span class="tag">' . $tag->name . '</span>';
                            }
                            ?>
                        </div>
                    </div>
                </a>
            </div>
        <?php } ?>

<div class="post-grid">
        <?php foreach ($content_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post['thumbnail']) && $post['thumbnail']): ?>
                        <?php if (preg_match('/^https?:\/\//', $post['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php else: ?>
                            <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php endif; ?>
                    <?php else: ?>
                        <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                        <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img placeholder">
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars(is_string($post['excerpt']) ? $post['excerpt'] : ''); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author'] && is_string($post['author'])): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo is_string($post['date']) ? htmlspecialchars($post['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>

