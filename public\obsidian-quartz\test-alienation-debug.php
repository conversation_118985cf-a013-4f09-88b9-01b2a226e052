<?php
/**
 * Debug alienation page specifically
 */

echo "<h1>Debug Alienation Page</h1>";

// Test if we can load the alienation index and see what happens
echo "<h2>Loading Alienation Index</h2>";

try {
    // Capture all output
    ob_start();
    include 'content/alienation/index.php';
    $output = ob_get_clean();
    
    echo "<h3>Output Analysis:</h3>";
    echo "<p>Total output length: " . strlen($output) . " characters</p>";
    
    if (strpos($output, 'No posts found') !== false) {
        echo "<p>❌ Contains 'No posts found' message</p>";
    } else {
        echo "<p>✅ Does not contain 'No posts found' message</p>";
    }
    
    if (strpos($output, 'post-card') !== false) {
        $cardCount = substr_count($output, 'post-card');
        echo "<p>✅ Contains $cardCount post cards</p>";
    } else {
        echo "<p>❌ No post cards found</p>";
    }
    
    if (strpos($output, 'Debug: Found') !== false) {
        preg_match('/Debug: Found (\d+) posts/', $output, $matches);
        if ($matches) {
            echo "<p>🔍 Debug info: Found " . $matches[1] . " posts</p>";
        }
    }
    
    // Look for the post grid section
    if (strpos($output, 'post-grid') !== false) {
        echo "<p>✅ Contains post-grid div</p>";
    } else {
        echo "<p>❌ No post-grid div found</p>";
    }
    
    // Show a snippet of the output around where posts should be
    $gridPos = strpos($output, 'post-grid');
    if ($gridPos !== false) {
        $snippet = substr($output, max(0, $gridPos - 100), 300);
        echo "<h4>Output snippet around post-grid:</h4>";
        echo "<pre>" . htmlspecialchars($snippet) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error loading alienation page: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>Direct Directory Test</h2>";

// Test the directory scanning directly
$alienationDir = __DIR__ . '/content/alienation';
$posts = glob($alienationDir . '/*.php');
$content_posts = [];

foreach ($posts as $postFile) {
    $filename = basename($postFile, '.php');
    if ($filename !== 'index' && $filename !== 'contents' && $filename !== 'notes') {
        $content_posts[] = [
            'title' => ucwords(str_replace('-', ' ', $filename)),
            'url' => $filename . '.php',
            'excerpt' => 'Alienation and recovery content.',
            'date' => date('Y-m-d'),
            'author' => 'A. A. Chips',
            'thumbnail' => null
        ];
    }
}

echo "<p>Direct scan found: " . count($content_posts) . " posts</p>";
echo "<p>!empty(\$content_posts): " . (!empty($content_posts) ? 'TRUE' : 'FALSE') . "</p>";

if (!empty($content_posts)) {
    echo "<h4>Sample posts:</h4>";
    echo "<ul>";
    foreach (array_slice($content_posts, 0, 3) as $post) {
        echo "<li>" . htmlspecialchars($post['title']) . "</li>";
    }
    echo "</ul>";
}
?>
