<?php
/**
 * Test category filtering logic for all categories
 */

echo "<h1>Test Category Filtering - All Categories</h1>";

// Test categories to check
$categories = [
    'humor', 'alienation', 'climate', 'inspiration',
    'journal', 'judaism', 'kitchen', 'ptsd-myth', 'street', 'writings'
];

echo "<h2>Testing All Category Index Pages</h2>";

foreach ($categories as $category) {
    echo "<h3>Testing $category category:</h3>";

    $indexFile = "content/$category/index.php";
    if (file_exists($indexFile)) {
        // Capture output from category index
        ob_start();
        include $indexFile;
        $output = ob_get_clean();

        if (strpos($output, 'No posts found') !== false) {
            echo "<p>❌ $category: Still showing 'No posts found'</p>";
        } elseif (strpos($output, 'post-card') !== false) {
            $cardCount = substr_count($output, 'post-card');
            echo "<p>✅ $category: Found $cardCount post cards!</p>";
        } else {
            echo "<p>⚠️ $category: Unclear result - check manually</p>";
        }
    } else {
        echo "<p>❌ $category: Index file not found</p>";
    }
}

echo "<h2>Test URL Pattern Matching</h2>";

// Test the exact filtering logic
$test_posts = [
    ['title' => 'Humor Post', 'url' => 'content\humor\test.php'],
    ['title' => 'Climate Post', 'url' => 'content\climate\test.php'],
    ['title' => 'Journal Post', 'url' => 'content\journal\test.php'],
    ['title' => 'Other Post', 'url' => 'content\other\test.php']
];

foreach ($categories as $category) {
    echo "<h4>Testing $category filter:</h4>";
    $matches = 0;

    foreach ($test_posts as $post) {
        if (strpos($post['url'], "content\\$category\\") !== false) {
            echo "<p>✅ " . htmlspecialchars($post['title']) . " matches $category</p>";
            $matches++;
        }
    }

    if ($matches === 0) {
        echo "<p>⚠️ No test posts matched $category filter</p>";
    }
}

echo "<h2>Summary</h2>";
echo "<p>All category index files have been updated with the correct URL filtering logic.</p>";
echo "<p>The issue was that the filtering was looking for double backslashes (content\\\\category\\\\) but the actual URLs use single backslashes (content\\category\\).</p>";
echo "<p>Fixed categories: " . implode(', ', $categories) . "</p>";
?>
