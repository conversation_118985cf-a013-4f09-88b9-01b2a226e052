<?php
/**
 * Test database connection and visitor counter tables
 */

try {
    require_once 'comments/database.php';
    $db = CommentDatabase::getInstance();
    echo 'Database connection successful!' . PHP_EOL;
    
    // Check if visitor counter tables exist
    $pdo = $db->getPDO();
    $tables = ['aachipsc_blog_page_visits', 'aachipsc_blog_page_stats', 'aachipsc_blog_site_stats', 'aachipsc_blog_visitor_sessions'];
    
    foreach ($tables as $table) {
        $stmt = $pdo->prepare('SHOW TABLES LIKE ?');
        $stmt->execute([$table]);
        if ($stmt->rowCount() > 0) {
            echo "Table $table exists" . PHP_EOL;
        } else {
            echo "Table $table MISSING" . PHP_EOL;
        }
    }
    
    // Test visitor counter initialization
    echo "\nTesting visitor counter..." . PHP_EOL;
    require_once 'visitor-counter/VisitorCounter.php';
    $counter = new VisitorCounter();
    echo "Visitor counter initialized successfully!" . PHP_EOL;
    
    // Test getting stats
    $stats = $counter->getSiteStats();
    echo "Site stats retrieved: " . json_encode($stats) . PHP_EOL;
    
} catch (Exception $e) {
    echo 'Error: ' . $e->getMessage() . PHP_EOL;
    echo 'Stack trace: ' . $e->getTraceAsString() . PHP_EOL;
}
