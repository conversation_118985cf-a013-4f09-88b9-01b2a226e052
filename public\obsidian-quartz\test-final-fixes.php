<?php
/**
 * Test final fixes for alienation and URL issues
 */

echo "<h1>Test Final Fixes</h1>";

echo "<h2>1. Testing Alienation Category</h2>";

try {
    ob_start();
    include 'content/alienation/index.php';
    $output = ob_get_clean();
    
    if (strpos($output, 'No posts found') !== false) {
        echo "<p>❌ Alienation still shows 'No posts found'</p>";
    } elseif (strpos($output, 'post-card') !== false) {
        $cardCount = substr_count($output, 'post-card');
        echo "<p>✅ Alienation shows $cardCount post cards!</p>";
    } else {
        echo "<p>⚠️ Alienation unclear result</p>";
    }
    
    // Check for debug info
    if (strpos($output, 'Debug: Found') !== false) {
        preg_match('/Debug: Found (\d+) posts/', $output, $matches);
        if ($matches) {
            echo "<p>🔍 Debug: Found " . $matches[1] . " posts in directory</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error testing alienation: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>2. Testing URL Fixes</h2>";

// Test URL conversion logic
$testUrls = [
    'content\humor\test-post.php',
    'content\journal\asylum-run-dream.php',
    'content\climate\climate-action.php'
];

foreach ($testUrls as $testUrl) {
    echo "<h4>Testing URL: " . htmlspecialchars($testUrl) . "</h4>";
    
    // Test humor URL fix
    if (strpos($testUrl, 'content\humor\\') === 0) {
        $fixedUrl = '../../' . str_replace('\\', '/', $testUrl);
        echo "<p>✅ Humor URL: " . htmlspecialchars($fixedUrl) . "</p>";
    }
    
    // Test journal URL fix  
    if (strpos($testUrl, 'content\journal\\') === 0) {
        $fixedUrl = '../../' . str_replace('\\', '/', $testUrl);
        echo "<p>✅ Journal URL: " . htmlspecialchars($fixedUrl) . "</p>";
    }
    
    // Test climate URL fix
    if (strpos($testUrl, 'content\climate\\') === 0) {
        $fixedUrl = '../../' . str_replace('\\', '/', $testUrl);
        echo "<p>✅ Climate URL: " . htmlspecialchars($fixedUrl) . "</p>";
    }
}

echo "<h2>3. Testing Category Pages</h2>";

$categories = ['humor', 'journal', 'climate', 'alienation'];

foreach ($categories as $category) {
    echo "<h4>Testing $category:</h4>";
    
    $indexFile = "content/$category/index.php";
    if (file_exists($indexFile)) {
        try {
            ob_start();
            include $indexFile;
            $output = ob_get_clean();
            
            if (strpos($output, 'No posts found') !== false) {
                echo "<p>❌ $category: Shows 'No posts found'</p>";
            } elseif (strpos($output, 'post-card') !== false) {
                $cardCount = substr_count($output, 'post-card');
                echo "<p>✅ $category: Shows $cardCount post cards</p>";
            } else {
                echo "<p>⚠️ $category: Unclear result</p>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ $category: Error - " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    } else {
        echo "<p>❌ $category: Index file not found</p>";
    }
}

echo "<h2>Summary of Fixes</h2>";
echo "<ul>";
echo "<li><strong>Alienation Fix:</strong> Moved PHP directory scanning code outside the \$post_content string so it executes properly</li>";
echo "<li><strong>URL Fix:</strong> Changed from '../filename.php' to '../../content/category/filename.php' to maintain proper category paths</li>";
echo "<li><strong>Structure Fix:</strong> Ensured post grids appear after introductory content in all category pages</li>";
echo "</ul>";

echo "<h3>Expected Results:</h3>";
echo "<ul>";
echo "<li>✅ Alienation category should now show 19 posts</li>";
echo "<li>✅ Journal links should work as: ../../content/journal/asylum-run-dream.php</li>";
echo "<li>✅ All category pages should show posts after introduction text</li>";
echo "<li>✅ No more 404 errors when clicking post links</li>";
echo "</ul>";
?>
