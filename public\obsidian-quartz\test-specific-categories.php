<?php
/**
 * Test specific category issues: Alienation and Climate
 */

echo "<h1>Test Specific Category Issues</h1>";

echo "<h2>1. Testing Alienation Category</h2>";

if (file_exists('content/alienation/index.php')) {
    echo "<h3>Alienation Directory Scan:</h3>";
    
    // Test the directory scanning logic
    $alienationDir = __DIR__ . '/content/alienation';
    $posts = glob($alienationDir . '/*.php');
    $content_posts = [];
    
    foreach ($posts as $postFile) {
        $filename = basename($postFile, '.php');
        if ($filename !== 'index' && $filename !== 'contents' && $filename !== 'notes') {
            $content_posts[] = [
                'title' => ucwords(str_replace('-', ' ', $filename)),
                'url' => $filename . '.php',
                'excerpt' => 'Alienation and recovery content.',
                'date' => date('Y-m-d'),
                'author' => 'A. A. Chips',
                'thumbnail' => null
            ];
        }
    }
    
    echo "<p>Found " . count($posts) . " total PHP files</p>";
    echo "<p>Found " . count($content_posts) . " valid post files</p>";
    
    if (count($content_posts) > 0) {
        echo "<h4>Sample posts found:</h4>";
        echo "<ul>";
        foreach (array_slice($content_posts, 0, 5) as $post) {
            echo "<li>" . htmlspecialchars($post['title']) . " (" . htmlspecialchars($post['url']) . ")</li>";
        }
        echo "</ul>";
    }
    
    // Test the actual page
    echo "<h3>Alienation Page Test:</h3>";
    ob_start();
    include 'content/alienation/index.php';
    $output = ob_get_clean();
    
    if (strpos($output, 'No posts found') !== false) {
        echo "<p>❌ Still showing 'No posts found'</p>";
    } elseif (strpos($output, 'post-card') !== false) {
        $cardCount = substr_count($output, 'post-card');
        echo "<p>✅ Found $cardCount post cards!</p>";
    } else {
        echo "<p>⚠️ Unclear result - check manually</p>";
    }
} else {
    echo "<p>❌ Alienation index file not found</p>";
}

echo "<h2>2. Testing Climate Category</h2>";

if (file_exists('content/climate/index.php')) {
    echo "<h3>Climate Page Test:</h3>";
    
    // Test if the page loads without errors
    ob_start();
    try {
        include 'content/climate/index.php';
        $output = ob_get_clean();
        
        // Check if posts appear after content
        $contentPos = strpos($output, 'Climate Action and Environmental Advocacy');
        $postGridPos = strpos($output, 'post-grid');
        
        if ($contentPos !== false && $postGridPos !== false) {
            if ($postGridPos > $contentPos) {
                echo "<p>✅ Post grid appears after introductory content!</p>";
            } else {
                echo "<p>❌ Post grid appears before introductory content</p>";
            }
        } else {
            echo "<p>⚠️ Could not find content or post grid positions</p>";
        }
        
        if (strpos($output, 'No posts found') !== false) {
            echo "<p>❌ Still showing 'No posts found'</p>";
        } elseif (strpos($output, 'post-card') !== false) {
            $cardCount = substr_count($output, 'post-card');
            echo "<p>✅ Found $cardCount post cards!</p>";
        } else {
            echo "<p>⚠️ No post cards found</p>";
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p>❌ Error loading climate page: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
} else {
    echo "<p>❌ Climate index file not found</p>";
}

echo "<h2>URL Testing</h2>";

// Test URL conversion logic
$testUrls = [
    'content\humor\test-post.php',
    'content\journal\asylum-run-dream.php',
    'content\climate\climate-action.php'
];

foreach ($testUrls as $testUrl) {
    echo "<h4>Testing URL: " . htmlspecialchars($testUrl) . "</h4>";

    // Test humor URL fix
    if (strpos($testUrl, 'content\humor\\') === 0) {
        $fixedUrl = '../' . basename($testUrl);
        echo "<p>✅ Humor URL fixed: " . htmlspecialchars($fixedUrl) . "</p>";
    }

    // Test journal URL fix
    if (strpos($testUrl, 'content\journal\\') === 0) {
        $fixedUrl = '../' . basename($testUrl);
        echo "<p>✅ Journal URL fixed: " . htmlspecialchars($fixedUrl) . "</p>";
    }

    // Test climate URL fix
    if (strpos($testUrl, 'content\climate\\') === 0) {
        $fixedUrl = '../' . basename($testUrl);
        echo "<p>✅ Climate URL fixed: " . htmlspecialchars($fixedUrl) . "</p>";
    }
}

echo "<h2>Summary</h2>";
echo "<p><strong>Alienation Fix:</strong> Moved post grid inside ob_start() buffer and fixed condition logic</p>";
echo "<p><strong>Climate Fix:</strong> Moved post grid to appear after introductory content instead of before</p>";
echo "<p><strong>URL Fix:</strong> Convert paths like 'content\\category\\file.php' to '../file.php' to prevent double paths</p>";
echo "<p><strong>Result:</strong> Category pages should now show posts and links should work without 404 errors</p>";
?>
