<?php
/**
 * Check Visitor Counter Status and Set Count to 3000
 * This script will help you determine which counter system is active
 * and allow you to set the count to 3000
 */

// Start output buffering to capture any errors
ob_start();

echo "<h2>Visitor Counter Status Check</h2>\n";

// Try to load the visitor counter system
try {
    require_once __DIR__ . '/VisitorCounter.php';
    
    echo "<h3>Testing Database Connection...</h3>\n";
    
    try {
        $counter = new VisitorCounter();
        echo "✅ <strong>Database connection successful!</strong><br>\n";
        echo "Using database-based visitor counter.<br>\n";
        
        // Get current stats
        $siteStats = $counter->getSiteStats();
        echo "Current total site visits: " . ($siteStats['total_site_visits'] ?? 0) . "<br>\n";
        
        $counterType = 'database';
        
    } catch (Exception $e) {
        echo "❌ Database connection failed: " . $e->getMessage() . "<br>\n";
        echo "Falling back to file-based counter...<br>\n";
        
        require_once __DIR__ . '/simple-counter.php';
        $counter = new SimpleVisitorCounter();
        echo "✅ <strong>File-based counter loaded!</strong><br>\n";
        
        // Get current stats
        $siteStats = $counter->getSiteStats();
        echo "Current total site visits: " . ($siteStats['total_site_visits'] ?? 0) . "<br>\n";
        
        $counterType = 'file';
    }
    
} catch (Exception $e) {
    echo "❌ Error loading visitor counter: " . $e->getMessage() . "<br>\n";
    exit;
}

echo "<hr>\n";

// Check if we should set the count to 3000
if (isset($_POST['set_count']) && $_POST['set_count'] === '3000') {
    echo "<h3>Setting Visitor Count to 3,000...</h3>\n";
    
    if ($counterType === 'database') {
        try {
            // Access database through comments database connection
            require_once __DIR__ . '/../comments/database.php';
            $db = CommentDatabase::getInstance();
            $pdo = $db->getPDO();
            $config = $db->getConfig();
            $tablePrefix = $config['database']['table_prefix'] ?? 'aachipsc_blog_';

            // Update site stats table
            $sql = "UPDATE {$tablePrefix}site_stats SET stat_value = 3000 WHERE stat_name = 'total_site_visits'";
            $result = $pdo->exec($sql);

            // If no rows were affected, insert the record
            if ($result === 0) {
                $sql = "INSERT INTO {$tablePrefix}site_stats (stat_name, stat_value) VALUES ('total_site_visits', 3000)";
                $pdo->exec($sql);
            }

            echo "✅ <strong>Database updated successfully!</strong><br>\n";
            echo "Total site visits set to 3,000 in database.<br>\n";

        } catch (Exception $e) {
            echo "❌ Error updating database: " . $e->getMessage() . "<br>\n";
        }
        
    } else {
        try {
            // Update file-based counter
            $dataDir = __DIR__ . '/data/';
            $statsFile = $dataDir . 'stats.json';
            
            // Create data directory if it doesn't exist
            if (!is_dir($dataDir)) {
                mkdir($dataDir, 0755, true);
            }
            
            // Load existing stats or create new
            $stats = [];
            if (file_exists($statsFile)) {
                $stats = json_decode(file_get_contents($statsFile), true) ?: [];
            }
            
            // Set the total visits to 3000
            $stats['total_site_visits'] = 3000;
            $stats['total_visits'] = 3000; // Alternative key name
            
            // Save updated stats
            file_put_contents($statsFile, json_encode($stats, JSON_PRETTY_PRINT));
            
            echo "✅ <strong>File-based counter updated successfully!</strong><br>\n";
            echo "Total site visits set to 3,000 in stats.json file.<br>\n";
            
        } catch (Exception $e) {
            echo "❌ Error updating file-based counter: " . $e->getMessage() . "<br>\n";
        }
    }
    
    echo "<hr>\n";
    echo "<h3>Updated Status:</h3>\n";
    
    // Get updated stats
    try {
        $siteStats = $counter->getSiteStats();
        echo "New total site visits: " . ($siteStats['total_site_visits'] ?? 0) . "<br>\n";
    } catch (Exception $e) {
        echo "Error getting updated stats: " . $e->getMessage() . "<br>\n";
    }
}

// Display form to set count
if (!isset($_POST['set_count'])) {
    echo "<h3>Set Visitor Count to 3,000</h3>\n";
    echo "<p>Click the button below to set your visitor count to 3,000:</p>\n";
    echo "<form method='post'>\n";
    echo "<input type='hidden' name='set_count' value='3000'>\n";
    echo "<button type='submit' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Set Count to 3,000</button>\n";
    echo "</form>\n";
}

// Output any buffered content
$output = ob_get_clean();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Visitor Counter Status</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        h2, h3 { color: #333; }
        hr { margin: 20px 0; }
        button:hover { background: #005a87 !important; }
    </style>
</head>
<body>
    <?php echo $output; ?>
</body>
</html>
