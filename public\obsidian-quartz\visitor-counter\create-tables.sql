-- Visitor Counter Database Tables
-- For <PERSON><PERSON> A<PERSON> Chips' Obsidian-Quartz Blog

-- Page visits table - stores individual visit records
CREATE TABLE IF NOT EXISTS `aachipsc_blog_page_visits` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `page_slug` varchar(255) NOT NULL,
    `page_title` varchar(500) DEFAULT NULL,
    `ip_address` varchar(45) NOT NULL,
    `user_agent_hash` varchar(64) NOT NULL,
    `session_id` varchar(128) DEFAULT NULL,
    `visit_date` date NOT NULL,
    `visit_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `is_unique_daily` tinyint(1) DEFAULT 1,
    `is_unique_total` tinyint(1) DEFAULT 1,
    `referrer` varchar(500) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_page_slug` (`page_slug`),
    KEY `idx_visit_date` (`visit_date`),
    KEY `idx_ip_agent` (`ip_address`, `user_agent_hash`),
    KEY `idx_unique_check` (`page_slug`, `ip_address`, `user_agent_hash`, `visit_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Page statistics table - aggregated stats per page
CREATE TABLE IF NOT EXISTS `aachipsc_blog_page_stats` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `page_slug` varchar(255) NOT NULL UNIQUE,
    `page_title` varchar(500) DEFAULT NULL,
    `total_visits` int(11) DEFAULT 0,
    `unique_visits` int(11) DEFAULT 0,
    `today_visits` int(11) DEFAULT 0,
    `today_unique_visits` int(11) DEFAULT 0,
    `first_visit` timestamp NULL DEFAULT NULL,
    `last_visit` timestamp NULL DEFAULT NULL,
    `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_page_slug` (`page_slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Site-wide statistics table
CREATE TABLE IF NOT EXISTS `aachipsc_blog_site_stats` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `stat_name` varchar(100) NOT NULL UNIQUE,
    `stat_value` bigint(20) DEFAULT 0,
    `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_stat_name` (`stat_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Visitor sessions table - tracks visitor sessions
CREATE TABLE IF NOT EXISTS `aachipsc_blog_visitor_sessions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `ip_address` varchar(45) NOT NULL,
    `user_agent_hash` varchar(64) NOT NULL,
    `session_id` varchar(128) DEFAULT NULL,
    `first_visit` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_visit` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `total_page_views` int(11) DEFAULT 1,
    `pages_visited` text DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_visitor` (`ip_address`, `user_agent_hash`, `session_id`),
    KEY `idx_last_visit` (`last_visit`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Initialize site stats with default values
INSERT IGNORE INTO `aachipsc_blog_site_stats` (`stat_name`, `stat_value`) VALUES
('total_site_visits', 0),
('unique_site_visitors', 0),
('total_pages_visited', 0);

-- Stored procedure to update page statistics
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS `UpdatePageStats`(IN page_slug_param VARCHAR(255))
BEGIN
    DECLARE total_visits_count INT DEFAULT 0;
    DECLARE unique_visits_count INT DEFAULT 0;
    DECLARE today_visits_count INT DEFAULT 0;
    DECLARE today_unique_count INT DEFAULT 0;
    DECLARE first_visit_time TIMESTAMP DEFAULT NULL;
    DECLARE last_visit_time TIMESTAMP DEFAULT NULL;
    
    -- Calculate statistics
    SELECT COUNT(*) INTO total_visits_count
    FROM aachipsc_blog_page_visits 
    WHERE page_slug = page_slug_param;
    
    SELECT COUNT(DISTINCT CONCAT(ip_address, user_agent_hash)) INTO unique_visits_count
    FROM aachipsc_blog_page_visits 
    WHERE page_slug = page_slug_param;
    
    SELECT COUNT(*) INTO today_visits_count
    FROM aachipsc_blog_page_visits 
    WHERE page_slug = page_slug_param AND visit_date = CURDATE();
    
    SELECT COUNT(DISTINCT CONCAT(ip_address, user_agent_hash)) INTO today_unique_count
    FROM aachipsc_blog_page_visits 
    WHERE page_slug = page_slug_param AND visit_date = CURDATE();
    
    SELECT MIN(visit_time) INTO first_visit_time
    FROM aachipsc_blog_page_visits 
    WHERE page_slug = page_slug_param;
    
    SELECT MAX(visit_time) INTO last_visit_time
    FROM aachipsc_blog_page_visits 
    WHERE page_slug = page_slug_param;
    
    -- Update or insert page stats
    INSERT INTO aachipsc_blog_page_stats 
        (page_slug, total_visits, unique_visits, today_visits, today_unique_visits, first_visit, last_visit)
    VALUES 
        (page_slug_param, total_visits_count, unique_visits_count, today_visits_count, today_unique_count, first_visit_time, last_visit_time)
    ON DUPLICATE KEY UPDATE
        total_visits = total_visits_count,
        unique_visits = unique_visits_count,
        today_visits = today_visits_count,
        today_unique_visits = today_unique_count,
        first_visit = COALESCE(first_visit, first_visit_time),
        last_visit = last_visit_time;
END$$
DELIMITER ;
