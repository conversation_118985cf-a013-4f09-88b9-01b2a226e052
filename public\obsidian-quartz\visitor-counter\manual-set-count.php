<?php
/**
 * Manual Visitor Count Setter
 * Simple script to set visitor count to 3000
 */

echo "<h2>Manual Visitor Count Setter</h2>\n";

// Method 1: Try Database Update
echo "<h3>Method 1: Database Update</h3>\n";
try {
    require_once __DIR__ . '/../comments/database.php';
    $db = CommentDatabase::getInstance();
    $pdo = $db->getPDO();
    $config = $db->getConfig();
    $tablePrefix = $config['database']['table_prefix'] ?? 'aachipsc_blog_';
    
    echo "✅ Database connection successful!<br>\n";
    
    // Check if table exists
    $sql = "SHOW TABLES LIKE '{$tablePrefix}site_stats'";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "✅ Site stats table exists.<br>\n";
        
        // Update the count
        $sql = "INSERT INTO {$tablePrefix}site_stats (stat_name, stat_value) VALUES ('total_site_visits', 3000) 
                ON DUPLICATE KEY UPDATE stat_value = 3000";
        $pdo->exec($sql);
        
        echo "✅ <strong>Database updated! Visitor count set to 3,000.</strong><br>\n";
        
        // Verify
        $sql = "SELECT stat_value FROM {$tablePrefix}site_stats WHERE stat_name = 'total_site_visits'";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $count = $stmt->fetchColumn();
        echo "Verified count: " . $count . "<br>\n";
        
    } else {
        echo "❌ Site stats table doesn't exist. Creating it...<br>\n";
        
        // Create the table
        $sql = "CREATE TABLE {$tablePrefix}site_stats (
            id int(11) NOT NULL AUTO_INCREMENT,
            stat_name varchar(100) NOT NULL UNIQUE,
            stat_value bigint(20) DEFAULT 0,
            last_updated timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY idx_stat_name (stat_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($sql);
        
        // Insert the count
        $sql = "INSERT INTO {$tablePrefix}site_stats (stat_name, stat_value) VALUES ('total_site_visits', 3000)";
        $pdo->exec($sql);
        
        echo "✅ <strong>Table created and visitor count set to 3,000!</strong><br>\n";
    }
    
} catch (Exception $e) {
    echo "❌ Database method failed: " . $e->getMessage() . "<br>\n";
    echo "<br>Trying file-based method...<br>\n";
    
    // Method 2: File-based Update
    echo "<h3>Method 2: File-based Update</h3>\n";
    try {
        $dataDir = __DIR__ . '/data/';
        $statsFile = $dataDir . 'stats.json';
        
        // Create data directory if it doesn't exist
        if (!is_dir($dataDir)) {
            mkdir($dataDir, 0755, true);
            echo "✅ Created data directory.<br>\n";
        }
        
        // Create or update stats file
        $stats = [
            'total_site_visits' => 3000,
            'total_visits' => 3000,
            'unique_visitors' => 2500,
            'pages' => []
        ];
        
        file_put_contents($statsFile, json_encode($stats, JSON_PRETTY_PRINT));
        echo "✅ <strong>File-based counter updated! Visitor count set to 3,000.</strong><br>\n";
        echo "Stats file created at: " . $statsFile . "<br>\n";
        
    } catch (Exception $e) {
        echo "❌ File-based method also failed: " . $e->getMessage() . "<br>\n";
    }
}

echo "<hr>\n";
echo "<h3>Next Steps:</h3>\n";
echo "<ol>\n";
echo "<li>Refresh your website to see if the visitor count now shows 3,000</li>\n";
echo "<li>If you're on your webhost, the database method should work</li>\n";
echo "<li>If you're testing locally, the file-based method should work</li>\n";
echo "<li>You can delete this script after confirming it worked</li>\n";
echo "</ol>\n";

echo "<p><strong>Note:</strong> The visitor count will continue to increment from 3,000 as new visitors arrive.</p>\n";
?>
