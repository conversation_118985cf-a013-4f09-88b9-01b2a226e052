-- <PERSON><PERSON>t to Set Visitor Count to 3,000
-- Run this in your database (either locally in phpMyAdmin or on your webhost)

-- First, ensure the site_stats table exists and has the required structure
CREATE TABLE IF NOT EXISTS `aachipsc_blog_site_stats` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `stat_name` varchar(100) NOT NULL UNIQUE,
    `stat_value` bigint(20) DEFAULT 0,
    `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_stat_name` (`stat_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Set the total site visits to 3000
-- This will either update the existing record or insert a new one
INSERT INTO `aachipsc_blog_site_stats` (`stat_name`, `stat_value`) 
VALUES ('total_site_visits', 3000)
ON DUPLICATE KEY UPDATE 
    `stat_value` = 3000,
    `last_updated` = CURRENT_TIMESTAMP;

-- Also set other related stats if they don't exist
INSERT IGNORE INTO `aachipsc_blog_site_stats` (`stat_name`, `stat_value`) VALUES
('unique_site_visitors', 2500),  -- Estimate: assuming 2500 unique visitors out of 3000 total
('total_pages_visited', 0);      -- This will be calculated automatically

-- Verify the update
SELECT * FROM `aachipsc_blog_site_stats` WHERE `stat_name` = 'total_site_visits';
