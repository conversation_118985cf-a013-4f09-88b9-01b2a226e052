<?php
/**
 * Simple File-Based Visitor Counter
 * For A. A. Chips' Obsidian-Quartz Blog
 * 
 * A fallback visitor counter that uses file storage instead of database
 */

class SimpleVisitorCounter {
    private $dataDir;
    private $visitsFile;
    private $statsFile;
    
    public function __construct() {
        $this->dataDir = __DIR__ . '/data/';
        $this->visitsFile = $this->dataDir . 'visits.json';
        $this->statsFile = $this->dataDir . 'stats.json';
        
        // Create data directory if it doesn't exist
        if (!is_dir($this->dataDir)) {
            mkdir($this->dataDir, 0755, true);
        }
        
        // Initialize files if they don't exist
        if (!file_exists($this->visitsFile)) {
            file_put_contents($this->visitsFile, json_encode([]));
        }
        
        if (!file_exists($this->statsFile)) {
            $defaultStats = [
                'total_visits' => 0,
                'unique_visitors' => 0,
                'pages' => []
            ];
            file_put_contents($this->statsFile, json_encode($defaultStats));
        }
    }
    
    /**
     * Record a page visit
     */
    public function recordVisit($pageSlug, $pageTitle = null) {
        try {
            $ipAddress = $this->getClientIP();
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $userAgentHash = hash('sha256', $userAgent);
            $visitDate = date('Y-m-d');
            $visitTime = time();
            
            // Check if this is a duplicate visit (same IP, user agent, page, and date)
            if ($this->isDuplicateVisit($pageSlug, $ipAddress, $userAgentHash, $visitDate)) {
                return false;
            }
            
            // Record the visit
            $this->addVisit($pageSlug, $pageTitle, $ipAddress, $userAgentHash, $visitDate, $visitTime);
            
            // Update statistics
            $this->updateStats($pageSlug, $pageTitle);
            
            return true;
            
        } catch (Exception $e) {
            error_log("Simple visitor counter error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get visitor statistics for a specific page
     */
    public function getPageStats($pageSlug) {
        $stats = $this->loadStats();
        
        if (isset($stats['pages'][$pageSlug])) {
            return $stats['pages'][$pageSlug];
        }
        
        return [
            'page_slug' => $pageSlug,
            'total_visits' => 0,
            'unique_visits' => 0,
            'today_visits' => 0,
            'today_unique_visits' => 0,
            'last_visit' => null,
            'first_visit' => null
        ];
    }
    
    /**
     * Get site-wide visitor statistics
     */
    public function getSiteStats() {
        $stats = $this->loadStats();
        
        // Calculate today's stats
        $todayVisits = $this->getTodayVisits();
        $todayUnique = $this->getTodayUniqueVisitors();
        
        return [
            'total_site_visits' => $stats['total_visits'] ?? 0,
            'unique_site_visitors' => $stats['unique_visitors'] ?? 0,
            'today_total_visits' => $todayVisits,
            'today_unique_visitors' => $todayUnique,
            'total_pages_visited' => count($stats['pages'] ?? [])
        ];
    }
    
    /**
     * Get formatted visitor count display
     */
    public function getVisitorCountDisplay($pageSlug, $options = []) {
        $pageStats = $this->getPageStats($pageSlug);
        $siteStats = $this->getSiteStats();
        
        $showPageCount = $options['show_page_count'] ?? true;
        $showSiteCount = $options['show_site_count'] ?? true;
        $showTodayCount = $options['show_today_count'] ?? false;
        $style = $options['style'] ?? 'retro';
        
        $html = '<div class="visitor-counter visitor-counter-' . $style . '">';
        
        if ($showSiteCount) {
            $html .= '<div class="counter-row">';
            $html .= '<span class="counter-label">Total Site Visitors:</span>';
            $html .= '<span class="counter-number">' . number_format($siteStats['total_site_visits']) . '</span>';
            $html .= '</div>';
        }
        
        if ($showPageCount) {
            $html .= '<div class="counter-row">';
            $html .= '<span class="counter-label">Page Visitors:</span>';
            $html .= '<span class="counter-number">' . number_format($pageStats['total_visits']) . '</span>';
            $html .= '</div>';
        }
        
        if ($showTodayCount) {
            $html .= '<div class="counter-row">';
            $html .= '<span class="counter-label">Today\'s Visitors:</span>';
            $html .= '<span class="counter-number">' . number_format($siteStats['today_total_visits']) . '</span>';
            $html .= '</div>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Check if this is a duplicate visit
     */
    private function isDuplicateVisit($pageSlug, $ipAddress, $userAgentHash, $visitDate) {
        $visits = $this->loadVisits();
        
        foreach ($visits as $visit) {
            if ($visit['page_slug'] === $pageSlug &&
                $visit['ip_address'] === $ipAddress &&
                $visit['user_agent_hash'] === $userAgentHash &&
                $visit['visit_date'] === $visitDate) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Add a new visit record
     */
    private function addVisit($pageSlug, $pageTitle, $ipAddress, $userAgentHash, $visitDate, $visitTime) {
        $visits = $this->loadVisits();
        
        $visits[] = [
            'page_slug' => $pageSlug,
            'page_title' => $pageTitle,
            'ip_address' => $ipAddress,
            'user_agent_hash' => $userAgentHash,
            'visit_date' => $visitDate,
            'visit_time' => $visitTime
        ];
        
        // Keep only last 10000 visits to prevent file from growing too large
        if (count($visits) > 10000) {
            $visits = array_slice($visits, -10000);
        }
        
        file_put_contents($this->visitsFile, json_encode($visits));
    }
    
    /**
     * Update statistics
     */
    private function updateStats($pageSlug, $pageTitle) {
        $stats = $this->loadStats();
        $visits = $this->loadVisits();
        
        // Update total site visits
        $stats['total_visits'] = count($visits);
        
        // Calculate unique visitors
        $uniqueVisitors = [];
        foreach ($visits as $visit) {
            $uniqueVisitors[$visit['ip_address'] . $visit['user_agent_hash']] = true;
        }
        $stats['unique_visitors'] = count($uniqueVisitors);
        
        // Update page-specific stats
        if (!isset($stats['pages'][$pageSlug])) {
            $stats['pages'][$pageSlug] = [
                'page_slug' => $pageSlug,
                'page_title' => $pageTitle,
                'total_visits' => 0,
                'unique_visits' => 0,
                'today_visits' => 0,
                'today_unique_visits' => 0,
                'first_visit' => null,
                'last_visit' => null
            ];
        }
        
        // Calculate page stats
        $pageVisits = array_filter($visits, function($visit) use ($pageSlug) {
            return $visit['page_slug'] === $pageSlug;
        });
        
        $stats['pages'][$pageSlug]['total_visits'] = count($pageVisits);
        
        // Calculate unique page visits
        $uniquePageVisitors = [];
        foreach ($pageVisits as $visit) {
            $uniquePageVisitors[$visit['ip_address'] . $visit['user_agent_hash']] = true;
        }
        $stats['pages'][$pageSlug]['unique_visits'] = count($uniquePageVisitors);
        
        // Calculate today's page visits
        $today = date('Y-m-d');
        $todayPageVisits = array_filter($pageVisits, function($visit) use ($today) {
            return $visit['visit_date'] === $today;
        });
        $stats['pages'][$pageSlug]['today_visits'] = count($todayPageVisits);
        
        // Calculate today's unique page visits
        $todayUniquePageVisitors = [];
        foreach ($todayPageVisits as $visit) {
            $todayUniquePageVisitors[$visit['ip_address'] . $visit['user_agent_hash']] = true;
        }
        $stats['pages'][$pageSlug]['today_unique_visits'] = count($todayUniquePageVisitors);
        
        // Update first and last visit times
        if (!empty($pageVisits)) {
            $firstVisit = min(array_column($pageVisits, 'visit_time'));
            $lastVisit = max(array_column($pageVisits, 'visit_time'));
            $stats['pages'][$pageSlug]['first_visit'] = date('Y-m-d H:i:s', $firstVisit);
            $stats['pages'][$pageSlug]['last_visit'] = date('Y-m-d H:i:s', $lastVisit);
        }
        
        file_put_contents($this->statsFile, json_encode($stats));
    }
    
    /**
     * Get today's total visits
     */
    private function getTodayVisits() {
        $visits = $this->loadVisits();
        $today = date('Y-m-d');
        
        return count(array_filter($visits, function($visit) use ($today) {
            return $visit['visit_date'] === $today;
        }));
    }
    
    /**
     * Get today's unique visitors
     */
    private function getTodayUniqueVisitors() {
        $visits = $this->loadVisits();
        $today = date('Y-m-d');
        
        $todayVisits = array_filter($visits, function($visit) use ($today) {
            return $visit['visit_date'] === $today;
        });
        
        $uniqueVisitors = [];
        foreach ($todayVisits as $visit) {
            $uniqueVisitors[$visit['ip_address'] . $visit['user_agent_hash']] = true;
        }
        
        return count($uniqueVisitors);
    }
    
    /**
     * Load visits from file
     */
    private function loadVisits() {
        $content = file_get_contents($this->visitsFile);
        return json_decode($content, true) ?: [];
    }
    
    /**
     * Load stats from file
     */
    private function loadStats() {
        $content = file_get_contents($this->statsFile);
        return json_decode($content, true) ?: [];
    }
    
    /**
     * Get client IP address
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}
?>
